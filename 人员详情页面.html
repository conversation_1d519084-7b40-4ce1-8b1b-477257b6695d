﻿<!DOCTYPE html>
<html>
  <head>
    <title>人员详情页面</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/人员详情页面/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/人员详情页面/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u151" class="ax_default box_1 transition notrs">
        <div id="u151_div" class=""></div>
        <div id="u151_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u152" class="ax_default label transition notrs">
        <div id="u152_div" class=""></div>
        <div id="u152_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u153" class="ax_default label transition notrs">
        <div id="u153_div" class=""></div>
        <div id="u153_text" class="text ">
          <p><span>人员详情页面</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u154" class="ax_default _图片 transition notrs">
        <img id="u154_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u154_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u155" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u155_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u155_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u156" class="ax_default box_1 transition notrs">
              <div id="u156_div" class=""></div>
              <div id="u156_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u157" class="ax_default label transition notrs">
              <div id="u157_div" class=""></div>
              <div id="u157_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u158" class="ax_default label transition notrs">
              <div id="u158_div" class=""></div>
              <div id="u158_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u159" class="ax_default button transition notrs">
              <div id="u159_div" class=""></div>
              <div id="u159_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u160" class="ax_default primary_button transition notrs">
              <div id="u160_div" class=""></div>
              <div id="u160_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u155_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u155_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u161" class="ax_default box_1 transition notrs">
              <div id="u161_div" class=""></div>
              <div id="u161_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u162" class="ax_default label transition notrs">
              <div id="u162_div" class=""></div>
              <div id="u162_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u163" class="ax_default label transition notrs">
              <div id="u163_div" class=""></div>
              <div id="u163_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u164" class="ax_default button transition notrs">
              <div id="u164_div" class=""></div>
              <div id="u164_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u165" class="ax_default primary_button transition notrs">
              <div id="u165_div" class=""></div>
              <div id="u165_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u166" class="ax_default label transition notrs">
              <div id="u166_div" class=""></div>
              <div id="u166_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u167" class="ax_default text_field transition notrs">
              <div id="u167_div" class=""></div>
              <input id="u167_input" type="text" value="" class="u167_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u168" class="ax_default label transition notrs">
              <div id="u168_div" class=""></div>
              <div id="u168_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u169" class="ax_default label transition notrs">
        <div id="u169_div" class=""></div>
        <div id="u169_text" class="text ">
          <p><span>张伟 |</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u170" class="ax_default label transition notrs">
        <div id="u170_div" class=""></div>
        <div id="u170_text" class="text ">
          <p><span>已实名认证</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u171" class="ax_default label transition notrs">
        <div id="u171_div" class=""></div>
        <div id="u171_text" class="text ">
          <p><span>用户评价：</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u172" class="ax_default" data-left="8.000696313122466" data-top="576" data-width="456.998607373755" data-height="124.2537850601509" layer-opacity="1">

        <!-- Unnamed (图片) -->
        <div id="u173" class="ax_default _图片 transition notrs">
          <img id="u173_img" class="img " src="images/人员详情页面/u173.png"/>
          <div id="u173_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u174" class="ax_default label transition notrs">
          <div id="u174_div" class=""></div>
          <div id="u174_text" class="text ">
            <p><span>昵称11</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u175" class="ax_default label transition notrs">
          <div id="u175_div" class=""></div>
          <div id="u175_text" class="text ">
            <p><span>除了以上内容，我还可以辅导会计的考研、保研等除了以上内容，我</span></p><p><span>还可以辅导会计的考研、保研等除了以上内容，我还可以辅导会计的考</span></p><p><span>研、保研等</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u176" class="ax_default line transition notrs">
          <svg data="images/人员详情页面/u176.svg" id="u176_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -8 -698 )">
    <path d="M 0 0.5  L 457 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(242, 242, 242, 1)" fill="none" transform="matrix(1 0 0 1 8 698 )" class="stroke" />
  </g>
          </svg>
          <div id="u176_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u177" class="ax_default label transition notrs">
          <div id="u177_div" class=""></div>
          <div id="u177_text" class="text ">
            <p><span>论文辅导 | 4.5分</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u178" class="ax_default label transition notrs">
          <div id="u178_div" class=""></div>
          <div id="u178_text" class="text ">
            <p><span>2025-3-12</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u179" class="ax_default" data-left="8.00069631312247" data-top="710" data-width="456.998607373755" data-height="124.25378506015102" layer-opacity="1">

        <!-- Unnamed (图片) -->
        <div id="u180" class="ax_default _图片 transition notrs">
          <img id="u180_img" class="img " src="images/人员详情页面/u173.png"/>
          <div id="u180_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u181" class="ax_default label transition notrs">
          <div id="u181_div" class=""></div>
          <div id="u181_text" class="text ">
            <p><span>昵称11</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u182" class="ax_default label transition notrs">
          <div id="u182_div" class=""></div>
          <div id="u182_text" class="text ">
            <p><span>除了以上内容，我还可以辅导会计的考研、保研等除了以上内容，我</span></p><p><span>还可以辅导会计的考研、保研等除了以上内容，我还可以辅导会计的考</span></p><p><span>研、保研等</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u183" class="ax_default line transition notrs">
          <svg data="images/人员详情页面/u176.svg" id="u183_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -8 -698 )">
    <path d="M 0 0.5  L 457 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(242, 242, 242, 1)" fill="none" transform="matrix(1 0 0 1 8 698 )" class="stroke" />
  </g>
          </svg>
          <div id="u183_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u184" class="ax_default label transition notrs">
          <div id="u184_div" class=""></div>
          <div id="u184_text" class="text ">
            <p><span>论文辅导 | 4.5分</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u185" class="ax_default label transition notrs">
          <div id="u185_div" class=""></div>
          <div id="u185_text" class="text ">
            <p><span>2025-3-12</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u186" class="ax_default sticky_1 transition notrs">
        <div id="u186_div" class=""></div>
        <div id="u186_text" class="text ">
          <p><span>用户评价按照时间倒序</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u187" class="ax_default label transition notrs">
        <div id="u187_div" class=""></div>
        <div id="u187_text" class="text ">
          <p><span>学员评分：5.0分</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u188" class="ax_default label transition notrs">
        <div id="u188_div" class=""></div>
        <div id="u188_text" class="text ">
          <p><span>辅导人数：12人</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u189" class="ax_default _图片 transition notrs">
        <img id="u189_img" class="img " src="images/首页（学生端）/u21.png"/>
        <div id="u189_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u190" class="ax_default label transition notrs">
        <div id="u190_div" class=""></div>
        <div id="u190_text" class="text ">
          <p><span>清华大学 | 博士 | 环境会计</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u191" class="ax_default" data-left="36" data-top="1056" data-width="418" data-height="40" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u192" class="ax_default primary_button transition notrs">
          <div id="u192_div" class=""></div>
          <div id="u192_text" class="text ">
            <p><span>复制微信</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u193" class="ax_default sticky_1 transition notrs">
        <div id="u193_div" class=""></div>
        <div id="u193_text" class="text ">
          <p><span>1、如果没有项目，则不显示操作按钮</span></p><p><span>2、如果老师没投递，只显示复制微信</span></p><p><span>3、如果老师投递，显示复制微信、确认辅导老师两个操作按钮</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u194" class="ax_default sticky_4 transition notrs">
        <div id="u194_div" class=""></div>
        <div id="u194_text" class="text ">
          <p><span>可以电话、微信、在线沟通</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u195" class="ax_default" data-left="32" data-top="210" data-width="422" data-height="73" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u196" class="ax_default" data-left="35" data-top="210" data-width="419" data-height="73" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u197" class="ax_default label transition notrs">
            <div id="u197_div" class=""></div>
            <div id="u197_text" class="text ">
              <p><span>我是来自清华大学的张飞，在读博士，已发表2篇SCI。我是来自清华大学</span></p><p><span>的张飞，在读博士，已发表2篇SCI……</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u198" class="ax_default label transition notrs">
            <div id="u198_div" class=""></div>
            <div id="u198_text" class="text ">
              <p><span>自述</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (垂直线) -->
        <div id="u199" class="ax_default line transition notrs">
          <svg data="images/人员详情页面/u199.svg" id="u199_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -31 -213 )">
    <path d="M 1 0  L 1 20  " stroke-width="2" stroke-dasharray="0" stroke="rgba(2, 167, 240, 1)" fill="none" transform="matrix(1 0 0 1 32 214 )" class="stroke" />
  </g>
          </svg>
          <div id="u199_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u200" class="ax_default" data-left="31" data-top="307" data-width="423" data-height="73" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u201" class="ax_default" data-left="35" data-top="307" data-width="419" data-height="73" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u202" class="ax_default label transition notrs">
            <div id="u202_div" class=""></div>
            <div id="u202_text" class="text ">
              <p><span>我是来自清华大学的张飞，在读博士，已发表2篇SCI。我是来自清华大学</span></p><p><span>的张飞，在读博士，已发表2篇SCI……</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u203" class="ax_default label transition notrs">
            <div id="u203_div" class=""></div>
            <div id="u203_text" class="text ">
              <p><span>科研经历</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (垂直线) -->
        <div id="u204" class="ax_default line transition notrs">
          <svg data="images/人员详情页面/u199.svg" id="u204_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -31 -213 )">
    <path d="M 1 0  L 1 20  " stroke-width="2" stroke-dasharray="0" stroke="rgba(2, 167, 240, 1)" fill="none" transform="matrix(1 0 0 1 32 214 )" class="stroke" />
  </g>
          </svg>
          <div id="u204_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u205" class="ax_default" data-left="35" data-top="392" data-width="344" data-height="68" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u206" class="ax_default label transition notrs">
          <div id="u206_div" class=""></div>
          <div id="u206_text" class="text ">
            <p><span>擅长辅导</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u207" class="ax_default label transition notrs">
          <div id="u207_div" class=""></div>
          <div id="u207_text" class="text ">
            <p><span>国内论文</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u208" class="ax_default label transition notrs">
          <div id="u208_div" class=""></div>
          <div id="u208_text" class="text ">
            <p><span>专业课</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u209" class="ax_default label transition notrs">
          <div id="u209_div" class=""></div>
          <div id="u209_text" class="text ">
            <p><span>留学申请</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u210" class="ax_default label transition notrs">
          <div id="u210_div" class=""></div>
          <div id="u210_text" class="text ">
            <p><span>申博</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (垂直线) -->
      <div id="u211" class="ax_default line transition notrs">
        <svg data="images/人员详情页面/u199.svg" id="u211_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -31 -213 )">
    <path d="M 1 0  L 1 20  " stroke-width="2" stroke-dasharray="0" stroke="rgba(2, 167, 240, 1)" fill="none" transform="matrix(1 0 0 1 32 214 )" class="stroke" />
  </g>
        </svg>
        <div id="u211_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u212" class="ax_default line transition notrs">
        <svg data="images/人员详情页面/u212.svg" id="u212_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -20 -520 )">
    <path d="M 0 0.5  L 451 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(215, 215, 215, 1)" fill="none" transform="matrix(1 0 0 1 20 520 )" class="stroke" />
  </g>
        </svg>
        <div id="u212_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u213" class="ax_default sticky_1 transition notrs">
        <div id="u213_div" class=""></div>
        <div id="u213_text" class="text ">
          <p><span>复制微信后需要toast提示：已复制成功，可以添加微信，备注：来自辅导君。</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
