﻿<!DOCTYPE html>
<html>
  <head>
    <title>人员详情页面</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/人员详情页面_1/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/人员详情页面_1/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u436" class="ax_default box_1 transition notrs">
        <div id="u436_div" class=""></div>
        <div id="u436_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u437" class="ax_default label transition notrs">
        <div id="u437_div" class=""></div>
        <div id="u437_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u438" class="ax_default label transition notrs">
        <div id="u438_div" class=""></div>
        <div id="u438_text" class="text ">
          <p><span>人员详情页面</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u439" class="ax_default _图片 transition notrs">
        <img id="u439_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u439_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u440" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u440_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u440_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u441" class="ax_default box_1 transition notrs">
              <div id="u441_div" class=""></div>
              <div id="u441_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u442" class="ax_default label transition notrs">
              <div id="u442_div" class=""></div>
              <div id="u442_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u443" class="ax_default label transition notrs">
              <div id="u443_div" class=""></div>
              <div id="u443_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u444" class="ax_default button transition notrs">
              <div id="u444_div" class=""></div>
              <div id="u444_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u445" class="ax_default primary_button transition notrs">
              <div id="u445_div" class=""></div>
              <div id="u445_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u440_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u440_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u446" class="ax_default box_1 transition notrs">
              <div id="u446_div" class=""></div>
              <div id="u446_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u447" class="ax_default label transition notrs">
              <div id="u447_div" class=""></div>
              <div id="u447_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u448" class="ax_default label transition notrs">
              <div id="u448_div" class=""></div>
              <div id="u448_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u449" class="ax_default button transition notrs">
              <div id="u449_div" class=""></div>
              <div id="u449_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u450" class="ax_default primary_button transition notrs">
              <div id="u450_div" class=""></div>
              <div id="u450_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u451" class="ax_default label transition notrs">
              <div id="u451_div" class=""></div>
              <div id="u451_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u452" class="ax_default text_field transition notrs">
              <div id="u452_div" class=""></div>
              <input id="u452_input" type="text" value="" class="u452_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u453" class="ax_default label transition notrs">
              <div id="u453_div" class=""></div>
              <div id="u453_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u454" class="ax_default label transition notrs">
        <div id="u454_div" class=""></div>
        <div id="u454_text" class="text ">
          <p><span>张伟 | 博士</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u455" class="ax_default label transition notrs">
        <div id="u455_div" class=""></div>
        <div id="u455_text" class="text ">
          <p><span>已实名认证</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u456" class="ax_default label transition notrs">
        <div id="u456_div" class=""></div>
        <div id="u456_text" class="text ">
          <p><span>我是来自清华大学的张飞，在读博士，已发表2篇SCI。我是来自清华大学</span></p><p><span>的张飞，在读博士，已发表2篇SCI……</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u457" class="ax_default sticky_1 transition notrs">
        <div id="u457_div" class=""></div>
        <div id="u457_text" class="text ">
          <p><span>可以电话、微信、在线沟通</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u458" class="ax_default label transition notrs">
        <div id="u458_div" class=""></div>
        <div id="u458_text" class="text ">
          <p><span>自述</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u459" class="ax_default label transition notrs">
        <div id="u459_div" class=""></div>
        <div id="u459_text" class="text ">
          <p><span>擅长辅导</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u460" class="ax_default label transition notrs">
        <div id="u460_div" class=""></div>
        <div id="u460_text" class="text ">
          <p><span>其他说明：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u461" class="ax_default label transition notrs">
        <div id="u461_div" class=""></div>
        <div id="u461_text" class="text ">
          <p><span>除了以上内容，我还可以辅导会计的考研、保研等</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u462" class="ax_default label transition notrs">
        <div id="u462_div" class=""></div>
        <div id="u462_text" class="text ">
          <p><span>用户评价：</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u463" class="ax_default" data-left="18.00069631312249" data-top="516" data-width="456.998607373755" data-height="124.25378506015113" layer-opacity="1">

        <!-- Unnamed (图片) -->
        <div id="u464" class="ax_default _图片 transition notrs">
          <img id="u464_img" class="img " src="images/人员详情页面/u173.png"/>
          <div id="u464_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u465" class="ax_default label transition notrs">
          <div id="u465_div" class=""></div>
          <div id="u465_text" class="text ">
            <p><span>昵称11</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u466" class="ax_default label transition notrs">
          <div id="u466_div" class=""></div>
          <div id="u466_text" class="text ">
            <p><span>除了以上内容，我还可以辅导会计的考研、保研等除了以上内容，我</span></p><p><span>还可以辅导会计的考研、保研等除了以上内容，我还可以辅导会计的考</span></p><p><span>研、保研等</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u467" class="ax_default line transition notrs">
          <svg data="images/人员详情页面/u176.svg" id="u467_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -8 -698 )">
    <path d="M 0 0.5  L 457 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(242, 242, 242, 1)" fill="none" transform="matrix(1 0 0 1 8 698 )" class="stroke" />
  </g>
          </svg>
          <div id="u467_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u468" class="ax_default label transition notrs">
          <div id="u468_div" class=""></div>
          <div id="u468_text" class="text ">
            <p><span>论文辅导 | 4.5分</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u469" class="ax_default label transition notrs">
          <div id="u469_div" class=""></div>
          <div id="u469_text" class="text ">
            <p><span>2025-3-12</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u470" class="ax_default sticky_1 transition notrs">
        <div id="u470_div" class=""></div>
        <div id="u470_text" class="text ">
          <p><span>用户评价按照时间倒序</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u471" class="ax_default label transition notrs">
        <div id="u471_div" class=""></div>
        <div id="u471_text" class="text ">
          <p><span>学员评分：5.0分</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u472" class="ax_default label transition notrs">
        <div id="u472_div" class=""></div>
        <div id="u472_text" class="text ">
          <p><span>辅导人数：20人</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u473" class="ax_default label transition notrs">
        <div id="u473_div" class=""></div>
        <div id="u473_text" class="text ">
          <p><span>响应度：高</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u474" class="ax_default" data-left="18.00069631312246" data-top="650" data-width="456.998607373755" data-height="124.25378506015113" layer-opacity="1">

        <!-- Unnamed (图片) -->
        <div id="u475" class="ax_default _图片 transition notrs">
          <img id="u475_img" class="img " src="images/人员详情页面/u173.png"/>
          <div id="u475_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u476" class="ax_default label transition notrs">
          <div id="u476_div" class=""></div>
          <div id="u476_text" class="text ">
            <p><span>昵称11</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u477" class="ax_default label transition notrs">
          <div id="u477_div" class=""></div>
          <div id="u477_text" class="text ">
            <p><span>除了以上内容，我还可以辅导会计的考研、保研等除了以上内容，我</span></p><p><span>还可以辅导会计的考研、保研等除了以上内容，我还可以辅导会计的考</span></p><p><span>研、保研等</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u478" class="ax_default line transition notrs">
          <svg data="images/人员详情页面/u176.svg" id="u478_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -8 -698 )">
    <path d="M 0 0.5  L 457 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(242, 242, 242, 1)" fill="none" transform="matrix(1 0 0 1 8 698 )" class="stroke" />
  </g>
          </svg>
          <div id="u478_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u479" class="ax_default label transition notrs">
          <div id="u479_div" class=""></div>
          <div id="u479_text" class="text ">
            <p><span>论文辅导 | 4.5分</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u480" class="ax_default label transition notrs">
          <div id="u480_div" class=""></div>
          <div id="u480_text" class="text ">
            <p><span>2025-3-12</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u481" class="ax_default _图片 transition notrs">
        <img id="u481_img" class="img " src="images/首页（学生端）/u21.png"/>
        <div id="u481_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u482" class="ax_default label transition notrs">
        <div id="u482_div" class=""></div>
        <div id="u482_text" class="text ">
          <p><span>清华大学 | 博士 | 环境会计</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u483" class="ax_default label transition notrs">
        <div id="u483_div" class=""></div>
        <div id="u483_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">已辅导：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">12</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u484" class="ax_default" data-left="55" data-top="1056" data-width="405" data-height="40" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u485" class="ax_default primary_button transition notrs">
          <div id="u485_div" class=""></div>
          <div id="u485_text" class="text ">
            <p><span>复制微信</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u486" class="ax_default primary_button transition notrs">
          <div id="u486_div" class=""></div>
          <div id="u486_text" class="text ">
            <p><span>确认老师</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u487" class="ax_default sticky_1 transition notrs">
        <div id="u487_div" class=""></div>
        <div id="u487_text" class="text ">
          <p><span>复制微信后需要toast提示：已复制成功，可以添加微信，备注：来自辅导君。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u488" class="ax_default label transition notrs">
        <div id="u488_div" class=""></div>
        <div id="u488_text" class="text ">
          <p><span>国内论文</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u489" class="ax_default label transition notrs">
        <div id="u489_div" class=""></div>
        <div id="u489_text" class="text ">
          <p><span>专业课</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u490" class="ax_default label transition notrs">
        <div id="u490_div" class=""></div>
        <div id="u490_text" class="text ">
          <p><span>留学申请</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u491" class="ax_default label transition notrs">
        <div id="u491_div" class=""></div>
        <div id="u491_text" class="text ">
          <p><span>申博</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
