﻿<!DOCTYPE html>
<html>
  <head>
    <title>冻结订单</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/冻结订单/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/冻结订单/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u2089" class="ax_default box_1 transition notrs">
        <div id="u2089_div" class=""></div>
        <div id="u2089_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2090" class="ax_default" data-left="16" data-top="85" data-width="436" data-height="137" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2091" class="ax_default box_1 transition notrs">
          <div id="u2091_div" class=""></div>
          <div id="u2091_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2092" class="ax_default label transition notrs">
          <div id="u2092_div" class=""></div>
          <div id="u2092_text" class="text ">
            <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">&nbsp; 订单号：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">JS13455555333&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2093" class="ax_default" data-left="28" data-top="124" data-width="107" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2094" class="ax_default label transition notrs">
            <div id="u2094_div" class=""></div>
            <div id="u2094_text" class="text ">
              <p><span>辅导项目：</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u2095" class="ax_default shape transition notrs">
            <div id="u2095_div" class=""></div>
            <div id="u2095_text" class="text ">
              <p><span>作业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2096" class="ax_default" data-left="343" data-top="124" data-width="97" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2097" class="ax_default label transition notrs">
            <div id="u2097_div" class=""></div>
            <div id="u2097_text" class="text ">
              <p><span>总金额：2000元</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2098" class="ax_default" data-left="28" data-top="161" data-width="105" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2099" class="ax_default label transition notrs">
            <div id="u2099_div" class=""></div>
            <div id="u2099_text" class="text ">
              <p><span>交付阶段：已完成</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2100" class="ax_default" data-left="392" data-top="194" data-width="49" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2101" class="ax_default label transition notrs">
            <div id="u2101_div" class=""></div>
            <div id="u2101_text" class="text ">
              <p><span>4.5分</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2102" class="ax_default" data-left="361" data-top="159" data-width="79" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2103" class="ax_default label transition notrs">
            <div id="u2103_div" class=""></div>
            <div id="u2103_text" class="text ">
              <p><span>学生：张菲菲</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2104" class="ax_default" data-left="28" data-top="194" data-width="178" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2105" class="ax_default label transition notrs">
            <div id="u2105_div" class=""></div>
            <div id="u2105_text" class="text ">
              <p><span>完成时间：2025-4-12 12:11:11</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2106" class="ax_default" data-left="294" data-top="124" data-width="49" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2107" class="ax_default label transition notrs">
            <div id="u2107_div" class=""></div>
            <div id="u2107_text" class="text ">
              <p><span>冻结中</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2108" class="ax_default label transition notrs">
        <div id="u2108_div" class=""></div>
        <div id="u2108_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2109" class="ax_default label transition notrs">
        <div id="u2109_div" class=""></div>
        <div id="u2109_text" class="text ">
          <p><span>我的订单</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2110" class="ax_default _图片 transition notrs">
        <img id="u2110_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u2110_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u2111" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u2111_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u2111_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2112" class="ax_default box_1 transition notrs">
              <div id="u2112_div" class=""></div>
              <div id="u2112_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2113" class="ax_default label transition notrs">
              <div id="u2113_div" class=""></div>
              <div id="u2113_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2114" class="ax_default label transition notrs">
              <div id="u2114_div" class=""></div>
              <div id="u2114_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2115" class="ax_default button transition notrs">
              <div id="u2115_div" class=""></div>
              <div id="u2115_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2116" class="ax_default primary_button transition notrs">
              <div id="u2116_div" class=""></div>
              <div id="u2116_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u2111_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u2111_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2117" class="ax_default box_1 transition notrs">
              <div id="u2117_div" class=""></div>
              <div id="u2117_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2118" class="ax_default label transition notrs">
              <div id="u2118_div" class=""></div>
              <div id="u2118_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2119" class="ax_default label transition notrs">
              <div id="u2119_div" class=""></div>
              <div id="u2119_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2120" class="ax_default button transition notrs">
              <div id="u2120_div" class=""></div>
              <div id="u2120_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2121" class="ax_default primary_button transition notrs">
              <div id="u2121_div" class=""></div>
              <div id="u2121_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2122" class="ax_default label transition notrs">
              <div id="u2122_div" class=""></div>
              <div id="u2122_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2123" class="ax_default text_field transition notrs">
              <div id="u2123_div" class=""></div>
              <input id="u2123_input" type="text" value="" class="u2123_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2124" class="ax_default label transition notrs">
              <div id="u2124_div" class=""></div>
              <div id="u2124_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2125" class="ax_default" data-left="16" data-top="233" data-width="436" data-height="137" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2126" class="ax_default box_1 transition notrs">
          <div id="u2126_div" class=""></div>
          <div id="u2126_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2127" class="ax_default label transition notrs">
          <div id="u2127_div" class=""></div>
          <div id="u2127_text" class="text ">
            <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">&nbsp; 订单号：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">JS13455555333&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2128" class="ax_default" data-left="28" data-top="272" data-width="107" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2129" class="ax_default label transition notrs">
            <div id="u2129_div" class=""></div>
            <div id="u2129_text" class="text ">
              <p><span>辅导项目：</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u2130" class="ax_default shape transition notrs">
            <div id="u2130_div" class=""></div>
            <div id="u2130_text" class="text ">
              <p><span>论文</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2131" class="ax_default" data-left="343" data-top="272" data-width="97" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2132" class="ax_default label transition notrs">
            <div id="u2132_div" class=""></div>
            <div id="u2132_text" class="text ">
              <p><span>总金额：3000元</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2133" class="ax_default" data-left="28" data-top="309" data-width="105" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2134" class="ax_default label transition notrs">
            <div id="u2134_div" class=""></div>
            <div id="u2134_text" class="text ">
              <p><span>交付阶段：已完成</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2135" class="ax_default" data-left="392" data-top="342" data-width="49" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2136" class="ax_default label transition notrs">
            <div id="u2136_div" class=""></div>
            <div id="u2136_text" class="text ">
              <p><span>4.5分</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2137" class="ax_default" data-left="361" data-top="307" data-width="79" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2138" class="ax_default label transition notrs">
            <div id="u2138_div" class=""></div>
            <div id="u2138_text" class="text ">
              <p><span>学生：张菲菲</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2139" class="ax_default" data-left="28" data-top="342" data-width="178" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2140" class="ax_default label transition notrs">
            <div id="u2140_div" class=""></div>
            <div id="u2140_text" class="text ">
              <p><span>完成时间：2025-4-12 12:11:11</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2141" class="ax_default" data-left="294" data-top="272" data-width="49" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2142" class="ax_default label transition notrs">
            <div id="u2142_div" class=""></div>
            <div id="u2142_text" class="text ">
              <p><span>冻结中</span></p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
