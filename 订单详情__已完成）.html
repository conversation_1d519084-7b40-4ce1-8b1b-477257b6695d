﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情 (已完成）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情__已完成）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情__已完成）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1811" class="ax_default box_1 transition notrs">
        <div id="u1811_div" class=""></div>
        <div id="u1811_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1812" class="ax_default label transition notrs">
        <div id="u1812_div" class=""></div>
        <div id="u1812_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1813" class="ax_default label transition notrs">
        <div id="u1813_div" class=""></div>
        <div id="u1813_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1814" class="ax_default _图片 transition notrs">
        <img id="u1814_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1814_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1815" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1815_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1815_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1816" class="ax_default box_1 transition notrs">
              <div id="u1816_div" class=""></div>
              <div id="u1816_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1817" class="ax_default label transition notrs">
              <div id="u1817_div" class=""></div>
              <div id="u1817_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1818" class="ax_default label transition notrs">
              <div id="u1818_div" class=""></div>
              <div id="u1818_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1819" class="ax_default button transition notrs">
              <div id="u1819_div" class=""></div>
              <div id="u1819_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1820" class="ax_default primary_button transition notrs">
              <div id="u1820_div" class=""></div>
              <div id="u1820_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1815_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1815_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1821" class="ax_default box_1 transition notrs">
              <div id="u1821_div" class=""></div>
              <div id="u1821_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1822" class="ax_default label transition notrs">
              <div id="u1822_div" class=""></div>
              <div id="u1822_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1823" class="ax_default label transition notrs">
              <div id="u1823_div" class=""></div>
              <div id="u1823_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1824" class="ax_default button transition notrs">
              <div id="u1824_div" class=""></div>
              <div id="u1824_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1825" class="ax_default primary_button transition notrs">
              <div id="u1825_div" class=""></div>
              <div id="u1825_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1826" class="ax_default label transition notrs">
              <div id="u1826_div" class=""></div>
              <div id="u1826_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1827" class="ax_default text_field transition notrs">
              <div id="u1827_div" class=""></div>
              <input id="u1827_input" type="text" value="" class="u1827_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1828" class="ax_default label transition notrs">
              <div id="u1828_div" class=""></div>
              <div id="u1828_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1829" class="ax_default" data-left="19.999483828313867" data-top="244" data-width="453.0010323433722" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1830" class="ax_default" data-left="38" data-top="246" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1831" class="ax_default label transition notrs">
            <div id="u1831_div" class=""></div>
            <div id="u1831_text" class="text ">
              <p><span>辅导项目</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1832" class="ax_default" data-left="435" data-top="244" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1833" class="ax_default label transition notrs">
            <div id="u1833_div" class=""></div>
            <div id="u1833_text" class="text ">
              <p><span>作业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1834" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1834_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1834_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1835" class="ax_default" data-left="16.999483828313878" data-top="126" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1836" class="ax_default" data-left="35" data-top="126" data-width="40" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1837" class="ax_default label transition notrs">
            <div id="u1837_div" class=""></div>
            <div id="u1837_text" class="text ">
              <p><span>订单号</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1838" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1838_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1838_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1839" class="ax_default" data-left="363" data-top="126" data-width="96" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1840" class="ax_default label transition notrs">
            <div id="u1840_div" class=""></div>
            <div id="u1840_text" class="text ">
              <p><span>JS13455555333</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1841" class="ax_default" data-left="16.999483828313867" data-top="168" data-width="453.00103234337223" data-height="29.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1842" class="ax_default" data-left="35" data-top="169" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1843" class="ax_default label transition notrs">
            <div id="u1843_div" class=""></div>
            <div id="u1843_text" class="text ">
              <p><span>发布时间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1844" class="ax_default" data-left="358" data-top="168" data-width="112" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1845" class="ax_default label transition notrs">
            <div id="u1845_div" class=""></div>
            <div id="u1845_text" class="text ">
              <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp;2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1846" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1846_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1846_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1847" class="ax_default" data-left="19.99948382831389" data-top="286" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1848" class="ax_default" data-left="38" data-top="288" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1849" class="ax_default label transition notrs">
            <div id="u1849_div" class=""></div>
            <div id="u1849_text" class="text ">
              <p><span>学习阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1850" class="ax_default" data-left="435" data-top="286" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1851" class="ax_default label transition notrs">
            <div id="u1851_div" class=""></div>
            <div id="u1851_text" class="text ">
              <p><span>本科</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1852" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1852_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1852_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1853" class="ax_default" data-left="19.999483828313863" data-top="321" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1854" class="ax_default" data-left="38" data-top="323" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1855" class="ax_default label transition notrs">
            <div id="u1855_div" class=""></div>
            <div id="u1855_text" class="text ">
              <p><span>学习位置</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1856" class="ax_default" data-left="435" data-top="321" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1857" class="ax_default label transition notrs">
            <div id="u1857_div" class=""></div>
            <div id="u1857_text" class="text ">
              <p><span>国内</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1858" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1858_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1858_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1859" class="ax_default" data-left="19.99948382831388" data-top="362" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1860" class="ax_default" data-left="38" data-top="364" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1861" class="ax_default label transition notrs">
            <div id="u1861_div" class=""></div>
            <div id="u1861_text" class="text ">
              <p><span>学习专业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1862" class="ax_default" data-left="435" data-top="362" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1863" class="ax_default label transition notrs">
            <div id="u1863_div" class=""></div>
            <div id="u1863_text" class="text ">
              <p><span>会计</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1864" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1864_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1864_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1865" class="ax_default" data-left="19.999483828313874" data-top="403" data-width="453.0010323433722" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1866" class="ax_default" data-left="38" data-top="405" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1867" class="ax_default label transition notrs">
            <div id="u1867_div" class=""></div>
            <div id="u1867_text" class="text ">
              <p><span>预算区间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1868" class="ax_default" data-left="382" data-top="403" data-width="80" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1869" class="ax_default label transition notrs">
            <div id="u1869_div" class=""></div>
            <div id="u1869_text" class="text ">
              <p><span>1000-2000元</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1870" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1870_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1870_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1871" class="ax_default" data-left="16.999483828313885" data-top="442" data-width="453.00103234337223" data-height="90" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1872" class="ax_default" data-left="35" data-top="442" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1873" class="ax_default label transition notrs">
            <div id="u1873_div" class=""></div>
            <div id="u1873_text" class="text ">
              <p><span>需求描述</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1874" class="ax_default label transition notrs">
          <div id="u1874_div" class=""></div>
          <div id="u1874_text" class="text ">
            <p><span>想要找一个辅导论文的老师，想要找一个辅导论文的老师想要找一个</span></p><p><span>辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想</span></p><p><span>要找一个辅导论文的老师</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1875" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1875_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1875_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1876" class="ax_default" data-left="16.999483828313867" data-top="558" data-width="453.0010323433722" data-height="165" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1877" class="ax_default label transition notrs">
          <div id="u1877_div" class=""></div>
          <div id="u1877_text" class="text ">
            <p><span>希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，</span></p><p><span>有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热</span></p><p><span>情专业，有耐心。</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1878" class="ax_default" data-left="16.999483828313867" data-top="558" data-width="453.0010323433722" data-height="28.627173050050146" layer-opacity="1">

          <!-- Unnamed (组合) -->
          <div id="u1879" class="ax_default" data-left="35" data-top="558" data-width="53" data-height="18" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1880" class="ax_default label transition notrs">
              <div id="u1880_div" class=""></div>
              <div id="u1880_text" class="text ">
                <p><span>老师要求</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1881" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
          </div>

          <!-- Unnamed (线段) -->
          <div id="u1882" class="ax_default line transition notrs">
            <svg data="images/订单详情__待接单）/u316.svg" id="u1882_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
            </svg>
            <div id="u1882_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1883" class="ax_default" data-left="35" data-top="600" data-width="121" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1884" class="ax_default label transition notrs">
            <div id="u1884_div" class=""></div>
            <div id="u1884_text" class="text ">
              <p><span>老师情况</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1885" class="ax_default shape transition notrs">
            <div id="u1885_div" class=""></div>
            <div id="u1885_text" class="text ">
              <p><span>博士</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1886" class="ax_default" data-left="35" data-top="632" data-width="132" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1887" class="ax_default label transition notrs">
            <div id="u1887_div" class=""></div>
            <div id="u1887_text" class="text ">
              <p><span>是否留学</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1888" class="ax_default box_1 transition notrs">
            <div id="u1888_div" class=""></div>
            <div id="u1888_text" class="text ">
              <p><span>不要求</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1889" class="ax_default label transition notrs">
        <div id="u1889_div" class=""></div>
        <div id="u1889_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1890" class="ax_default label transition notrs">
        <div id="u1890_div" class=""></div>
        <div id="u1890_text" class="text ">
          <p><span>交付信息</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1891" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u1891_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u1891_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1892" class="ax_default" data-left="18.999483828313878" data-top="205" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1893" class="ax_default" data-left="37" data-top="207" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1894" class="ax_default label transition notrs">
            <div id="u1894_div" class=""></div>
            <div id="u1894_text" class="text ">
              <p><span>学生名称</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1895" class="ax_default" data-left="422" data-top="205" data-width="40" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1896" class="ax_default label transition notrs">
            <div id="u1896_div" class=""></div>
            <div id="u1896_text" class="text ">
              <p><span>张菲菲</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1897" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1897_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1897_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1898" class="ax_default label transition notrs">
        <div id="u1898_div" class=""></div>
        <div id="u1898_text" class="text ">
          <p><span>评价信息</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
