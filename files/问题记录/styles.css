﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-99px;
  width:570px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2312 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:71px;
  width:570px;
  height:348px;
}
#u2313 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2313 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2313_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u2313_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2314 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:0px;
  width:232px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2314 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2314_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:232px;
  height:30px;
}
#u2314_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2315 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:0px;
  width:208px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2315 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2315_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:30px;
}
#u2315_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2316 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:130px;
  height:70px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2316 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2316_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:70px;
}
#u2316_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2317 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:30px;
  width:232px;
  height:70px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u2317 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2317_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:232px;
  height:70px;
}
#u2317_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2318 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:30px;
  width:208px;
  height:70px;
  display:flex;
  transition:none;
}
#u2318 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2318_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:70px;
}
#u2318_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2319 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:100px;
  width:130px;
  height:71px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2319 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2319_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:71px;
}
#u2319_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2320 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:100px;
  width:232px;
  height:71px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u2320 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2320_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:232px;
  height:71px;
}
#u2320_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2321 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:100px;
  width:208px;
  height:71px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2321 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2321_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:71px;
}
#u2321_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2322 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:171px;
  width:130px;
  height:59px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2322 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2322_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:59px;
}
#u2322_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2323 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:171px;
  width:232px;
  height:59px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u2323 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2323_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:232px;
  height:59px;
}
#u2323_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2324 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:171px;
  width:208px;
  height:59px;
  display:flex;
  transition:none;
}
#u2324 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2324_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:59px;
}
#u2324_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2325 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:230px;
  width:130px;
  height:59px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2325 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2325_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:59px;
}
#u2325_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2326 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:230px;
  width:232px;
  height:59px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u2326 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2326_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:232px;
  height:59px;
}
#u2326_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2327 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:230px;
  width:208px;
  height:59px;
  display:flex;
  transition:none;
}
#u2327 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2327_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:59px;
}
#u2327_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2328 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:289px;
  width:130px;
  height:59px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2328 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2328_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:59px;
}
#u2328_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2329 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:289px;
  width:232px;
  height:59px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u2329 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2329_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:232px;
  height:59px;
}
#u2329_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2330 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:289px;
  width:208px;
  height:59px;
  display:flex;
  transition:none;
}
#u2330 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2330_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:59px;
}
#u2330_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
