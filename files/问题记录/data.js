﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,j,k,_(l,m,n,o)),p,[],q,_(h,r),s,[t],u,_(v,w,x,y,g,z,A,_(),B,[],C,_(D,E,F,G,H,_(I,J,K,L),M,null,N,_(O,P,Q,P),R,S,T,null,U,V,W,X,Y,Z,ba,V,bb,bc,bd,_(I,J,K,be),bf,V,bg,bc,bh,_(bi,bj,bk,bl,bm,bl,bn,bl,bo,o,K,_(bp,bq,br,bq,bs,bq,bt,bu)),k,_(l,o,n,o),bv,bw),bx,_(),by,_(),bz,_(bA,[_(bB,bC,bD,h,bE,bF,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),k,_(l,m,n,bT),bU,_(bV,bW,bX,bY)),bx,_(),bZ,_(),bA,[_(bB,ca,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),k,_(l,ce,n,cf),D,cg),bx,_(),bZ,_(),ch,_(ci,cj),ck,bj,cl,bj),_(bB,cm,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,o,bX,cf),k,_(l,ce,n,cn),D,cg),bx,_(),bZ,_(),ch,_(ci,co),ck,bj,cl,bj),_(bB,cp,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,o,bX,cq),k,_(l,ce,n,bY),D,cg),bx,_(),bZ,_(),ch,_(ci,cr),ck,bj,cl,bj),_(bB,cs,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,ce,bX,o),k,_(l,ct,n,cf),D,cg),bx,_(),bZ,_(),ch,_(ci,cu),ck,bj,cl,bj),_(bB,cv,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,ce,bX,cf),k,_(l,ct,n,cn),D,cg,cw,cx),bx,_(),bZ,_(),ch,_(ci,cy),ck,bj,cl,bj),_(bB,cz,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,ce,bX,cq),k,_(l,ct,n,bY),D,cg,cw,cx),bx,_(),bZ,_(),ch,_(ci,cA),ck,bj,cl,bj),_(bB,cB,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,cC,bX,o),k,_(l,cD,n,cf),D,cg),bx,_(),bZ,_(),ch,_(ci,cE),ck,bj,cl,bj),_(bB,cF,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,cC,bX,cf),k,_(l,cD,n,cn),D,cg),bx,_(),bZ,_(),ch,_(ci,cG),ck,bj,cl,bj),_(bB,cH,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,cC,bX,cq),k,_(l,cD,n,bY),D,cg),bx,_(),bZ,_(),ch,_(ci,cI),ck,bj,cl,bj),_(bB,cJ,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,o,bX,cK),k,_(l,ce,n,cL),D,cg),bx,_(),bZ,_(),ch,_(ci,cM),ck,bj,cl,bj),_(bB,cN,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,ce,bX,cK),k,_(l,ct,n,cL),D,cg,cw,cx),bx,_(),bZ,_(),ch,_(ci,cO),ck,bj,cl,bj),_(bB,cP,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,cC,bX,cK),k,_(l,cD,n,cL),D,cg),bx,_(),bZ,_(),ch,_(ci,cQ),ck,bj,cl,bj),_(bB,cR,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,o,bX,cS),k,_(l,ce,n,cL),D,cg),bx,_(),bZ,_(),ch,_(ci,cT),ck,bj,cl,bj),_(bB,cU,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,ce,bX,cS),k,_(l,ct,n,cL),D,cg,cw,cx),bx,_(),bZ,_(),ch,_(ci,cV),ck,bj,cl,bj),_(bB,cW,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,cC,bX,cS),k,_(l,cD,n,cL),D,cg),bx,_(),bZ,_(),ch,_(ci,cX),ck,bj,cl,bj),_(bB,cY,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,o,bX,cZ),k,_(l,ce,n,cL),D,cg),bx,_(),bZ,_(),ch,_(ci,da),ck,bj,cl,bj),_(bB,db,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,ce,bX,cZ),k,_(l,ct,n,cL),D,cg,cw,cx),bx,_(),bZ,_(),ch,_(ci,dc),ck,bj,cl,bj),_(bB,dd,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,cC,bX,cZ),k,_(l,cD,n,cL),D,cg),bx,_(),bZ,_(),ch,_(ci,de),ck,bj,cl,bj)])])),df,_(),dg,_(dh,_(di,dj),dk,_(di,dl),dm,_(di,dn),dp,_(di,dq),dr,_(di,ds),dt,_(di,du),dv,_(di,dw),dx,_(di,dy),dz,_(di,dA),dB,_(di,dC),dD,_(di,dE),dF,_(di,dG),dH,_(di,dI),dJ,_(di,dK),dL,_(di,dM),dN,_(di,dO),dP,_(di,dQ),dR,_(di,dS),dT,_(di,dU)));}; 
var b="url",c="问题记录.html",d="generationDate",e=new Date(1751801875307.613),f="defaultAdaptiveView",g="name",h="",i="cursor",j=1,k="size",l="width",m=570,n="height",o=0,p="adaptiveViews",q="sketchKeys",r="s0",s="variables",t="OnLoadVariable",u="page",v="packageId",w="fbefe03aad7d46919533d58e0c564ac0",x="type",y="Axure:Page",z="问题记录",A="notes",B="annotations",C="style",D="baseStyle",E="627587b6038d43cca051c114ac41ad32",F="pageAlignment",G="center",H="fill",I="fillType",J="solid",K="color",L=0xFFFFFFFF,M="image",N="imageAlignment",O="horizontal",P="near",Q="vertical",R="imageRepeat",S="auto",T="favicon",U="sketchFactor",V="0",W="colorStyle",X="appliedColor",Y="fontName",Z="Applied font",ba="borderWidth",bb="borderVisibility",bc="top right bottom left",bd="borderFill",be=0xFF797979,bf="cornerRadius",bg="cornerVisibility",bh="outerShadow",bi="on",bj=false,bk="offsetX",bl=5,bm="offsetY",bn="blurRadius",bo="spread",bp="r",bq=0,br="g",bs="b",bt="a",bu=0.34901960784313724,bv="pageCursor",bw="touch",bx="adaptiveStyles",by="interactionMap",bz="diagram",bA="objects",bB="id",bC="113a9b69ecad4a51acc983a9252c399c",bD="label",bE="friendlyType",bF="表格",bG="table",bH="styleType",bI="visible",bJ=true,bK="\"ArialMT\", \"Arial\", sans-serif",bL="fontWeight",bM="400",bN="fontStyle",bO="normal",bP="fontStretch",bQ="5",bR="foreGroundFill",bS=0xFF333333,bT=348,bU="location",bV="x",bW=99,bX="y",bY=71,bZ="imageOverrides",ca="c230c41ba1684e3db60f41d7327a1706",cb="单元格",cc="tableCell",cd="\"PingFangSC-Regular\", \"苹方-简\", sans-serif",ce=130,cf=30,cg="33ea2511485c479dbf973af3302f2352",ch="images",ci="normal~",cj="images/问题记录/u2313.svg",ck="autoFitWidth",cl="autoFitHeight",cm="b8ff5e8ab6f84ae0aca2d2d3dabbca7a",cn=70,co="images/问题记录/u2316.svg",cp="b86f39954e4c44fc9f4ab97f4f19f79c",cq=100,cr="images/问题记录/u2319.svg",cs="a7cab140257b436583af3cc7397f3d98",ct=232,cu="images/问题记录/u2314.svg",cv="59f5c76746cf41c9a0463162d6ac6698",cw="horizontalAlignment",cx="left",cy="images/问题记录/u2317.svg",cz="8c9dafc2e5cf40948a49a963d2240afd",cA="images/问题记录/u2320.svg",cB="fac46866998142e99a4d0d55de5c504a",cC=362,cD=208,cE="images/问题记录/u2315.svg",cF="eb1347c2103643d4b1d328609461551d",cG="images/问题记录/u2318.svg",cH="70446a10d76d4a419f0721ecb521ff02",cI="images/问题记录/u2321.svg",cJ="4247f26fc6cc4cc6954a7c4b03e75fa5",cK=171,cL=59,cM="images/问题记录/u2322.svg",cN="e43629c851ef458a85c28298306144cb",cO="images/问题记录/u2323.svg",cP="5f37a18724684952b12aa503955d01bf",cQ="images/问题记录/u2324.svg",cR="c938ed5861d5458aa9f0f042d8226698",cS=230,cT="images/问题记录/u2325.svg",cU="d4ed4c15fb6f4d12b8534d6ee375983a",cV="images/问题记录/u2326.svg",cW="fd75b0beae454f06b2e66de705845b9a",cX="images/问题记录/u2327.svg",cY="923c9fdd32f24ab682864bac07ee218f",cZ=289,da="images/问题记录/u2328.svg",db="0596f739c0674190bc5d93ac0906fc2e",dc="images/问题记录/u2329.svg",dd="bab1f4cb7011458da23fe119e113b2e8",de="images/问题记录/u2330.svg",df="masters",dg="objectPaths",dh="113a9b69ecad4a51acc983a9252c399c",di="scriptId",dj="u2312",dk="c230c41ba1684e3db60f41d7327a1706",dl="u2313",dm="a7cab140257b436583af3cc7397f3d98",dn="u2314",dp="fac46866998142e99a4d0d55de5c504a",dq="u2315",dr="b8ff5e8ab6f84ae0aca2d2d3dabbca7a",ds="u2316",dt="59f5c76746cf41c9a0463162d6ac6698",du="u2317",dv="eb1347c2103643d4b1d328609461551d",dw="u2318",dx="b86f39954e4c44fc9f4ab97f4f19f79c",dy="u2319",dz="8c9dafc2e5cf40948a49a963d2240afd",dA="u2320",dB="70446a10d76d4a419f0721ecb521ff02",dC="u2321",dD="4247f26fc6cc4cc6954a7c4b03e75fa5",dE="u2322",dF="e43629c851ef458a85c28298306144cb",dG="u2323",dH="5f37a18724684952b12aa503955d01bf",dI="u2324",dJ="c938ed5861d5458aa9f0f042d8226698",dK="u2325",dL="d4ed4c15fb6f4d12b8534d6ee375983a",dM="u2326",dN="fd75b0beae454f06b2e66de705845b9a",dO="u2327",dP="923c9fdd32f24ab682864bac07ee218f",dQ="u2328",dR="0596f739c0674190bc5d93ac0906fc2e",dS="u2329",dT="bab1f4cb7011458da23fe119e113b2e8",dU="u2330";
return _creator();
})());