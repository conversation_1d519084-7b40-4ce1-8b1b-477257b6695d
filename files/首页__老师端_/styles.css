﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:616px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1230_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:386px;
  height:680px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1230 {
  border-width:0px;
  position:absolute;
  left:-224px;
  top:-3854px;
  width:386px;
  height:680px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1230 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1230_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1231 {
  border-width:0px;
  position:absolute;
  left:-174px;
  top:-3226px;
  width:26px;
  height:29px;
  display:flex;
  transition:none;
}
#u1231 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1231_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:29px;
}
#u1231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1232 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1233 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:-3226px;
  width:38px;
  height:34px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:22px;
}
#u1233 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u1233_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:34px;
}
#u1233_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1234 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:-3224px;
  width:33px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:22px;
}
#u1234 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u1234_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:30px;
}
#u1234_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1235_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1235 {
  border-width:0px;
  position:absolute;
  left:-174px;
  top:-3197px;
  width:27px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1235 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1235_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1236_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1236 {
  border-width:0px;
  position:absolute;
  left:69px;
  top:-3192px;
  width:27px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1236 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1236_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1237 {
  border-width:0px;
  position:absolute;
  left:-53px;
  top:-3226px;
  width:44px;
  height:44px;
  display:flex;
  transition:none;
  font-size:36px;
}
#u1237 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1237_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:44px;
}
#u1237_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1238_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:430px;
  height:806px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1238 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:15px;
  width:430px;
  height:806px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1238 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1238_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1239 {
  border-width:0px;
  position:absolute;
  left:333px;
  top:33px;
  width:103px;
  height:37px;
  display:flex;
  transition:none;
}
#u1239 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1239_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:37px;
}
#u1239_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1240_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:28px;
}
#u1240 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:43px;
  width:85px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:28px;
}
#u1240 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1240_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1241 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:231px;
  width:1px;
  height:46px;
  display:flex;
  transition:none;
  font-family:"MicrosoftSansSerif", "Microsoft Sans Serif", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u1241 .text {
  position:absolute;
  align-self:flex-start;
  padding:18px 2px 2px 36px;
  box-sizing:border-box;
  width:100%;
}
#u1241_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:46px;
}
#u1241_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1242_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(16, 142, 233, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u1242 {
  border-width:0px;
  position:absolute;
  left:88px;
  top:223px;
  width:89px;
  height:46px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u1242 .text {
  position:absolute;
  align-self:center;
  padding:12px 16px 12px 16px;
  box-sizing:border-box;
  width:100%;
}
#u1242_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1243_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(24, 144, 255, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1890FF;
  text-align:left;
  line-height:22px;
}
#u1243 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:223px;
  width:61px;
  height:46px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1890FF;
  text-align:left;
  line-height:22px;
}
#u1243 .text {
  position:absolute;
  align-self:center;
  padding:12px 16px 12px 16px;
  box-sizing:border-box;
  width:100%;
}
#u1243_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1244_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:91px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(16, 142, 233, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u1244 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:223px;
  width:91px;
  height:46px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u1244 .text {
  position:absolute;
  align-self:center;
  padding:12px 16px 12px 16px;
  box-sizing:border-box;
  width:100%;
}
#u1244_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1245_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(16, 142, 233, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u1245 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:223px;
  width:70px;
  height:46px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u1245 .text {
  position:absolute;
  align-self:center;
  padding:12px 16px 12px 16px;
  box-sizing:border-box;
  width:100%;
}
#u1245_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1246_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(16, 142, 233, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u1246 {
  border-width:0px;
  position:absolute;
  left:303px;
  top:223px;
  width:70px;
  height:46px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u1246 .text {
  position:absolute;
  align-self:center;
  padding:12px 16px 12px 16px;
  box-sizing:border-box;
  width:100%;
}
#u1246_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1247 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:89px;
  width:401px;
  height:121px;
  display:flex;
  transition:none;
}
#u1247 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1247_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:401px;
  height:121px;
}
#u1247_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1248 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:285px;
  width:80px;
  height:80px;
  display:flex;
  transition:none;
}
#u1248 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1248_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:80px;
}
#u1248_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1249_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1249 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:302px;
  width:110px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1249 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1249_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1250_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:18px;
  background:inherit;
  background-color:rgba(129, 211, 248, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1250 {
  border-width:0px;
  position:absolute;
  left:333px;
  top:303px;
  width:113px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1250 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1250_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1251_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:405px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1251 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:370px;
  width:405px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1251 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1251_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1252_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:18px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1252 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:422px;
  width:78px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1252 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1252_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1253_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1253 {
  border-width:0px;
  position:absolute;
  left:33px;
  top:422px;
  width:66px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1253 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1253_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1254_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:18px;
  background:inherit;
  background-color:rgba(129, 211, 248, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1254 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:326px;
  width:149px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1254 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1254_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1255 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:454px;
  width:424px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(0.13577154553748114deg);
  -moz-transform:rotate(0.13577154553748114deg);
  -ms-transform:rotate(0.13577154553748114deg);
  transform:rotate(0.13577154553748114deg);
  transition:none;
}
#u1255 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1255_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:2px;
}
#u1255_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1256_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:31px;
  background:inherit;
  background-color:rgba(30, 152, 215, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1256 {
  border-width:0px;
  position:absolute;
  left:351px;
  top:462px;
  width:85px;
  height:31px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1256 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1256_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1257 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1258 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:760px;
  width:34px;
  height:29px;
  display:flex;
  transition:none;
}
#u1258 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1258_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:29px;
}
#u1258_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1259_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1259 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:794px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1259 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1259_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1260 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1261 {
  border-width:0px;
  position:absolute;
  left:370px;
  top:760px;
  width:29px;
  height:34px;
  display:flex;
  transition:none;
}
#u1261 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1261_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:34px;
}
#u1261_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1262_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1262 {
  border-width:0px;
  position:absolute;
  left:370px;
  top:796px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1262 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1262_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1263 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1264 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:760px;
  width:34px;
  height:29px;
  display:flex;
  transition:none;
}
#u1264 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1264_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:29px;
}
#u1264_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1265_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1265 {
  border-width:0px;
  position:absolute;
  left:191px;
  top:794px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1265 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1265_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1266_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(16, 142, 233, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u1266 {
  border-width:0px;
  position:absolute;
  left:378px;
  top:223px;
  width:70px;
  height:46px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u1266 .text {
  position:absolute;
  align-self:center;
  padding:12px 16px 12px 16px;
  box-sizing:border-box;
  width:100%;
}
#u1266_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1267 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1268 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1269 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:493px;
  width:80px;
  height:80px;
  display:flex;
  transition:none;
}
#u1269 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1269_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:80px;
}
#u1269_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1270_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1270 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:510px;
  width:110px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1270 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1270_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1271_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:18px;
  background:inherit;
  background-color:rgba(129, 211, 248, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1271 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:511px;
  width:113px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1271 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1271_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1272_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:378px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1272 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:578px;
  width:378px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1272 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1272_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1273_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:18px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1273 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:630px;
  width:78px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1273 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1273_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1274_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1274 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:630px;
  width:66px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1274 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1274_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1275_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:18px;
  background:inherit;
  background-color:rgba(129, 211, 248, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1275 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:534px;
  width:149px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1275 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1275_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1276 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:662px;
  width:424px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(0.13577154553748114deg);
  -moz-transform:rotate(0.13577154553748114deg);
  -ms-transform:rotate(0.13577154553748114deg);
  transform:rotate(0.13577154553748114deg);
  transition:none;
}
#u1276 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1276_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:2px;
}
#u1276_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1277_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:31px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1277 {
  border-width:0px;
  position:absolute;
  left:352px;
  top:670px;
  width:85px;
  height:31px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1277 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1277_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1278_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:185px;
  height:80px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
}
#u1278 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:89px;
  width:185px;
  height:80px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1278 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1278_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1279_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:18px;
  background:inherit;
  background-color:rgba(2, 167, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:center;
}
#u1279 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:326px;
  width:98px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:center;
}
#u1279 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1279_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1280_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:18px;
  background:inherit;
  background-color:rgba(2, 167, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:center;
}
#u1280 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:530px;
  width:98px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:center;
}
#u1280 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1280_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1281_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:18px;
  background:inherit;
  background-color:rgba(250, 205, 145, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1281 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:469px;
  width:81px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1281 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1281_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1282_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:18px;
  background:inherit;
  background-color:rgba(250, 205, 145, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1282 {
  border-width:0px;
  position:absolute;
  left:258px;
  top:676px;
  width:79px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1282 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1282_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1283_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:232px;
  height:230px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1283 {
  border-width:0px;
  position:absolute;
  left:454px;
  top:188px;
  width:232px;
  height:230px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1283 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1283_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1284 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:287px;
  width:415px;
  height:168px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u1285_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:67px;
  background:inherit;
  background-color:rgba(255, 184, 217, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1285 {
  border-width:0px;
  position:absolute;
  left:454px;
  top:760px;
  width:267px;
  height:67px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1285 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1285_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1286_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:386px;
  height:172px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1286 {
  border-width:0px;
  position:absolute;
  left:454px;
  top:447px;
  width:386px;
  height:172px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1286 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1286_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1287 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:317px;
  width:332px;
  height:279px;
  visibility:hidden;
}
#u1287_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:332px;
  height:279px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1287_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1288_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:332px;
  height:279px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1288 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:332px;
  height:279px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1288 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1288_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1289_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1289 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:80px;
  width:157px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1289 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1289_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1290_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(30, 152, 215, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1290 {
  border-width:0px;
  position:absolute;
  left:69px;
  top:163px;
  width:140px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1290 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1290_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1291_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Helvetica", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u1291 {
  border-width:0px;
  position:absolute;
  left:305px;
  top:8px;
  width:13px;
  height:24px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Helvetica", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u1291 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1291_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
