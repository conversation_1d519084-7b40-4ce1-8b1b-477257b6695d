﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,j,k,_(l,m,n,o)),p,[],q,_(h,r),s,[t],u,_(v,w,x,y,g,z,A,_(),B,[],C,_(D,E,F,G,H,_(I,J,K,L),M,null,N,_(O,P,Q,P),R,S,T,null,U,V,W,X,Y,Z,ba,V,bb,bc,bd,_(I,J,K,be),bf,V,bg,bc,bh,_(bi,bj,bk,bl,bm,bl,bn,bl,bo,o,K,_(bp,bq,br,bq,bs,bq,bt,bu)),k,_(l,o,n,o),bv,bw),bx,_(),by,_(),bz,_(bA,[_(bB,bC,bD,h,bE,bF,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),k,_(l,m,n,bT),bU,_(bV,bW,bX,bY)),bx,_(),bZ,_(),bA,[_(bB,ca,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),k,_(l,ce,n,cf),D,cg),bx,_(),bZ,_(),ch,_(ci,cj),ck,bj,cl,bj),_(bB,cm,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,o,bX,cf),k,_(l,ce,n,cn),D,cg),bx,_(),bZ,_(),ch,_(ci,co),ck,bj,cl,bj),_(bB,cp,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,o,bX,cq),k,_(l,ce,n,cr),D,cg),bx,_(),bZ,_(),ch,_(ci,cs),ck,bj,cl,bj),_(bB,ct,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,ce,bX,o),k,_(l,cu,n,cf),D,cg),bx,_(),bZ,_(),ch,_(ci,cv),ck,bj,cl,bj),_(bB,cw,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bU,_(bV,ce,bX,cf),k,_(l,cu,n,cn),D,cg,cx,cy),bx,_(),bZ,_(),ch,_(ci,cz),ck,bj,cl,bj),_(bB,cA,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,ce,bX,cq),k,_(l,cu,n,cr),D,cg,cx,cy),bx,_(),bZ,_(),ch,_(ci,cB),ck,bj,cl,bj),_(bB,cC,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,cd,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,cD,bX,o),k,_(l,cE,n,cf),D,cg),bx,_(),bZ,_(),ch,_(ci,cF),ck,bj,cl,bj),_(bB,cG,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,cD,bX,cf),k,_(l,cE,n,cn),D,cg),bx,_(),bZ,_(),ch,_(ci,cH),ck,bj,cl,bj),_(bB,cI,bD,h,bE,cb,x,cc,bH,cc,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),bU,_(bV,cD,bX,cq),k,_(l,cE,n,cr),D,cg),bx,_(),bZ,_(),ch,_(ci,cJ),ck,bj,cl,bj)])])),cK,_(),cL,_(cM,_(cN,cO),cP,_(cN,cQ),cR,_(cN,cS),cT,_(cN,cU),cV,_(cN,cW),cX,_(cN,cY),cZ,_(cN,da),db,_(cN,dc),dd,_(cN,de),df,_(cN,dg)));}; 
var b="url",c="发版节奏.html",d="generationDate",e=new Date(1751801875315.235),f="defaultAdaptiveView",g="name",h="",i="cursor",j=1,k="size",l="width",m=527,n="height",o=0,p="adaptiveViews",q="sketchKeys",r="s0",s="variables",t="OnLoadVariable",u="page",v="packageId",w="23026fd8c33c40ff912bf9032b722688",x="type",y="Axure:Page",z="发版节奏",A="notes",B="annotations",C="style",D="baseStyle",E="627587b6038d43cca051c114ac41ad32",F="pageAlignment",G="center",H="fill",I="fillType",J="solid",K="color",L=0xFFFFFFFF,M="image",N="imageAlignment",O="horizontal",P="near",Q="vertical",R="imageRepeat",S="auto",T="favicon",U="sketchFactor",V="0",W="colorStyle",X="appliedColor",Y="fontName",Z="Applied font",ba="borderWidth",bb="borderVisibility",bc="top right bottom left",bd="borderFill",be=0xFF797979,bf="cornerRadius",bg="cornerVisibility",bh="outerShadow",bi="on",bj=false,bk="offsetX",bl=5,bm="offsetY",bn="blurRadius",bo="spread",bp="r",bq=0,br="g",bs="b",bt="a",bu=0.34901960784313724,bv="pageCursor",bw="touch",bx="adaptiveStyles",by="interactionMap",bz="diagram",bA="objects",bB="id",bC="94f75d59e4924d99a46f5211b7140549",bD="label",bE="friendlyType",bF="表格",bG="table",bH="styleType",bI="visible",bJ=true,bK="\"ArialMT\", \"Arial\", sans-serif",bL="fontWeight",bM="400",bN="fontStyle",bO="normal",bP="fontStretch",bQ="5",bR="foreGroundFill",bS=0xFF333333,bT=221,bU="location",bV="x",bW=59,bX="y",bY=50,bZ="imageOverrides",ca="29a686d585774587bb1c810c356ea2c6",cb="单元格",cc="tableCell",cd="\"PingFangSC-Regular\", \"苹方-简\", sans-serif",ce=92,cf=30,cg="33ea2511485c479dbf973af3302f2352",ch="images",ci="normal~",cj="images/发版节奏/u2332.svg",ck="autoFitWidth",cl="autoFitHeight",cm="8d9a7129c44a4bc982d3fc1aadd50ffd",cn=112,co="images/发版节奏/u2335.svg",cp="a6ef1d4d9adc49a083cadd91511aaa4c",cq=142,cr=79,cs="images/发版节奏/u2338.svg",ct="f5478d87570244b1abdac4801648cf78",cu=369,cv="images/发版节奏/u2333.svg",cw="971e93be9e2a4e9f8adc9baf7e8cafa8",cx="horizontalAlignment",cy="left",cz="images/发版节奏/u2336.svg",cA="a772c56eb26340e185281d8972aa6b31",cB="images/发版节奏/u2339.svg",cC="d6f2ba8008d542608f894025b59027bc",cD=461,cE=66,cF="images/发版节奏/u2334.svg",cG="69bad4885c914fe8a9876239d33fefa7",cH="images/发版节奏/u2337.svg",cI="b7709620a11e4ec8b6023acf85ba8b42",cJ="images/发版节奏/u2340.svg",cK="masters",cL="objectPaths",cM="94f75d59e4924d99a46f5211b7140549",cN="scriptId",cO="u2331",cP="29a686d585774587bb1c810c356ea2c6",cQ="u2332",cR="f5478d87570244b1abdac4801648cf78",cS="u2333",cT="d6f2ba8008d542608f894025b59027bc",cU="u2334",cV="8d9a7129c44a4bc982d3fc1aadd50ffd",cW="u2335",cX="971e93be9e2a4e9f8adc9baf7e8cafa8",cY="u2336",cZ="69bad4885c914fe8a9876239d33fefa7",da="u2337",db="a6ef1d4d9adc49a083cadd91511aaa4c",dc="u2338",dd="a772c56eb26340e185281d8972aa6b31",de="u2339",df="b7709620a11e4ec8b6023acf85ba8b42",dg="u2340";
return _creator();
})());