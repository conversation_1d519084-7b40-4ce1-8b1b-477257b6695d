﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-59px;
  width:527px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2331 {
  border-width:0px;
  position:absolute;
  left:59px;
  top:50px;
  width:527px;
  height:221px;
}
#u2332 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2332 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2332_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:30px;
}
#u2332_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2333 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:0px;
  width:369px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2333 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2333_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:369px;
  height:30px;
}
#u2333_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2334 {
  border-width:0px;
  position:absolute;
  left:461px;
  top:0px;
  width:66px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2334 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2334_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:30px;
}
#u2334_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2335 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:92px;
  height:112px;
  display:flex;
  transition:none;
}
#u2335 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2335_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:112px;
}
#u2335_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2336 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:30px;
  width:369px;
  height:112px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u2336 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2336_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:369px;
  height:112px;
}
#u2336_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2337 {
  border-width:0px;
  position:absolute;
  left:461px;
  top:30px;
  width:66px;
  height:112px;
  display:flex;
  transition:none;
}
#u2337 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2337_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:112px;
}
#u2337_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2338 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:142px;
  width:92px;
  height:79px;
  display:flex;
  transition:none;
}
#u2338 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2338_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:79px;
}
#u2338_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2339 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:142px;
  width:369px;
  height:79px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u2339 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2339_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:369px;
  height:79px;
}
#u2339_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2340 {
  border-width:0px;
  position:absolute;
  left:461px;
  top:142px;
  width:66px;
  height:79px;
  display:flex;
  transition:none;
}
#u2340 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2340_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:79px;
}
#u2340_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
