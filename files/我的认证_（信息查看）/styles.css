﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-16px;
  width:1144px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2232_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:456px;
  height:1373px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u2232 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:19px;
  width:456px;
  height:1373px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u2232 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2232_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2233_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:28px;
}
#u2233 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:30px;
  width:34px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:28px;
}
#u2233 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2233_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2234_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u2234 {
  border-width:0px;
  position:absolute;
  left:197px;
  top:34px;
  width:81px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u2234 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2234_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2235 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:32px;
  width:93px;
  height:30px;
  display:flex;
  transition:none;
}
#u2235 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2235_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:30px;
}
#u2235_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2236 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2237_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2237 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:140px;
  width:27px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2237 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2237_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2238 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:174px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2238 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2238_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2238_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2239_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:429px;
  height:40px;
  background:inherit;
  background-color:rgba(30, 152, 215, 1);
  border-radius:38px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2239 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:1343px;
  width:429px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2239 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2239_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2240_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u2240 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:203px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u2240 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2240_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2241 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:229px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2241 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2241_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2241_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2242_input {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2242_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2242_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2242_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2242_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2242 {
  border-width:0px;
  position:absolute;
  left:268px;
  top:202px;
  width:177px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2242 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2242_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2242.hint {
}
#u2242_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2242.disabled {
}
#u2242_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2242.hint.disabled {
}
#u2243 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:259px;
  width:1px;
  height:157px;
  display:flex;
  transition:none;
}
#u2243 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2243_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:157px;
}
#u2243_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2244 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:416px;
  width:1px;
  height:155px;
  display:flex;
  transition:none;
}
#u2244 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2244_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:155px;
}
#u2244_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2245 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2246_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2246 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:295px;
  width:40px;
  height:16px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2246 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2246_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2247 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:321px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2247 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2247_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2247_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2248_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2248 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:295px;
  width:119px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2248 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2248_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2249 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2250 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2251_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2251 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:338px;
  width:79px;
  height:16px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2251 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2251_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2252 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:364px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2252 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2252_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2252_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2253_input {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2253_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2253_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2253_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2253_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2253 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:335px;
  width:177px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2253 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2253_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2253.hint {
}
#u2253_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2253.disabled {
}
#u2253_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2253.hint.disabled {
}
#u2254 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2255 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2256_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2256 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:252px;
  width:40px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2256 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2256_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2257 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:278px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2257 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2257_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2257_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2258_input {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2258_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2258_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2258_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2258_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2258 {
  border-width:0px;
  position:absolute;
  left:268px;
  top:249px;
  width:177px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:right;
}
#u2258 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2258_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2258.hint {
}
#u2258_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2258.disabled {
}
#u2258_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2258.hint.disabled {
}
#u2259 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2260 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2261 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2262_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2262 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:383px;
  width:92px;
  height:16px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2262 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2262_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2263 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:409px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2263 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2263_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2263_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2264_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:20px;
  background:inherit;
  background-color:rgba(44, 140, 240, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  text-align:left;
  line-height:20px;
}
#u2264 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:383px;
  width:41px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  text-align:left;
  line-height:20px;
}
#u2264 .text {
  position:absolute;
  align-self:center;
  padding:0px 8px 0px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2264_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2265_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2265 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:429px;
  width:92px;
  height:16px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2265 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2265_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2266 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:455px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2266 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2266_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2266_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2267_input {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2267_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2267_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2267_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2267_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2267 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:426px;
  width:177px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2267 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2267_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2267.hint {
}
#u2267_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2267.disabled {
}
#u2267_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u2267.hint.disabled {
}
#u2268 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2269_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2269 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:614px;
  width:73px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2269 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2269_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2270_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:326px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2270 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:621px;
  width:326px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2270 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2270_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2271_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:315px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2271 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:659px;
  width:315px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2271 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2271_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2272 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:647px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2272 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2272_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2272_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2273_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Semibold", "苹方-简 Semibold", "苹方-简", sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:18px;
}
#u2273 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:99px;
  width:73px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Semibold", "苹方-简 Semibold", "苹方-简", sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:18px;
}
#u2273 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2273_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2274 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2275_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2275 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:709px;
  width:73px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2275 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2275_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2276 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:746px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2276 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2276_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2276_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2277_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:404px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2277 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:766px;
  width:404px;
  height:90px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2277 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2277_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2278_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2278 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:911px;
  width:73px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2278 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2278_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2279_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:274px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2279 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:915px;
  width:274px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2279 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2279_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2280_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:133px;
  background:inherit;
  background-color:rgba(255, 184, 217, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2280 {
  border-width:0px;
  position:absolute;
  left:782px;
  top:1277px;
  width:150px;
  height:133px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2280 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2280_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2281 {
  border-width:0px;
  position:absolute;
  left:374px;
  top:112px;
  width:62px;
  height:62px;
  display:flex;
  transition:none;
}
#u2281 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2281_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:62px;
}
#u2281_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2282 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:936px;
  width:198px;
  height:127px;
  display:flex;
  transition:none;
}
#u2282 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2282_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:127px;
}
#u2282_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2283 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:936px;
  width:198px;
  height:127px;
  display:flex;
  transition:none;
}
#u2283 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2283_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:127px;
}
#u2283_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2284 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:1066px;
  width:198px;
  height:127px;
  display:flex;
  transition:none;
}
#u2284 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2284_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:127px;
}
#u2284_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2285_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2285 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:119px;
  width:150px;
  height:41px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2285 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2285_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2286_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:79px;
  background:inherit;
  background-color:rgba(255, 255, 0, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2286 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:1313px;
  width:259px;
  height:79px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2286 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2286_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2287 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:72px;
  width:393px;
  height:27px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#D9001B;
}
#u2287 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2287_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:27px;
}
#u2287_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2288 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:72px;
  width:393px;
  height:27px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#D9001B;
}
#u2288 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2288_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:27px;
}
#u2288_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2289_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:203px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2289 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:13px;
  width:203px;
  height:49px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2289 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2289_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2290 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2291_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2291 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:477px;
  width:73px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2291 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2291_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2292_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2292 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:517px;
  width:74px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2292 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2292_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2292.mouseOver {
  opacity:0.8;
}
#u2292_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2293_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:28px;
  background:inherit;
  background-color:rgba(44, 140, 240, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:20px;
}
#u2293 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:517px;
  width:96px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:20px;
}
#u2293 .text {
  position:absolute;
  align-self:center;
  padding:0px 8px 0px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2293_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2294_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2294 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:517px;
  width:74px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2294 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2294_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2294.mouseOver {
  opacity:0.8;
}
#u2294_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2295_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2295 {
  border-width:0px;
  position:absolute;
  left:371px;
  top:517px;
  width:74px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2295 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2295_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2295.mouseOver {
  opacity:0.8;
}
#u2295_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2296_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2296 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:557px;
  width:74px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2296 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2296_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2296.mouseOver {
  opacity:0.8;
}
#u2296_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2297_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:28px;
  background:inherit;
  background-color:rgba(44, 140, 240, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:20px;
}
#u2297 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:557px;
  width:96px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:20px;
}
#u2297 .text {
  position:absolute;
  align-self:center;
  padding:0px 8px 0px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2297_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2298_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2298 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:557px;
  width:74px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2298 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2298_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2298.mouseOver {
  opacity:0.8;
}
#u2298_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2299_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2299 {
  border-width:0px;
  position:absolute;
  left:371px;
  top:557px;
  width:74px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2299 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2299_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2299.mouseOver {
  opacity:0.8;
}
#u2299_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2300_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2300 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:481px;
  width:87px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2300 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2300_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2301_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:178px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#D9001B;
}
#u2301 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:1325px;
  width:178px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#D9001B;
}
#u2301 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2301_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2302 {
  border-width:0px;
  position:absolute;
  left:915px;
  top:72px;
  width:245px;
  height:27px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#D9001B;
}
#u2302 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2302_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:245px;
  height:27px;
}
#u2302_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2303_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:203px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2303 {
  border-width:0px;
  position:absolute;
  left:915px;
  top:13px;
  width:203px;
  height:49px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2303 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2303_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
