﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-8.000696313122466px;
  width:931.9993036868775px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u151_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:456px;
  height:1108px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u151 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:24px;
  width:456px;
  height:1108px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u151 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u151_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u152_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:28px;
}
#u152 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:30px;
  width:34px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:28px;
}
#u152 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u152_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u153_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u153 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:38px;
  width:121px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u153 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u153_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u154 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:32px;
  width:93px;
  height:30px;
  display:flex;
  transition:none;
}
#u154 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u154_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:30px;
}
#u154_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u155 {
  border-width:0px;
  position:absolute;
  left:1630px;
  top:269px;
  width:360px;
  height:266px;
  visibility:hidden;
}
#u155_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:266px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u155_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u156_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:266px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u156 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:0px;
  width:358px;
  height:266px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u156 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u157_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u157 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:19px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u157 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u157_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u158_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u158 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:96px;
  width:267px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u158 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u158_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u159_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u159 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:209px;
  width:114px;
  height:37px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u159 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u159_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u160_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:36px;
  background:inherit;
  background-color:rgba(2, 167, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u160 {
  border-width:0px;
  position:absolute;
  left:204px;
  top:207px;
  width:117px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u160 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u160_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u155_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:266px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u155_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u161_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:266px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u161 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:0px;
  width:358px;
  height:266px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u161 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u161_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u162 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:19px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u162 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u162_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u163_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u163 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:79px;
  width:267px;
  height:27px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u163 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u164_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u164 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:209px;
  width:114px;
  height:37px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u164 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u165_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:36px;
  background:inherit;
  background-color:rgba(30, 152, 215, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u165 {
  border-width:0px;
  position:absolute;
  left:204px;
  top:207px;
  width:117px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u165 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u165_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u166_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u166 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:113px;
  width:267px;
  height:27px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u166 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u166_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u167_input {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u167_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u167_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u167_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u167_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u167 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:108px;
  width:98px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u167 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u167_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u167.hint {
}
#u167_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u167.disabled {
}
#u167_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u167.hint.disabled {
}
#u168_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:13px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:12px;
  color:#D9001B;
}
#u168 {
  border-width:0px;
  position:absolute;
  left:103px;
  top:149px;
  width:145px;
  height:13px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:12px;
  color:#D9001B;
}
#u168 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u168_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u169_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u169 {
  border-width:0px;
  position:absolute;
  left:101px;
  top:87px;
  width:120px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u169 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u169_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u170_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:18px;
  background:inherit;
  background-color:rgba(250, 205, 145, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u170 {
  border-width:0px;
  position:absolute;
  left:384px;
  top:91px;
  width:76px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u170 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u171_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u171 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:538px;
  width:101px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u171 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u171_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u172 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u173 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:576px;
  width:49px;
  height:49px;
  display:flex;
  transition:none;
}
#u173 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u173_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:49px;
}
#u173_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u174_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u174 {
  border-width:0px;
  position:absolute;
  left:91px;
  top:592px;
  width:37px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u174 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u174_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u175_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:404px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u175 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:635px;
  width:404px;
  height:54px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u175 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u175_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u176 {
  border-width:0px;
  position:absolute;
  left:8px;
  top:698px;
  width:457px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.3143868013546231deg);
  -moz-transform:rotate(-0.3143868013546231deg);
  -ms-transform:rotate(-0.3143868013546231deg);
  transform:rotate(-0.3143868013546231deg);
  transition:none;
}
#u176 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u176_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:458px;
  height:2px;
}
#u176_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u177 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:592px;
  width:109px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u177 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u177_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u178_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u178 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:592px;
  width:68px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u178 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u178_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u179 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u180 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:710px;
  width:49px;
  height:49px;
  display:flex;
  transition:none;
}
#u180 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u180_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:49px;
}
#u180_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u181_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u181 {
  border-width:0px;
  position:absolute;
  left:91px;
  top:726px;
  width:37px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u181 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u181_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u182_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:404px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u182 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:769px;
  width:404px;
  height:54px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u182 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u182_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u183 {
  border-width:0px;
  position:absolute;
  left:8px;
  top:832px;
  width:457px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.3143868013546231deg);
  -moz-transform:rotate(-0.3143868013546231deg);
  -ms-transform:rotate(-0.3143868013546231deg);
  transform:rotate(-0.3143868013546231deg);
  transition:none;
}
#u183 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u183_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:458px;
  height:2px;
}
#u183_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u184_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u184 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:726px;
  width:109px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u184 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u184_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u185_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u185 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:726px;
  width:68px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u185 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u185_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u186_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:277px;
  height:66px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u186 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:544px;
  width:277px;
  height:66px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u186 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u186_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u187_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u187 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:162px;
  width:98px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u187 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u187_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u188_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u188 {
  border-width:0px;
  position:absolute;
  left:192px;
  top:162px;
  width:92px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u188 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u188_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u189 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:70px;
  width:80px;
  height:80px;
  display:flex;
  transition:none;
}
#u189 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u189_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:80px;
}
#u189_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u190_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u190 {
  border-width:0px;
  position:absolute;
  left:101px;
  top:115px;
  width:153px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u190 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u190_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u191 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u192_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:418px;
  height:40px;
  background:inherit;
  background-color:rgba(30, 152, 215, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u192 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:1056px;
  width:418px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u192 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u193_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:446px;
  height:82px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u193 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:973px;
  width:446px;
  height:82px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u193 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u193_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u194_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:192px;
  height:53px;
  background:inherit;
  background-color:rgba(255, 184, 217, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u194 {
  border-width:0px;
  position:absolute;
  left:221px;
  top:1154px;
  width:192px;
  height:53px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u194 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u194_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u195 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u196 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u197_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:419px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u197 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:247px;
  width:419px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u197 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u197_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u198_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u198 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:210px;
  width:41px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u198 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u198_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u199 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:214px;
  width:2px;
  height:20px;
  display:flex;
  transition:none;
}
#u199 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u199_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u199_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u200 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u201 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u202_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:419px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u202 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:344px;
  width:419px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u202 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u202_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u203_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u203 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:307px;
  width:81px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u203 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u203_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u204 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:313px;
  width:2px;
  height:20px;
  display:flex;
  transition:none;
}
#u204 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u204_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u204_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u205 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u206_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u206 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:392px;
  width:81px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u206 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u206_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u207_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:20px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u207 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:440px;
  width:66px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u207 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u207_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u208_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:20px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u208 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:440px;
  width:66px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u208 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u208_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u209_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:20px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u209 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:440px;
  width:66px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u209 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u209_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u210_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:20px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u210 {
  border-width:0px;
  position:absolute;
  left:313px;
  top:440px;
  width:66px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u210 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u211 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:396px;
  width:2px;
  height:20px;
  display:flex;
  transition:none;
}
#u211 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u211_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:5px;
  height:23px;
}
#u211_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u212 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:520px;
  width:451px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(0.05208469045094913deg);
  -moz-transform:rotate(0.05208469045094913deg);
  -ms-transform:rotate(0.05208469045094913deg);
  transform:rotate(0.05208469045094913deg);
  transition:none;
}
#u212 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u212_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:2px;
}
#u212_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u213_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:277px;
  height:66px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u213 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:1066px;
  width:277px;
  height:66px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u213 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
