﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-17px;
  width:457.0005161716861px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1114_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:456px;
  height:736px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1114 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:0px;
  width:456px;
  height:736px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1114 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1114_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1115_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:28px;
}
#u1115 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:30px;
  width:34px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:28px;
}
#u1115 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1116_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u1116 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:38px;
  width:81px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u1116 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1116_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1117 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:32px;
  width:93px;
  height:30px;
  display:flex;
  transition:none;
}
#u1117 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1117_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:30px;
}
#u1117_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1118 {
  border-width:0px;
  position:absolute;
  left:1630px;
  top:269px;
  width:360px;
  height:266px;
  visibility:hidden;
}
#u1118_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:266px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1118_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1119_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:266px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1119 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:0px;
  width:358px;
  height:266px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1119 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1119_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1120_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1120 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:19px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1120 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1120_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1121_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u1121 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:96px;
  width:267px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u1121 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1121_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1122_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1122 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:209px;
  width:114px;
  height:37px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1122 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1122_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1123_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:36px;
  background:inherit;
  background-color:rgba(2, 167, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1123 {
  border-width:0px;
  position:absolute;
  left:204px;
  top:207px;
  width:117px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1123 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1123_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1118_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:266px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1118_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1124_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:266px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1124 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:0px;
  width:358px;
  height:266px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1124 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1124_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1125 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:19px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1125 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1125_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1126_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u1126 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:79px;
  width:267px;
  height:27px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u1126 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1126_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1127_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1127 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:209px;
  width:114px;
  height:37px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1127 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1127_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1128_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:36px;
  background:inherit;
  background-color:rgba(30, 152, 215, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1128 {
  border-width:0px;
  position:absolute;
  left:204px;
  top:207px;
  width:117px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1128 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1128_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1129_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u1129 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:113px;
  width:267px;
  height:27px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u1129 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1129_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1130_input {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1130_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1130_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1130_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1130_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1130 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:108px;
  width:98px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1130 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1130_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1130.hint {
}
#u1130_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1130.disabled {
}
#u1130_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1130.hint.disabled {
}
#u1131_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:13px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:12px;
  color:#D9001B;
}
#u1131 {
  border-width:0px;
  position:absolute;
  left:103px;
  top:149px;
  width:145px;
  height:13px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:12px;
  color:#D9001B;
}
#u1131 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1131_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1132 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:192px;
  width:45px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1132 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1132_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1133_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1133 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:192px;
  width:32px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1133 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1133_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1134_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1134 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:144px;
  width:53px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1134 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1134_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1135 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1136_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u1136 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:144px;
  width:62px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u1136 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1136_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1137 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:167px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1137 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1137_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1137_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1138_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1138 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:227px;
  width:48px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1138 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1138_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1139_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1139 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:230px;
  width:40px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1139 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1139_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1140 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1141 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1142_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1142 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:103px;
  width:53px;
  height:14px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1142 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1142_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1143 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1144_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#02A7F0;
}
#u1144 {
  border-width:0px;
  position:absolute;
  left:421px;
  top:101px;
  width:35px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#02A7F0;
}
#u1144 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1144_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1145 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:130px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1145 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1145_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1145_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1146 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1147 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1148_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1148 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:258px;
  width:48px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1148 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1148_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1149 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1150_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1150 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:292px;
  width:48px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1150 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1150_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1151 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1152_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1152 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:323px;
  width:48px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1152 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1152_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1153_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:424px;
  height:106px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1153 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:436px;
  width:424px;
  height:106px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1153 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1153_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1154 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1155_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u1155 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:144px;
  width:57px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u1155 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1155_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1156 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1157_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u1157 {
  border-width:0px;
  position:absolute;
  left:386px;
  top:144px;
  width:53px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u1157 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1157_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1158_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1158 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:192px;
  width:40px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1158 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1158_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1159_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1159 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:227px;
  width:35px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1159 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1159_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1160_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1160 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:258px;
  width:35px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1160 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1160_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1161_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1161 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:292px;
  width:35px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1161 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1161_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1162 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:323px;
  width:35px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1162 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1162_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1163_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1163 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:258px;
  width:40px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1163 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1163_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1164_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1164 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:292px;
  width:40px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1164 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1164_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1165_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1165 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:321px;
  width:40px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1165 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1165_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1166 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:353px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1166 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1166_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1166_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1167 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1168_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1168 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:364px;
  width:48px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1168 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1168_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1169 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1170_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#D9001B;
}
#u1170 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:364px;
  width:40px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#D9001B;
}
#u1170 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1170_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1171 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1172_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1172 {
  border-width:0px;
  position:absolute;
  left:228px;
  top:364px;
  width:40px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1172 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1172_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1173_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1173 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:77px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1173 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1173_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1174_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1174 {
  border-width:0px;
  position:absolute;
  left:217px;
  top:77px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1174 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1174_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1175 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:101px;
  width:53px;
  height:3px;
  display:flex;
  transition:none;
  font-size:12px;
}
#u1175 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1175_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:57px;
  height:7px;
}
#u1175_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1176_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1176 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:77px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1176 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1176_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
