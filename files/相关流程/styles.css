﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-131px;
  width:235px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2304 {
  border-width:0px;
  position:absolute;
  left:185px;
  top:123px;
  width:100px;
  height:51px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2304 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2304_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:51px;
}
#u2304_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2305_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#D9001B;
}
#u2305 {
  border-width:0px;
  position:absolute;
  left:195px;
  top:72px;
  width:79px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#D9001B;
}
#u2305 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2305_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2306_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
  background:inherit;
  background-color:rgba(250, 205, 145, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2306 {
  border-width:0px;
  position:absolute;
  left:185px;
  top:207px;
  width:100px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2306 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2306_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2307_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2307 {
  border-width:0px;
  position:absolute;
  left:131px;
  top:291px;
  width:100px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2307 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2307_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2308_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
  background:inherit;
  background-color:rgba(250, 205, 145, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2308 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:291px;
  width:100px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2308 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2308_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2309_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
  background:inherit;
  background-color:rgba(250, 205, 145, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2309 {
  border-width:0px;
  position:absolute;
  left:259px;
  top:364px;
  width:100px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2309 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2309_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2310_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
  background:inherit;
  background-color:rgba(202, 249, 130, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2310 {
  border-width:0px;
  position:absolute;
  left:195px;
  top:448px;
  width:100px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2310 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2310_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2311_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
  background:inherit;
  background-color:rgba(250, 205, 145, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2311 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:364px;
  width:100px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2311 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2311_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
