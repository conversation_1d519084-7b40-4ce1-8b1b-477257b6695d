﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,j,k,_(l,m,n,o)),p,[],q,_(h,r),s,[t],u,_(v,w,x,y,g,z,A,_(),B,[],C,_(D,E,F,G,H,_(I,J,K,L),M,null,N,_(O,P,Q,P),R,S,T,null,U,V,W,X,Y,Z,ba,V,bb,bc,bd,_(I,J,K,be),bf,V,bg,bc,bh,_(bi,bj,bk,bl,bm,bl,bn,bl,bo,o,K,_(bp,bq,br,bq,bs,bq,bt,bu)),k,_(l,o,n,o),bv,bw),bx,_(),by,_(),bz,_(bA,[_(bB,bC,bD,h,bE,bF,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),k,_(l,bT,n,bU),D,bV,bW,_(bX,bY,bZ,ca)),bx,_(),cb,_(),cc,_(cd,ce,cf,cg),ch,bj,ci,bj,cj,bj),_(bB,ck,bD,h,bE,cl,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bR,_(I,J,K,cm),bL,bM,bN,bO,bP,bQ,k,_(l,cn,n,co),D,cp,bW,_(bX,cq,bZ,cr)),bx,_(),cb,_(),ch,bj,ci,bJ,cj,bJ),_(bB,cs,bD,h,bE,cl,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),k,_(l,bT,n,ct),D,bV,bf,cu,bW,_(bX,bY,bZ,cv),H,_(I,J,K,cw)),bx,_(),cb,_(),ch,bj,ci,bj,cj,bj),_(bB,cx,bD,h,bE,cl,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),k,_(l,bT,n,ct),D,bV,bf,cu,bW,_(bX,cy,bZ,cz),H,_(I,J,K,cA)),bx,_(),cb,_(),ch,bj,ci,bj,cj,bj),_(bB,cB,bD,h,bE,cl,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),k,_(l,bT,n,ct),D,bV,bf,cu,bW,_(bX,cC,bZ,cz),H,_(I,J,K,cw)),bx,_(),cb,_(),ch,bj,ci,bj,cj,bj),_(bB,cD,bD,h,bE,cl,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),k,_(l,bT,n,ct),D,bV,bf,cu,bW,_(bX,cE,bZ,cF),H,_(I,J,K,cw)),bx,_(),cb,_(),ch,bj,ci,bj,cj,bj),_(bB,cG,bD,h,bE,cl,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),k,_(l,bT,n,ct),D,bV,bf,cu,bW,_(bX,cq,bZ,cH),H,_(I,J,K,cA)),bx,_(),cb,_(),ch,bj,ci,bj,cj,bj),_(bB,cI,bD,h,bE,cl,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),k,_(l,bT,n,ct),D,bV,bf,cu,bW,_(bX,cJ,bZ,cF),H,_(I,J,K,cw)),bx,_(),cb,_(),ch,bj,ci,bj,cj,bj)])),cK,_(),cL,_(cM,_(cN,cO),cP,_(cN,cQ),cR,_(cN,cS),cT,_(cN,cU),cV,_(cN,cW),cX,_(cN,cY),cZ,_(cN,da),db,_(cN,dc)));}; 
var b="url",c="相关流程.html",d="generationDate",e=new Date(1751801875298.072),f="defaultAdaptiveView",g="name",h="",i="cursor",j=1,k="size",l="width",m=235,n="height",o=0,p="adaptiveViews",q="sketchKeys",r="s0",s="variables",t="OnLoadVariable",u="page",v="packageId",w="d409d7e38b5d442b95f55b5ec3198544",x="type",y="Axure:Page",z="相关流程",A="notes",B="annotations",C="style",D="baseStyle",E="627587b6038d43cca051c114ac41ad32",F="pageAlignment",G="center",H="fill",I="fillType",J="solid",K="color",L=0xFFFFFFFF,M="image",N="imageAlignment",O="horizontal",P="near",Q="vertical",R="imageRepeat",S="auto",T="favicon",U="sketchFactor",V="0",W="colorStyle",X="appliedColor",Y="fontName",Z="Applied font",ba="borderWidth",bb="borderVisibility",bc="top right bottom left",bd="borderFill",be=0xFF797979,bf="cornerRadius",bg="cornerVisibility",bh="outerShadow",bi="on",bj=false,bk="offsetX",bl=5,bm="offsetY",bn="blurRadius",bo="spread",bp="r",bq=0,br="g",bs="b",bt="a",bu=0.34901960784313724,bv="pageCursor",bw="touch",bx="adaptiveStyles",by="interactionMap",bz="diagram",bA="objects",bB="id",bC="bde929d0794f47bc82d6159210c3eb53",bD="label",bE="friendlyType",bF="椭圆",bG="vectorShape",bH="styleType",bI="visible",bJ=true,bK="\"PingFangSC-Regular\", \"苹方-简\", sans-serif",bL="fontWeight",bM="400",bN="fontStyle",bO="normal",bP="fontStretch",bQ="5",bR="foreGroundFill",bS=0xFF333333,bT=100,bU=51,bV="caddf88798f04a469d3bb16589ed2a5d",bW="location",bX="x",bY=185,bZ="y",ca=123,cb="imageOverrides",cc="images",cd="normal~",ce="images/相关流程/u2304.svg",cf="images/相关流程/u2304.svg-isGeneratedImage",cg="true",ch="generateCompound",ci="autoFitWidth",cj="autoFitHeight",ck="03b6f4bdac154f83a4d3d139dfdb72e2",cl="矩形",cm=0xFFD9001B,cn=79,co=18,cp="2285372321d148ec80932747449c36c9",cq=195,cr=72,cs="a4c2dbddfc47442087df7f08ca453d91",ct=40,cu="10",cv=207,cw=0xFFFACD91,cx="04a4d7815e104cf3887a783d1948d028",cy=131,cz=291,cA=0xFFCAF982,cB="49f4e6c6fb32453695f885ef7a1003a7",cC=266,cD="a41a1328458e4c36a136c5b15eab0bdc",cE=259,cF=364,cG="00b6706d720a4991b9dfdd2590616c13",cH=448,cI="ccccd8cb0e6348fa90cfbfba5fe79e73",cJ=135,cK="masters",cL="objectPaths",cM="bde929d0794f47bc82d6159210c3eb53",cN="scriptId",cO="u2304",cP="03b6f4bdac154f83a4d3d139dfdb72e2",cQ="u2305",cR="a4c2dbddfc47442087df7f08ca453d91",cS="u2306",cT="04a4d7815e104cf3887a783d1948d028",cU="u2307",cV="49f4e6c6fb32453695f885ef7a1003a7",cW="u2308",cX="a41a1328458e4c36a136c5b15eab0bdc",cY="u2309",cZ="00b6706d720a4991b9dfdd2590616c13",da="u2310",db="ccccd8cb0e6348fa90cfbfba5fe79e73",dc="u2311";
return _creator();
})());