﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-16.999483828313892px;
  width:629.0005161716861px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1292_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:456px;
  height:757px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1292 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:-1px;
  width:456px;
  height:757px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1292 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1292_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1293_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:28px;
}
#u1293 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:30px;
  width:34px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:28px;
}
#u1293 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1293_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1294_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u1294 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:38px;
  width:81px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u1294 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1294_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1295 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:32px;
  width:93px;
  height:30px;
  display:flex;
  transition:none;
}
#u1295 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1295_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:30px;
}
#u1295_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1296 {
  border-width:0px;
  position:absolute;
  left:1630px;
  top:269px;
  width:360px;
  height:266px;
  visibility:hidden;
}
#u1296_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:266px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1296_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1297_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:266px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1297 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:0px;
  width:358px;
  height:266px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1297 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1297_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1298_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1298 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:19px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1298 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1298_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1299_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u1299 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:96px;
  width:267px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u1299 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1299_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1300_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1300 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:209px;
  width:114px;
  height:37px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1300 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1300_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1301_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:36px;
  background:inherit;
  background-color:rgba(2, 167, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1301 {
  border-width:0px;
  position:absolute;
  left:204px;
  top:207px;
  width:117px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1301 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1301_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1296_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:266px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1296_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1302_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:266px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1302 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:0px;
  width:358px;
  height:266px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1302 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1302_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1303_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1303 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:19px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1303 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1303_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1304_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u1304 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:79px;
  width:267px;
  height:27px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u1304 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1304_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1305_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1305 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:209px;
  width:114px;
  height:37px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1305 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1305_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1306_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:36px;
  background:inherit;
  background-color:rgba(30, 152, 215, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1306 {
  border-width:0px;
  position:absolute;
  left:204px;
  top:207px;
  width:117px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1306 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1306_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1307_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u1307 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:113px;
  width:267px;
  height:27px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u1307 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1307_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1308_input {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1308_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1308_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1308_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1308_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1308 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:108px;
  width:98px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1308 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1308_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1308.hint {
}
#u1308_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1308.disabled {
}
#u1308_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1308.hint.disabled {
}
#u1309_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:13px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:12px;
  color:#D9001B;
}
#u1309 {
  border-width:0px;
  position:absolute;
  left:103px;
  top:149px;
  width:145px;
  height:13px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:12px;
  color:#D9001B;
}
#u1309 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1309_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1310 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1311 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1312_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1312 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:129px;
  width:53px;
  height:14px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1312 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1312_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1313 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1314_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1314 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:127px;
  width:27px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1314 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1314_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1315 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:156px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1315 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1315_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1315_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1316 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1317 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1318_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1318 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:89px;
  width:53px;
  height:14px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1318 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1318_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1319 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1320_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#000000;
}
#u1320 {
  border-width:0px;
  position:absolute;
  left:358px;
  top:88px;
  width:112px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#000000;
}
#u1320 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1320_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1321 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:116px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1321 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1321_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1321_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1322 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1323 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1324_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1324 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:171px;
  width:53px;
  height:14px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1324 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1324_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1325 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1326_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1326 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:169px;
  width:27px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1326 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1326_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1327 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:198px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1327 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1327_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1327_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1328 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1329 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1330_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1330 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:206px;
  width:53px;
  height:14px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1330 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1330_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1331 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1332_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1332 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:204px;
  width:27px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1332 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1332_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1333 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:233px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1333 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1333_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1333_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1334 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1335 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1336_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1336 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:247px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1336 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1336_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1337 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1338_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1338 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:245px;
  width:27px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1338 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1338_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1339 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:274px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1339 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1339_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1339_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1340 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1341 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1342_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1342 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:288px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1342 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1342_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1343 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1344_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1344 {
  border-width:0px;
  position:absolute;
  left:370px;
  top:286px;
  width:96px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1344 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1344_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1345 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:315px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1345 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1345_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1345_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1346 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1347 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1348_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1348 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:408px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1348 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1348_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1349_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:417px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1349 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:444px;
  width:417px;
  height:54px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1349 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1349_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1350 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:435px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1350 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1350_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1350_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1351 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1352_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:430px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1352 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:572px;
  width:430px;
  height:54px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1352 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1352_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1353 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1354 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1355_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1355 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:530px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1355 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1355_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1356 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1357 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:557px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1357 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1357_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1357_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1358_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:293px;
  height:40px;
  background:inherit;
  background-color:rgba(30, 152, 215, 1);
  border-radius:133px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1358 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:706px;
  width:293px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1358 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1358_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1359 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1360 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1361_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1361 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:330px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1361 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1361_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1362 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1363_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1363 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:330px;
  width:27px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1363 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1363_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1364 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:357px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1364 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1364_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1364_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1365 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1366 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1367_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1367 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:363px;
  width:79px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1367 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1367_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1368 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1369_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1369 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:363px;
  width:40px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1369 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1369_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1370 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:390px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1370 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1370_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1370_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1371_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:18px;
  background:inherit;
  background-color:rgba(250, 205, 145, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#EC808D;
}
#u1371 {
  border-width:0px;
  position:absolute;
  left:187px;
  top:679px;
  width:81px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#EC808D;
}
#u1371 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1371_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1372_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:69px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
}
#u1372 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:688px;
  width:166px;
  height:69px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1372 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1372_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
