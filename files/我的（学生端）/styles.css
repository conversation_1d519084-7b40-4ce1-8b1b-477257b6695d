﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:473px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u214_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:386px;
  height:680px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u214 {
  border-width:0px;
  position:absolute;
  left:-224px;
  top:-3854px;
  width:386px;
  height:680px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u214 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u214_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u215 {
  border-width:0px;
  position:absolute;
  left:-174px;
  top:-3226px;
  width:26px;
  height:29px;
  display:flex;
  transition:none;
}
#u215 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u215_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:29px;
}
#u215_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u216 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u217 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:-3226px;
  width:38px;
  height:34px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:22px;
}
#u217 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u217_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:34px;
}
#u217_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u218 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:-3224px;
  width:33px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:22px;
}
#u218 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u218_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:30px;
}
#u218_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u219_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u219 {
  border-width:0px;
  position:absolute;
  left:-174px;
  top:-3197px;
  width:27px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u219 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u219_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u220_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u220 {
  border-width:0px;
  position:absolute;
  left:69px;
  top:-3192px;
  width:27px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u220 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u220_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u221 {
  border-width:0px;
  position:absolute;
  left:-53px;
  top:-3226px;
  width:44px;
  height:44px;
  display:flex;
  transition:none;
  font-size:36px;
}
#u221 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u221_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:44px;
}
#u221_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u222_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:386px;
  height:941px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u222 {
  border-width:0px;
  position:absolute;
  left:96px;
  top:35px;
  width:386px;
  height:941px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u222 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u222_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u223_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:28px;
}
#u223 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:55px;
  width:18px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:28px;
}
#u223 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u223_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u224 {
  border-width:0px;
  position:absolute;
  left:218px;
  top:67px;
  width:41px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u224 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u224_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u225 {
  border-width:0px;
  position:absolute;
  left:349px;
  top:57px;
  width:103px;
  height:37px;
  display:flex;
  transition:none;
}
#u225 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u225_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:37px;
}
#u225_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u226 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u227 {
  border-width:0px;
  position:absolute;
  left:205px;
  top:136px;
  width:94px;
  height:94px;
  display:flex;
  transition:none;
}
#u227 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u227_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:94px;
}
#u227_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u228_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u228 {
  border-width:0px;
  position:absolute;
  left:221px;
  top:245px;
  width:63px;
  height:15px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u228 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u228_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u229 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u230_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u230 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:301px;
  width:62px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u230 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u230_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u231_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u231 {
  border-width:0px;
  position:absolute;
  left:456px;
  top:301px;
  width:10px;
  height:15px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u231 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u232 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:333px;
  width:361px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(0.1993566017768655deg);
  -moz-transform:rotate(0.1993566017768655deg);
  -ms-transform:rotate(0.1993566017768655deg);
  transform:rotate(0.1993566017768655deg);
  transition:none;
}
#u232 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u232_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:2px;
}
#u232_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u233 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u234_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u234 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:348px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u234 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u234_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u235_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u235 {
  border-width:0px;
  position:absolute;
  left:447px;
  top:350px;
  width:19px;
  height:15px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u235 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u235_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u236_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(2, 167, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u236 {
  border-width:0px;
  position:absolute;
  left:292px;
  top:242px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
#u236 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u236_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u237_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#D9001B;
}
#u237 {
  border-width:0px;
  position:absolute;
  left:386px;
  top:242px;
  width:87px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#D9001B;
}
#u237 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u237_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u238_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:78px;
  background:inherit;
  background-color:rgba(255, 184, 217, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u238 {
  border-width:0px;
  position:absolute;
  left:547px;
  top:198px;
  width:150px;
  height:78px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u238 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u238_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
