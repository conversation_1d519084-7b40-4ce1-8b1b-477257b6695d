﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-16.999483828313867px;
  width:867.0005161716862px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1485_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:456px;
  height:757px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1485 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:30px;
  width:456px;
  height:757px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1485 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1485_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1486_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:28px;
}
#u1486 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:30px;
  width:34px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:28px;
}
#u1486 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1486_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1487_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u1487 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:38px;
  width:81px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u1487 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1487_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1488 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:32px;
  width:93px;
  height:30px;
  display:flex;
  transition:none;
}
#u1488 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1488_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:30px;
}
#u1488_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1489_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:397px;
  height:101px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
}
#u1489 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:176px;
  width:397px;
  height:101px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1489 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1489_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1490 {
  border-width:0px;
  position:absolute;
  left:1630px;
  top:269px;
  width:360px;
  height:266px;
  visibility:hidden;
}
#u1490_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:266px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1490_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1491_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:266px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1491 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:0px;
  width:358px;
  height:266px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1491 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1491_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1492_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1492 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:19px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1492 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1492_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1493_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u1493 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:96px;
  width:267px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u1493 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1493_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1494_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1494 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:209px;
  width:114px;
  height:37px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1494 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1494_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1495_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:36px;
  background:inherit;
  background-color:rgba(2, 167, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1495 {
  border-width:0px;
  position:absolute;
  left:204px;
  top:207px;
  width:117px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1495 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1495_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1490_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:266px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1490_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1496_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:266px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1496 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:0px;
  width:358px;
  height:266px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1496 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1496_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1497_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1497 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:19px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1497 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1497_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1498_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u1498 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:79px;
  width:267px;
  height:27px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u1498 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1498_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1499_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1499 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:209px;
  width:114px;
  height:37px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1499 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1499_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1500_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:36px;
  background:inherit;
  background-color:rgba(30, 152, 215, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1500 {
  border-width:0px;
  position:absolute;
  left:204px;
  top:207px;
  width:117px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1500 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1500_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1501_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u1501 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:113px;
  width:267px;
  height:27px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u1501 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1501_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1502_input {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1502_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1502_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1502_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1502_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1502 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:108px;
  width:98px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1502 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1502_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1502.hint {
}
#u1502_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1502.disabled {
}
#u1502_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1502.hint.disabled {
}
#u1503_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:13px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:12px;
  color:#D9001B;
}
#u1503 {
  border-width:0px;
  position:absolute;
  left:103px;
  top:149px;
  width:145px;
  height:13px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:12px;
  color:#D9001B;
}
#u1503 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1503_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1504 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1505 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1506_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1506 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:209px;
  width:53px;
  height:14px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1506 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1506_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1507 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1508_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1508 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:207px;
  width:27px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1508 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1508_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1509 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:236px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1509 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1509_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1509_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1510 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1511 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1512_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1512 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:126px;
  width:40px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1512 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1512_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1513 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:155px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1513 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1513_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1513_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1514 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1515_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1515 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:126px;
  width:98px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1515 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1515_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1516 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1517 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1518_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1518 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:169px;
  width:53px;
  height:14px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1518 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1518_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1519 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1520_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#000000;
}
#u1520 {
  border-width:0px;
  position:absolute;
  left:358px;
  top:168px;
  width:112px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#000000;
}
#u1520 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1520_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1521 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:196px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1521 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1521_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1521_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1522 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1523 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1524_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1524 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:251px;
  width:53px;
  height:14px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1524 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1524_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1525 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1526_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1526 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:249px;
  width:27px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1526 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1526_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1527 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:278px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1527 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1527_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1527_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1528 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1529 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1530_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1530 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:286px;
  width:53px;
  height:14px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1530 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1530_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1531 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1532_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1532 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:284px;
  width:27px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1532 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1532_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1533 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:313px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1533 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1533_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1533_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1534 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1535 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1536_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1536 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:327px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1536 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1536_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1537 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1538_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1538 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:325px;
  width:27px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1538 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1538_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1539 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:354px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1539 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1539_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1539_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1540 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1541 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1542_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1542 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:368px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1542 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1542_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1543 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1544_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1544 {
  border-width:0px;
  position:absolute;
  left:380px;
  top:366px;
  width:80px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1544 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1544_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1545 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:395px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1545 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1545_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1545_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1546 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1547 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1548_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1548 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:519px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1548 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1548_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1549_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:417px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1549 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:555px;
  width:417px;
  height:54px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1549 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1549_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1550 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:546px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1550 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1550_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1550_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1551_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:430px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1551 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:657px;
  width:430px;
  height:54px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1551 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1551_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1552 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1553 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1554_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1554 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:409px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1554 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1554_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1555 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1556 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:436px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1556 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1556_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1556_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1557 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1558_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1558 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:451px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1558 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1558_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1559_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:20px;
  background:inherit;
  background-color:rgba(44, 140, 240, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  text-align:left;
  line-height:20px;
}
#u1559 {
  border-width:0px;
  position:absolute;
  left:414px;
  top:451px;
  width:41px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  text-align:left;
  line-height:20px;
}
#u1559 .text {
  position:absolute;
  align-self:center;
  padding:0px 8px 0px 8px;
  box-sizing:border-box;
  width:100%;
}
#u1559_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1560 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1561_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1561 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:485px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1561 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1561_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1562_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:20px;
  background:inherit;
  background-color:rgba(44, 140, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
}
#u1562 {
  border-width:0px;
  position:absolute;
  left:408px;
  top:485px;
  width:52px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
}
#u1562 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u1562_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:20px;
  background:inherit;
  background-color:rgba(44, 140, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
}
#u1562.mouseOver {
  opacity:0.8;
}
#u1562_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1563 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1564 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1565_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1565 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:623px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1565 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1565_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1566 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1567 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:650px;
  width:453px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.15865132246412825deg);
  -moz-transform:rotate(-0.15865132246412825deg);
  -ms-transform:rotate(-0.15865132246412825deg);
  transform:rotate(-0.15865132246412825deg);
  transition:none;
}
#u1567 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1567_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:454px;
  height:2px;
}
#u1567_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
