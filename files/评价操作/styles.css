﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-568px;
  width:643px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u957_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:456px;
  height:823px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u957 {
  border-width:0px;
  position:absolute;
  left:568px;
  top:18px;
  width:456px;
  height:823px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u958 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u959_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:39px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u959 {
  border-width:0px;
  position:absolute;
  left:609px;
  top:38px;
  width:89px;
  height:39px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u959 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u959_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u960_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u960 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:46px;
  width:106px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u960 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u960_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u961 {
  border-width:0px;
  position:absolute;
  left:895px;
  top:38px;
  width:103px;
  height:37px;
  display:flex;
  transition:none;
}
#u961 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u961_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:37px;
}
#u961_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u962 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u963_input {
  position:absolute;
  left:0px;
  top:0px;
  width:397px;
  height:170px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
  resize:none;
}
#u963_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:397px;
  height:170px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
  resize:none;
}
#u963_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:397px;
  height:170px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
  resize:none;
}
#u963_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:397px;
  height:170px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
  resize:none;
}
#u963_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:397px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#D7D7D7;
}
#u963 {
  border-width:0px;
  position:absolute;
  left:598px;
  top:529px;
  width:397px;
  height:170px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#D7D7D7;
}
#u963 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u963_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:397px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#D7D7D7;
}
#u963.hint {
}
#u963_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:397px;
  height:170px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#D7D7D7;
}
#u963.disabled {
}
#u963_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:397px;
  height:170px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#D7D7D7;
}
#u963.hint.disabled {
}
#u964_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:356px;
  height:40px;
  background:inherit;
  background-color:rgba(0, 0, 255, 1);
  border-radius:40px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u964 {
  border-width:0px;
  position:absolute;
  left:609px;
  top:777px;
  width:356px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u964 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u964_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u965 {
  border-width:0px;
  position:absolute;
  left:609px;
  top:159px;
  width:286px;
  height:41px;
  display:flex;
  transition:none;
}
#u965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u965_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:286px;
  height:41px;
}
#u965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u966_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u966 {
  border-width:0px;
  position:absolute;
  left:601px;
  top:238px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u966 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u966_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u967_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u967 {
  border-width:0px;
  position:absolute;
  left:601px;
  top:270px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u967 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u967_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u968_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u968 {
  border-width:0px;
  position:absolute;
  left:601px;
  top:303px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u968 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u968_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u969 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u970 {
  border-width:0px;
  position:absolute;
  left:822px;
  top:238px;
  width:125px;
  height:23px;
  display:flex;
  transition:none;
}
#u970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u970_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:23px;
}
#u970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u971_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
  color:#000000;
}
#u971 {
  border-width:0px;
  position:absolute;
  left:951px;
  top:242px;
  width:39px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
  color:#000000;
}
#u971 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u971_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u972 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u973 {
  border-width:0px;
  position:absolute;
  left:822px;
  top:270px;
  width:125px;
  height:23px;
  display:flex;
  transition:none;
}
#u973 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u973_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:23px;
}
#u973_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u974_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
  color:#000000;
}
#u974 {
  border-width:0px;
  position:absolute;
  left:951px;
  top:274px;
  width:39px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
  color:#000000;
}
#u974 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u974_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u975_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:361px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u975 {
  border-width:0px;
  position:absolute;
  left:601px;
  top:104px;
  width:361px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u975 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u975_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u976_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u976 {
  border-width:0px;
  position:absolute;
  left:601px;
  top:335px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u976 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u976_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u977_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u977 {
  border-width:0px;
  position:absolute;
  left:601px;
  top:364px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u977 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u977_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u978 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u979 {
  border-width:0px;
  position:absolute;
  left:822px;
  top:303px;
  width:125px;
  height:23px;
  display:flex;
  transition:none;
}
#u979 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u979_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:23px;
}
#u979_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u980_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
  color:#000000;
}
#u980 {
  border-width:0px;
  position:absolute;
  left:951px;
  top:307px;
  width:39px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
  color:#000000;
}
#u980 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u980_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u981 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u982 {
  border-width:0px;
  position:absolute;
  left:822px;
  top:333px;
  width:125px;
  height:23px;
  display:flex;
  transition:none;
}
#u982 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u982_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:23px;
}
#u982_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u983_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
  color:#000000;
}
#u983 {
  border-width:0px;
  position:absolute;
  left:951px;
  top:337px;
  width:39px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
  color:#000000;
}
#u983 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u983_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u984 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u985 {
  border-width:0px;
  position:absolute;
  left:822px;
  top:366px;
  width:125px;
  height:23px;
  display:flex;
  transition:none;
}
#u985 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u985_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:23px;
}
#u985_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u986_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
  color:#000000;
}
#u986 {
  border-width:0px;
  position:absolute;
  left:951px;
  top:370px;
  width:39px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
  color:#000000;
}
#u986 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u986_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u987_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 0, 255, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#0000FF;
}
#u987 {
  border-width:0px;
  position:absolute;
  left:590px;
  top:411px;
  width:94px;
  height:33px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#0000FF;
}
#u987 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u987_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u988_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u988 {
  border-width:0px;
  position:absolute;
  left:731px;
  top:411px;
  width:94px;
  height:33px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u988 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u988_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u989_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:33px;
  background:inherit;
  background-color:rgba(0, 0, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
}
#u989 {
  border-width:0px;
  position:absolute;
  left:877px;
  top:411px;
  width:94px;
  height:33px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
}
#u989 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u989_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u990_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 0, 255, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#0000FF;
}
#u990 {
  border-width:0px;
  position:absolute;
  left:590px;
  top:452px;
  width:94px;
  height:33px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#0000FF;
}
#u990 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u990_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u991 {
  border-width:0px;
  position:absolute;
  left:731px;
  top:452px;
  width:92px;
  height:33px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u991 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u991_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u992_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 0, 255, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#0000FF;
}
#u992 {
  border-width:0px;
  position:absolute;
  left:879px;
  top:452px;
  width:92px;
  height:33px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#0000FF;
}
#u992 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u992_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:176px;
  height:101px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u993 {
  border-width:0px;
  position:absolute;
  left:1035px;
  top:129px;
  width:176px;
  height:101px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u993 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u993_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:176px;
  height:101px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u994 {
  border-width:0px;
  position:absolute;
  left:1027px;
  top:393px;
  width:176px;
  height:101px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u994 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u994_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u995_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:176px;
  height:67px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u995 {
  border-width:0px;
  position:absolute;
  left:1027px;
  top:553px;
  width:176px;
  height:67px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u995 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u995_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:176px;
  height:67px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u996 {
  border-width:0px;
  position:absolute;
  left:1027px;
  top:750px;
  width:176px;
  height:67px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u996 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u996_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
