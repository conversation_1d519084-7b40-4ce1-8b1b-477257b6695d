﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:678px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u0_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:386px;
  height:680px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u0 {
  border-width:0px;
  position:absolute;
  left:-224px;
  top:-3854px;
  width:386px;
  height:680px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u0 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u0_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:-174px;
  top:-3226px;
  width:26px;
  height:29px;
  display:flex;
  transition:none;
}
#u1 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:29px;
}
#u1_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:-3226px;
  width:38px;
  height:34px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:22px;
}
#u3 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u3_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:34px;
}
#u3_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:-3224px;
  width:33px;
  height:30px;
  display:flex;
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:22px;
}
#u4 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u4_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:30px;
}
#u4_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u5 {
  border-width:0px;
  position:absolute;
  left:-174px;
  top:-3197px;
  width:27px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u5 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u6_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u6 {
  border-width:0px;
  position:absolute;
  left:69px;
  top:-3192px;
  width:27px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u6 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7 {
  border-width:0px;
  position:absolute;
  left:-53px;
  top:-3226px;
  width:44px;
  height:44px;
  display:flex;
  transition:none;
  font-size:36px;
}
#u7 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:44px;
}
#u7_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u8_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:430px;
  height:806px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u8 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:15px;
  width:430px;
  height:806px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u8 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u9 {
  border-width:0px;
  position:absolute;
  left:333px;
  top:33px;
  width:103px;
  height:37px;
  display:flex;
  transition:none;
}
#u9 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u9_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:37px;
}
#u9_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u10_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:28px;
}
#u10 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:43px;
  width:85px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:28px;
}
#u10 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u10_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u11 {
  border-width:0px;
  position:absolute;
  left:384px;
  top:681px;
  width:56px;
  height:56px;
  display:flex;
  transition:none;
  font-size:30px;
  color:#FFFFFF;
}
#u11 .text {
  position:absolute;
  align-self:center;
  padding:5px 5px 5px 5px;
  box-sizing:border-box;
  width:100%;
}
#u11_img {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-2px;
  width:66px;
  height:66px;
}
#u11_img.mouseOver {
}
#u11.mouseOver {
}
#u11_img.mouseDown {
}
#u11.mouseDown {
}
#u11_img.mouseOver.mouseDown {
}
#u11.mouseOver.mouseDown {
}
#u11_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:231px;
  width:1px;
  height:46px;
  display:flex;
  transition:none;
  font-family:"MicrosoftSansSerif", "Microsoft Sans Serif", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u12 .text {
  position:absolute;
  align-self:flex-start;
  padding:18px 2px 2px 36px;
  box-sizing:border-box;
  width:100%;
}
#u12_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:46px;
}
#u12_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u14_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(16, 142, 233, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u14 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:223px;
  width:89px;
  height:46px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u14 .text {
  position:absolute;
  align-self:center;
  padding:12px 16px 12px 16px;
  box-sizing:border-box;
  width:100%;
}
#u14_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u15_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(24, 144, 255, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1890FF;
  text-align:left;
  line-height:22px;
}
#u15 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:223px;
  width:61px;
  height:46px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#1890FF;
  text-align:left;
  line-height:22px;
}
#u15 .text {
  position:absolute;
  align-self:center;
  padding:12px 16px 12px 16px;
  box-sizing:border-box;
  width:100%;
}
#u15_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u16_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(16, 142, 233, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u16 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:223px;
  width:70px;
  height:46px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u16 .text {
  position:absolute;
  align-self:center;
  padding:12px 16px 12px 16px;
  box-sizing:border-box;
  width:100%;
}
#u16_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u17_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(16, 142, 233, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u17 {
  border-width:0px;
  position:absolute;
  left:254px;
  top:223px;
  width:70px;
  height:46px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u17 .text {
  position:absolute;
  align-self:center;
  padding:12px 16px 12px 16px;
  box-sizing:border-box;
  width:100%;
}
#u17_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u18_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(16, 142, 233, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u18 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:223px;
  width:70px;
  height:46px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.6470588235294118);
  text-align:left;
  line-height:22px;
}
#u18 .text {
  position:absolute;
  align-self:center;
  padding:12px 16px 12px 16px;
  box-sizing:border-box;
  width:100%;
}
#u18_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u19 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:89px;
  width:401px;
  height:121px;
  display:flex;
  transition:none;
}
#u19 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u19_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:401px;
  height:121px;
}
#u19_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u20 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u21 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:285px;
  width:80px;
  height:80px;
  display:flex;
  transition:none;
}
#u21 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u21_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:80px;
}
#u21_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u22_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u22 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:307px;
  width:31px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u22 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u22_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u23_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:18px;
  background:inherit;
  background-color:rgba(250, 205, 145, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u23 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:307px;
  width:76px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u23 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u23_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u24_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u24 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:338px;
  width:153px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u24 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u24_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u25_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:18px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u25 {
  border-width:0px;
  position:absolute;
  left:366px;
  top:338px;
  width:70px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u25 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u25_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u26_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:396px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u26 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:370px;
  width:396px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u26 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u26_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u27_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:18px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u27 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:419px;
  width:78px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u27 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u27_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u28_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:18px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:149px;
  top:419px;
  width:78px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u28 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u28_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u29_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:18px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u29 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:419px;
  width:78px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u29 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u29_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:454px;
  width:424px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(0.13577154553748114deg);
  -moz-transform:rotate(0.13577154553748114deg);
  -ms-transform:rotate(0.13577154553748114deg);
  transform:rotate(0.13577154553748114deg);
  transition:none;
}
#u30 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u30_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:2px;
}
#u30_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:18px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:358px;
  top:419px;
  width:78px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u31 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u31_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u32_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:18px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u32 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:608px;
  width:78px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u32 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u32_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u33_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:18px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:608px;
  width:78px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u33 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u33_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u34_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:18px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:259px;
  top:608px;
  width:78px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u34 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u34_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u35_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:18px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:353px;
  top:608px;
  width:78px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u35 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u35_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:472px;
  width:80px;
  height:80px;
  display:flex;
  transition:none;
}
#u36 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u36_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:80px;
}
#u36_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u37_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:494px;
  width:31px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u37 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u37_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u38_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:18px;
  background:inherit;
  background-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:494px;
  width:76px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u38 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u38_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u39_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u39 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:525px;
  width:153px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u39 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u39_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u40_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:18px;
  background:inherit;
  background-color:rgba(129, 211, 248, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:370px;
  top:525px;
  width:70px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u40 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u40_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u41_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:396px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u41 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:557px;
  width:396px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u41 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u41_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:641px;
  width:424px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(0.13577154553748114deg);
  -moz-transform:rotate(0.13577154553748114deg);
  -ms-transform:rotate(0.13577154553748114deg);
  transform:rotate(0.13577154553748114deg);
  transition:none;
}
#u42 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u42_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:2px;
}
#u42_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u43 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u44 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:760px;
  width:34px;
  height:29px;
  display:flex;
  transition:none;
}
#u44 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u44_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:29px;
}
#u44_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u45_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u45 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:794px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u45 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u45_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u46 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u47 {
  border-width:0px;
  position:absolute;
  left:370px;
  top:760px;
  width:29px;
  height:34px;
  display:flex;
  transition:none;
}
#u47 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u47_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:34px;
}
#u47_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u48_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u48 {
  border-width:0px;
  position:absolute;
  left:370px;
  top:796px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u48 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u48_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u49_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:174px;
  height:121px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
}
#u49 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:89px;
  width:174px;
  height:121px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u49 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u49_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u50_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:359px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u50 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:223px;
  width:450px;
  height:359px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u50 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u50_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u51_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:51px;
  background:inherit;
  background-color:rgba(255, 184, 217, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u51 {
  border-width:0px;
  position:absolute;
  left:476px;
  top:767px;
  width:150px;
  height:51px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u51 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u51_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u52 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:285px;
  width:424px;
  height:158px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u53_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:317px;
  height:46px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
}
#u53 {
  border-width:0px;
  position:absolute;
  left:476px;
  top:686px;
  width:317px;
  height:46px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u53 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u53_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u54_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:124px;
  background:inherit;
  background-color:rgba(208, 225, 125, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u54 {
  border-width:0px;
  position:absolute;
  left:643px;
  top:89px;
  width:150px;
  height:124px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u54 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u54_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
