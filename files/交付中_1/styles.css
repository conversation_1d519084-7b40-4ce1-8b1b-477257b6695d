﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-15px;
  width:726px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1568_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:456px;
  height:1566px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1568 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:4px;
  width:456px;
  height:1566px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1568 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1568_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1569_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  height:109px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1569 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:136px;
  width:436px;
  height:109px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1569 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1569_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1570_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:436px;
  height:18px;
  background:inherit;
  background-color:rgba(51, 51, 51, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#FFFFFF;
}
#u1570 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:136px;
  width:436px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#FFFFFF;
}
#u1570 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1570_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1571_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:28px;
}
#u1571 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:30px;
  width:34px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:28px;
}
#u1571 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1571_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1572_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u1572 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:38px;
  width:81px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u1572 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1572_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1573 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:32px;
  width:93px;
  height:30px;
  display:flex;
  transition:none;
}
#u1573 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1573_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:30px;
}
#u1573_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1574 {
  border-width:0px;
  position:absolute;
  left:1630px;
  top:269px;
  width:360px;
  height:266px;
  visibility:hidden;
}
#u1574_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:266px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1574_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1575_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:266px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1575 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:0px;
  width:358px;
  height:266px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1575 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1575_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1576_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1576 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:19px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1576 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1576_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1577_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u1577 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:96px;
  width:267px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u1577 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1577_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1578_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1578 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:209px;
  width:114px;
  height:37px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1578 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1578_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1579_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:36px;
  background:inherit;
  background-color:rgba(2, 167, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1579 {
  border-width:0px;
  position:absolute;
  left:204px;
  top:207px;
  width:117px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1579 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1579_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1574_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:360px;
  height:266px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1574_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1580_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:266px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1580 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:0px;
  width:358px;
  height:266px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1580 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1580_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1581_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1581 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:19px;
  width:33px;
  height:22px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial-BoldMT", "Arial Bold", "Arial", sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1581 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1581_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1582_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u1582 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:79px;
  width:267px;
  height:27px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u1582 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1582_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1583_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1583 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:209px;
  width:114px;
  height:37px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1583 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1583_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1584_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:36px;
  background:inherit;
  background-color:rgba(30, 152, 215, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u1584 {
  border-width:0px;
  position:absolute;
  left:204px;
  top:207px;
  width:117px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1584 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1584_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1585_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:center;
}
#u1585 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:113px;
  width:267px;
  height:27px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:center;
}
#u1585 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1585_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1586_input {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1586_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1586_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1586_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1586_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1586 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:108px;
  width:98px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u1586 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1586_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1586.hint {
}
#u1586_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1586.disabled {
}
#u1586_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u1586.hint.disabled {
}
#u1587_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:13px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:12px;
  color:#D9001B;
}
#u1587 {
  border-width:0px;
  position:absolute;
  left:103px;
  top:149px;
  width:145px;
  height:13px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:12px;
  color:#D9001B;
}
#u1587 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1587_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1588 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1589_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1589 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:175px;
  width:66px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1589 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1589_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1590_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:20px;
  background:inherit;
  background-color:rgba(44, 140, 240, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  text-align:left;
  line-height:20px;
}
#u1590 {
  border-width:0px;
  position:absolute;
  left:94px;
  top:175px;
  width:41px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  text-align:left;
  line-height:20px;
}
#u1590 .text {
  position:absolute;
  align-self:center;
  padding:0px 8px 0px 8px;
  box-sizing:border-box;
  width:100%;
}
#u1590_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1591 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1592_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1592 {
  border-width:0px;
  position:absolute;
  left:343px;
  top:175px;
  width:97px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1592 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1592_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1593 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1594_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1594 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:212px;
  width:85px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1594 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1594_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1595 {
  border-width:0px;
  position:absolute;
  left:254px;
  top:113px;
  width:54px;
  height:3px;
  display:flex;
  -webkit-transform:rotate(0.4917410174421613deg);
  -moz-transform:rotate(0.4917410174421613deg);
  -ms-transform:rotate(0.4917410174421613deg);
  transform:rotate(0.4917410174421613deg);
  transition:none;
}
#u1595 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1595_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:58px;
  height:7px;
}
#u1595_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1596 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1597_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1597 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:210px;
  width:79px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1597 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1597_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1598 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:135px;
  width:434px;
  height:119px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u1599_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1599 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:90px;
  width:74px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1599 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1599_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1600_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1600 {
  border-width:0px;
  position:absolute;
  left:250px;
  top:90px;
  width:71px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1600 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1600_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1601_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1601 {
  border-width:0px;
  position:absolute;
  left:355px;
  top:90px;
  width:74px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1601 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1601_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1602_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1602 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:90px;
  width:74px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1602 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1602_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1603_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:242px;
  height:96px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1603 {
  border-width:0px;
  position:absolute;
  left:499px;
  top:137px;
  width:242px;
  height:96px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1603 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1603_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
