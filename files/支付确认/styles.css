﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-36px;
  width:1146px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u538 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:40px;
  width:568px;
  height:1289px;
  display:flex;
  transition:none;
}
#u538 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u538_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:568px;
  height:1289px;
}
#u538_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u539_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:541px;
  height:26px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u539 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:306px;
  width:541px;
  height:26px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u539 .text {
  position:absolute;
  align-self:flex-end;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u539_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u540_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:51px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:36px;
}
#u540 {
  border-width:0px;
  position:absolute;
  left:276px;
  top:255px;
  width:168px;
  height:51px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:36px;
}
#u540 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u540_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u541_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:534px;
  height:69px;
  background:inherit;
  background-color:rgba(30, 152, 215, 1);
  border-radius:70px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:28px;
}
#u541 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:1222px;
  width:534px;
  height:69px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:28px;
}
#u541 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u541_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u542 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:40px;
  width:509px;
  height:1289px;
  display:flex;
  transition:none;
}
#u542 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u542_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:509px;
  height:1289px;
}
#u542_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u543_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:227px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u543 {
  border-width:0px;
  position:absolute;
  left:833px;
  top:418px;
  width:227px;
  height:34px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u543 .text {
  position:absolute;
  align-self:flex-end;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u543_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
