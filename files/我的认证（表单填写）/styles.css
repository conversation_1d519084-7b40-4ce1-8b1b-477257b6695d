﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-16px;
  width:919px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2143_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:456px;
  height:1453px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u2143 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:19px;
  width:456px;
  height:1453px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u2143 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2143_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2144_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:28px;
}
#u2144 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:30px;
  width:34px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:28px;
}
#u2144 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2144_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2145_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u2145 {
  border-width:0px;
  position:absolute;
  left:197px;
  top:34px;
  width:81px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u2145 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2145_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2146 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:32px;
  width:93px;
  height:30px;
  display:flex;
  transition:none;
}
#u2146 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2146_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:30px;
}
#u2146_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2147 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2148_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2148 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:140px;
  width:27px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2148 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2148_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2149 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:174px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2149 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2149_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2149_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2150 {
  border-width:0px;
  position:absolute;
  left:389px;
  top:124px;
  width:50px;
  height:50px;
  display:flex;
  transition:none;
}
#u2150 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2150_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u2150_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2151_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:429px;
  height:40px;
  background:inherit;
  background-color:rgba(30, 152, 215, 1);
  border-radius:38px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2151 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:1416px;
  width:429px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2151 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2151_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2152 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2153_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2153 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:203px;
  width:53px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2153 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2153_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2154 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:229px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2154 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2154_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2154_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2155_input {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2155_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2155_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2155_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2155_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2155 {
  border-width:0px;
  position:absolute;
  left:268px;
  top:202px;
  width:177px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:right;
}
#u2155 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2155_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2155.hint {
}
#u2155_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2155.disabled {
}
#u2155_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2155.hint.disabled {
}
#u2156 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:259px;
  width:1px;
  height:157px;
  display:flex;
  transition:none;
}
#u2156 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2156_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:157px;
}
#u2156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2157 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:416px;
  width:1px;
  height:155px;
  display:flex;
  transition:none;
}
#u2157 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2157_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:155px;
}
#u2157_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2158 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2159_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2159 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:295px;
  width:40px;
  height:16px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2159 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2159_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2160 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:321px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2160 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2160_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2160_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2161_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
}
#u2161 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:295px;
  width:109px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#AAAAAA;
}
#u2161 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2161_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:278px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2162 {
  border-width:0px;
  position:absolute;
  left:472px;
  top:239px;
  width:278px;
  height:35px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2162 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2162_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2163 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2164 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2165_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2165 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:338px;
  width:79px;
  height:16px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2165 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2165_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2166 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:364px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2166 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2166_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2166_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2167_input {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2167_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2167_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2167_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2167_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2167 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:335px;
  width:177px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:right;
}
#u2167 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2167_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2167.hint {
}
#u2167_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2167.disabled {
}
#u2167_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2167.hint.disabled {
}
#u2168 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2169 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2170_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2170 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:252px;
  width:40px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2170 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2170_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2171 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:278px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2171 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2171_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2171_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2172_input {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2172_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2172_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2172_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2172_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2172 {
  border-width:0px;
  position:absolute;
  left:268px;
  top:249px;
  width:177px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:right;
}
#u2172 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2172_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2172.hint {
}
#u2172_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2172.disabled {
}
#u2172_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2172.hint.disabled {
}
#u2173 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2174 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2175 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2176_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2176 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:383px;
  width:92px;
  height:16px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2176 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2176_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2177 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:409px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2177 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2177_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2177_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2178_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:20px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
  text-align:left;
}
#u2178 {
  border-width:0px;
  position:absolute;
  left:217px;
  top:383px;
  width:41px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
  text-align:left;
}
#u2178 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2178_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:20px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
  text-align:left;
}
#u2178.mouseOver {
  opacity:0.8;
}
#u2178_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2179_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:20px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
  text-align:left;
}
#u2179 {
  border-width:0px;
  position:absolute;
  left:272px;
  top:383px;
  width:41px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
  text-align:left;
}
#u2179 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2179_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:20px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
  text-align:left;
}
#u2179.mouseOver {
  opacity:0.8;
}
#u2179_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2180_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:20px;
  background:inherit;
  background-color:rgba(44, 140, 240, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  text-align:left;
  line-height:20px;
}
#u2180 {
  border-width:0px;
  position:absolute;
  left:380px;
  top:383px;
  width:65px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  text-align:left;
  line-height:20px;
}
#u2180 .text {
  position:absolute;
  align-self:center;
  padding:0px 8px 0px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2180_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2181_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:20px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
  text-align:left;
}
#u2181 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:383px;
  width:41px;
  height:20px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
  text-align:left;
}
#u2181 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2181_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:20px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
  text-align:left;
}
#u2181.mouseOver {
  opacity:0.8;
}
#u2181_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2182 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2183 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2184_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2184 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:429px;
  width:92px;
  height:16px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2184 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2184_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2185 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:455px;
  width:410px;
  height:1px;
  display:flex;
  transition:none;
}
#u2185 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2185_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:411px;
  height:2px;
}
#u2185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2186_input {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2186_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2186_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2186_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"ArialMT", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:right;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2186_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2186 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:426px;
  width:177px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  text-align:right;
}
#u2186 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2186_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2186.hint {
}
#u2186_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2186.disabled {
}
#u2186_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  text-align:right;
}
#u2186.hint.disabled {
}
#u2187_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:305px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2187 {
  border-width:0px;
  position:absolute;
  left:473px;
  top:330px;
  width:305px;
  height:35px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2187 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2187_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2188 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2189_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2189 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:643px;
  width:73px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2189 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2189_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2190_input {
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#7F7F7F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2190_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2190_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#7F7F7F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2190_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2190_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2190 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:687px;
  width:410px;
  height:142px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2190 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2190_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2190.hint {
}
#u2190_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2190.disabled {
}
#u2190_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2190.hint.disabled {
}
#u2191_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:391px;
  height:72px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2191 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:702px;
  width:391px;
  height:72px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2191 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2191_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2192_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:326px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2192 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:650px;
  width:326px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2192 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2192_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2193_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Semibold", "苹方-简 Semibold", "苹方-简", sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:18px;
}
#u2193 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:99px;
  width:73px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Semibold", "苹方-简 Semibold", "苹方-简", sans-serif;
  font-weight:650;
  font-style:normal;
  font-size:18px;
}
#u2193 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2193_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2194 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2195_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2195 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:856px;
  width:73px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2195 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2195_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2196_input {
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#7F7F7F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2196_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2196_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#7F7F7F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2196_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  padding:2px 2px 2px 2px;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2196_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2196 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:898px;
  width:410px;
  height:142px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2196 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2196_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2196.hint {
}
#u2196_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2196.disabled {
}
#u2196_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:142px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2196.hint.disabled {
}
#u2197_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:394px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2197 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:915px;
  width:394px;
  height:36px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2197 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2197_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2198 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2199 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2200_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2200 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:1080px;
  width:73px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2200 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2200_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2201_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:90px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:48px;
}
#u2201 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:1122px;
  width:173px;
  height:90px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:48px;
}
#u2201 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2201_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2202 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2203_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:90px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:48px;
}
#u2203 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:1122px;
  width:173px;
  height:90px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:48px;
}
#u2203 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2203_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2204_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2204 {
  border-width:0px;
  position:absolute;
  left:78px;
  top:1186px;
  width:79px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2204 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2204_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2205 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2206_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:90px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:48px;
}
#u2206 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:1229px;
  width:173px;
  height:90px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:48px;
}
#u2206 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2206_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2207_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2207 {
  border-width:0px;
  position:absolute;
  left:47px;
  top:1293px;
  width:157px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2207 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2207_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2208_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:274px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2208 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:1084px;
  width:274px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2208 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2208_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2209 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2210_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:90px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:48px;
}
#u2210 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:1229px;
  width:173px;
  height:90px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:48px;
}
#u2210 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2211_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2211 {
  border-width:0px;
  position:absolute;
  left:258px;
  top:1293px;
  width:157px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2211 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2211_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2212_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2212 {
  border-width:0px;
  position:absolute;
  left:293px;
  top:1191px;
  width:79px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2212 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2212_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2213_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:269px;
  height:109px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2213 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:1363px;
  width:269px;
  height:109px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2213 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2214_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:341px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 184, 217, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2214 {
  border-width:0px;
  position:absolute;
  left:499px;
  top:746px;
  width:341px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2214 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2214_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2215_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:240px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2215 {
  border-width:0px;
  position:absolute;
  left:472px;
  top:132px;
  width:240px;
  height:35px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2215 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2215_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2216_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:240px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2216 {
  border-width:0px;
  position:absolute;
  left:472px;
  top:192px;
  width:240px;
  height:35px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2216 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2216_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2217_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:269px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2217 {
  border-width:0px;
  position:absolute;
  left:473px;
  top:416px;
  width:269px;
  height:35px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2217 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2217_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2218_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 184, 217, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2218 {
  border-width:0px;
  position:absolute;
  left:785px;
  top:1400px;
  width:150px;
  height:42px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2218 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2218_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2219 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2220_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2220 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:494px;
  width:73px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u2220 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2220_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2221_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2221 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:534px;
  width:74px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2221 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2221_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2221.mouseOver {
  opacity:0.8;
}
#u2221_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2222_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:28px;
  background:inherit;
  background-color:rgba(44, 140, 240, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:20px;
}
#u2222 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:534px;
  width:96px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:20px;
}
#u2222 .text {
  position:absolute;
  align-self:center;
  padding:0px 8px 0px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2222_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2223_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2223 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:534px;
  width:74px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2223 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2223_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2223.mouseOver {
  opacity:0.8;
}
#u2223_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2224 {
  border-width:0px;
  position:absolute;
  left:371px;
  top:534px;
  width:74px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2224 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2224_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2224.mouseOver {
  opacity:0.8;
}
#u2224_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2225_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2225 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:574px;
  width:74px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2225 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2225_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2225.mouseOver {
  opacity:0.8;
}
#u2225_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2226_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:28px;
  background:inherit;
  background-color:rgba(44, 140, 240, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:20px;
}
#u2226 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:574px;
  width:96px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:20px;
}
#u2226 .text {
  position:absolute;
  align-self:center;
  padding:0px 8px 0px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2226_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2227_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2227 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:574px;
  width:74px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2227 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2227_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2227.mouseOver {
  opacity:0.8;
}
#u2227_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2228_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2228 {
  border-width:0px;
  position:absolute;
  left:371px;
  top:574px;
  width:74px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2228 .text {
  position:absolute;
  align-self:center;
  padding:3px 8px 3px 8px;
  box-sizing:border-box;
  width:100%;
}
#u2228_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(220, 222, 226, 1);
  border-radius:3px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#808695;
}
#u2228.mouseOver {
  opacity:0.8;
}
#u2228_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2229_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2229 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:498px;
  width:87px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#7F7F7F;
}
#u2229 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2229_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2230_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:313px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2230 {
  border-width:0px;
  position:absolute;
  left:472px;
  top:534px;
  width:313px;
  height:54px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2230 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2230_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2231_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:278px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border-radius:0px;
  filter:drop-shadow(5px 5px 2.5px rgba(0, 0, 0, 0.2));
  transition:none;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2231 {
  border-width:0px;
  position:absolute;
  left:472px;
  top:287px;
  width:278px;
  height:35px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "苹方-简", sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2231 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
