﻿<!DOCTYPE html>
<html>
  <head>
    <title>提现管理</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/提现管理/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/提现管理/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u2055" class="ax_default box_1 transition notrs">
        <div id="u2055_div" class=""></div>
        <div id="u2055_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2056" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2057" class="ax_default label transition notrs">
        <div id="u2057_div" class=""></div>
        <div id="u2057_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2058" class="ax_default label transition notrs">
        <div id="u2058_div" class=""></div>
        <div id="u2058_text" class="text ">
          <p><span>提现</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2059" class="ax_default _图片 transition notrs">
        <img id="u2059_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u2059_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u2060" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u2060_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u2060_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2061" class="ax_default box_1 transition notrs">
              <div id="u2061_div" class=""></div>
              <div id="u2061_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2062" class="ax_default label transition notrs">
              <div id="u2062_div" class=""></div>
              <div id="u2062_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2063" class="ax_default label transition notrs">
              <div id="u2063_div" class=""></div>
              <div id="u2063_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2064" class="ax_default button transition notrs">
              <div id="u2064_div" class=""></div>
              <div id="u2064_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2065" class="ax_default primary_button transition notrs">
              <div id="u2065_div" class=""></div>
              <div id="u2065_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u2060_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u2060_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2066" class="ax_default box_1 transition notrs">
              <div id="u2066_div" class=""></div>
              <div id="u2066_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2067" class="ax_default label transition notrs">
              <div id="u2067_div" class=""></div>
              <div id="u2067_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2068" class="ax_default label transition notrs">
              <div id="u2068_div" class=""></div>
              <div id="u2068_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2069" class="ax_default button transition notrs">
              <div id="u2069_div" class=""></div>
              <div id="u2069_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2070" class="ax_default primary_button transition notrs">
              <div id="u2070_div" class=""></div>
              <div id="u2070_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2071" class="ax_default label transition notrs">
              <div id="u2071_div" class=""></div>
              <div id="u2071_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2072" class="ax_default text_field transition notrs">
              <div id="u2072_div" class=""></div>
              <input id="u2072_input" type="text" value="" class="u2072_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2073" class="ax_default label transition notrs">
              <div id="u2073_div" class=""></div>
              <div id="u2073_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2074" class="ax_default box_1 transition notrs">
        <div id="u2074_div" class=""></div>
        <div id="u2074_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2075" class="ax_default label transition notrs">
        <div id="u2075_div" class=""></div>
        <div id="u2075_text" class="text ">
          <p><span>未提现：1.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2076" class="ax_default label transition notrs">
        <div id="u2076_div" class=""></div>
        <div id="u2076_text" class="text ">
          <p><span>冻结中：100</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2077" class="ax_default label transition notrs">
        <div id="u2077_div" class=""></div>
        <div id="u2077_text" class="text ">
          <p><span>可提现：900</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u2078" class="ax_default line transition notrs">
        <svg data="images/我的钱包/u2038.svg" id="u2078_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -40 -163 )">
    <path d="M 0 0.5  L 399 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0.12941176470588237)" fill="none" transform="matrix(1 0 0 1 40 163 )" class="stroke" />
  </g>
        </svg>
        <div id="u2078_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2079" class="ax_default label transition notrs">
        <div id="u2079_div" class=""></div>
        <div id="u2079_text" class="text ">
          <p><span>提现金额</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2080" class="ax_default text_field transition notrs">
        <div id="u2080_div" class=""></div>
        <input id="u2080_input" type="number" value="" class="u2080_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2081" class="ax_default primary_button transition notrs">
        <div id="u2081_div" class=""></div>
        <div id="u2081_text" class="text ">
          <p><span>确认提交</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2082" class="ax_default label transition notrs">
        <div id="u2082_div" class=""></div>
        <div id="u2082_text" class="text ">
          <p><span>查看冻结订单&gt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2083" class="ax_default label transition notrs">
        <div id="u2083_div" class=""></div>
        <div id="u2083_text" class="text ">
          <p><span>注释：</span></p><p><span>1、佣金费用=提现费用*10%</span></p><p><span>2、冻结中的订单需要在订单验收后14日之后方可提现。</span></p><p><span>3、提现一般5个工作日导致。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2084" class="ax_default sticky_1 transition notrs">
        <div id="u2084_div" class=""></div>
        <div id="u2084_text" class="text ">
          <p><span>冻结中的金额：订单完成时间未到14天的订单金额的累加</span></p><p><span>可提现金额：订单完成时间超过14天的订单金额累加-已申请提现金额</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2085" class="ax_default sticky_1 transition notrs">
        <div id="u2085_div" class=""></div>
        <div id="u2085_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">提现金额</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">1、提现金额</span><span style="font-family:&quot;Helvetica&quot;, sans-serif;">≤</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">可提现金额</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">2、只允许输入整数，不允许输入小数</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">实际到账</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">实际到账更具提现金额实时计算</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">实际到账=提现金额*0.9</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2086" class="ax_default label transition notrs">
        <div id="u2086_div" class=""></div>
        <div id="u2086_text" class="text ">
          <p><span>实际到账</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2087" class="ax_default label transition notrs">
        <div id="u2087_div" class=""></div>
        <div id="u2087_text" class="text ">
          <p><span>450元</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2088" class="ax_default label transition notrs">
        <div id="u2088_div" class=""></div>
        <div id="u2088_text" class="text ">
          <p><span>实际到账=提现金额*0.9</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
