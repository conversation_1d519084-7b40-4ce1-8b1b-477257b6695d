﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情 (基本信息）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情__基本信息）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情__基本信息）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u602" class="ax_default box_1 transition notrs">
        <div id="u602_div" class=""></div>
        <div id="u602_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u603" class="ax_default label transition notrs">
        <div id="u603_div" class=""></div>
        <div id="u603_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u604" class="ax_default label transition notrs">
        <div id="u604_div" class=""></div>
        <div id="u604_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u605" class="ax_default _图片 transition notrs">
        <img id="u605_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u605_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u606" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u606_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u606_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u607" class="ax_default box_1 transition notrs">
              <div id="u607_div" class=""></div>
              <div id="u607_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u608" class="ax_default label transition notrs">
              <div id="u608_div" class=""></div>
              <div id="u608_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u609" class="ax_default label transition notrs">
              <div id="u609_div" class=""></div>
              <div id="u609_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u610" class="ax_default button transition notrs">
              <div id="u610_div" class=""></div>
              <div id="u610_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u611" class="ax_default primary_button transition notrs">
              <div id="u611_div" class=""></div>
              <div id="u611_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u606_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u606_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u612" class="ax_default box_1 transition notrs">
              <div id="u612_div" class=""></div>
              <div id="u612_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u613" class="ax_default label transition notrs">
              <div id="u613_div" class=""></div>
              <div id="u613_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u614" class="ax_default label transition notrs">
              <div id="u614_div" class=""></div>
              <div id="u614_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u615" class="ax_default button transition notrs">
              <div id="u615_div" class=""></div>
              <div id="u615_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u616" class="ax_default primary_button transition notrs">
              <div id="u616_div" class=""></div>
              <div id="u616_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u617" class="ax_default label transition notrs">
              <div id="u617_div" class=""></div>
              <div id="u617_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u618" class="ax_default text_field transition notrs">
              <div id="u618_div" class=""></div>
              <input id="u618_input" type="text" value="" class="u618_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u619" class="ax_default label transition notrs">
              <div id="u619_div" class=""></div>
              <div id="u619_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u620" class="ax_default" data-left="17.999483828313885" data-top="207" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u621" class="ax_default" data-left="36" data-top="209" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u622" class="ax_default label transition notrs">
            <div id="u622_div" class=""></div>
            <div id="u622_text" class="text ">
              <p><span>辅导项目</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u623" class="ax_default" data-left="433" data-top="207" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u624" class="ax_default label transition notrs">
            <div id="u624_div" class=""></div>
            <div id="u624_text" class="text ">
              <p><span>作业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u625" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u625_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u625_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u626" class="ax_default" data-left="16.999483828313878" data-top="126" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u627" class="ax_default" data-left="35" data-top="126" data-width="40" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u628" class="ax_default label transition notrs">
            <div id="u628_div" class=""></div>
            <div id="u628_text" class="text ">
              <p><span>订单号</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u629" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u629_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u629_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u630" class="ax_default" data-left="363" data-top="126" data-width="96" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u631" class="ax_default label transition notrs">
            <div id="u631_div" class=""></div>
            <div id="u631_text" class="text ">
              <p><span>JS13455555333</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u632" class="ax_default" data-left="16.999483828313867" data-top="168" data-width="453.00103234337223" data-height="29.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u633" class="ax_default" data-left="35" data-top="169" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u634" class="ax_default label transition notrs">
            <div id="u634_div" class=""></div>
            <div id="u634_text" class="text ">
              <p><span>发布时间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u635" class="ax_default" data-left="358" data-top="168" data-width="112" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u636" class="ax_default label transition notrs">
            <div id="u636_div" class=""></div>
            <div id="u636_text" class="text ">
              <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp;2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u637" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u637_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u637_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u638" class="ax_default" data-left="17.999483828313878" data-top="249" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u639" class="ax_default" data-left="36" data-top="251" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u640" class="ax_default label transition notrs">
            <div id="u640_div" class=""></div>
            <div id="u640_text" class="text ">
              <p><span>学习阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u641" class="ax_default" data-left="433" data-top="249" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u642" class="ax_default label transition notrs">
            <div id="u642_div" class=""></div>
            <div id="u642_text" class="text ">
              <p><span>本科</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u643" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u643_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u643_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u644" class="ax_default" data-left="17.99948382831388" data-top="284" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u645" class="ax_default" data-left="36" data-top="286" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u646" class="ax_default label transition notrs">
            <div id="u646_div" class=""></div>
            <div id="u646_text" class="text ">
              <p><span>学习位置</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u647" class="ax_default" data-left="433" data-top="284" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u648" class="ax_default label transition notrs">
            <div id="u648_div" class=""></div>
            <div id="u648_text" class="text ">
              <p><span>国内</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u649" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u649_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u649_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u650" class="ax_default" data-left="17.999483828313874" data-top="325" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u651" class="ax_default" data-left="36" data-top="327" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u652" class="ax_default label transition notrs">
            <div id="u652_div" class=""></div>
            <div id="u652_text" class="text ">
              <p><span>学习专业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u653" class="ax_default" data-left="433" data-top="325" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u654" class="ax_default label transition notrs">
            <div id="u654_div" class=""></div>
            <div id="u654_text" class="text ">
              <p><span>会计</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u655" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u655_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u655_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u656" class="ax_default" data-left="17.999483828313863" data-top="366" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u657" class="ax_default" data-left="36" data-top="368" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u658" class="ax_default label transition notrs">
            <div id="u658_div" class=""></div>
            <div id="u658_text" class="text ">
              <p><span>预算区间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u659" class="ax_default" data-left="380" data-top="366" data-width="80" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u660" class="ax_default label transition notrs">
            <div id="u660_div" class=""></div>
            <div id="u660_text" class="text ">
              <p><span>1000-2000元</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u661" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u661_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u661_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u662" class="ax_default" data-left="17.999483828313874" data-top="527" data-width="453.00103234337223" data-height="90" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u663" class="ax_default" data-left="36" data-top="527" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u664" class="ax_default label transition notrs">
            <div id="u664_div" class=""></div>
            <div id="u664_text" class="text ">
              <p><span>需求描述</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u665" class="ax_default label transition notrs">
          <div id="u665_div" class=""></div>
          <div id="u665_text" class="text ">
            <p><span>想要找一个辅导论文的老师，想要找一个辅导论文的老师想要找一个</span></p><p><span>辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想</span></p><p><span>要找一个辅导论文的老师</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u666" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u666_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u666_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u667" class="ax_default" data-left="17.999483828313878" data-top="407" data-width="453.0010323433723" data-height="28.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u668" class="ax_default" data-left="36" data-top="407" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u669" class="ax_default label transition notrs">
            <div id="u669_div" class=""></div>
            <div id="u669_text" class="text ">
              <p><span>老师要求</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u670" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
        </div>

        <!-- Unnamed (线段) -->
        <div id="u671" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u671_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u671_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u672" class="ax_default" data-left="36" data-top="449" data-width="424" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u673" class="ax_default label transition notrs">
          <div id="u673_div" class=""></div>
          <div id="u673_text" class="text ">
            <p><span>老师情况</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u674" class="ax_default shape transition notrs">
          <div id="u674_div" class=""></div>
          <div id="u674_text" class="text ">
            <p><span>博士</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u675" class="ax_default" data-left="36" data-top="483" data-width="424" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u676" class="ax_default label transition notrs">
          <div id="u676_div" class=""></div>
          <div id="u676_text" class="text ">
            <p><span>是否留学</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u677" class="ax_default box_1 transition notrs">
          <div id="u677_div" class=""></div>
          <div id="u677_text" class="text ">
            <p><span>不要求</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u678" class="ax_default label transition notrs">
        <div id="u678_div" class=""></div>
        <div id="u678_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u679" class="ax_default label transition notrs">
        <div id="u679_div" class=""></div>
        <div id="u679_text" class="text ">
          <p><span>交付信息</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u680" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u680_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u680_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u681" class="ax_default" data-left="17.999483828313885" data-top="635" data-width="453.0010323433723" data-height="90" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u682" class="ax_default label transition notrs">
          <div id="u682_div" class=""></div>
          <div id="u682_text" class="text ">
            <p><span>希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，</span></p><p><span>有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热</span></p><p><span>情专业，有耐心。</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u683" class="ax_default" data-left="30" data-top="635" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u684" class="ax_default label transition notrs">
            <div id="u684_div" class=""></div>
            <div id="u684_text" class="text ">
              <p><span>其他说明</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u685" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u685_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u685_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
