﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单列表（已拒绝）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单列表（已拒绝）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单列表（已拒绝）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1448" class="ax_default box_1 transition notrs">
        <div id="u1448_div" class=""></div>
        <div id="u1448_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1449" class="ax_default box_1 transition notrs">
        <div id="u1449_div" class=""></div>
        <div id="u1449_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1450" class="ax_default label transition notrs">
        <div id="u1450_div" class=""></div>
        <div id="u1450_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">&nbsp; 订单号：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">FD13455555333&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1451" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1452" class="ax_default label transition notrs">
        <div id="u1452_div" class=""></div>
        <div id="u1452_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1453" class="ax_default label transition notrs">
        <div id="u1453_div" class=""></div>
        <div id="u1453_text" class="text ">
          <p><span>我的订单</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1454" class="ax_default _图片 transition notrs">
        <img id="u1454_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1454_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1455" class="ax_default line transition notrs">
        <svg data="images/订单列表（投递中）/u1418.svg" id="u1455_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -32.5 -110.5 )">
    <path d="M 0 1.5  L 65 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 34 112 )" class="stroke" />
  </g>
        </svg>
        <div id="u1455_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1456" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1456_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1456_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1457" class="ax_default box_1 transition notrs">
              <div id="u1457_div" class=""></div>
              <div id="u1457_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1458" class="ax_default label transition notrs">
              <div id="u1458_div" class=""></div>
              <div id="u1458_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1459" class="ax_default label transition notrs">
              <div id="u1459_div" class=""></div>
              <div id="u1459_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1460" class="ax_default button transition notrs">
              <div id="u1460_div" class=""></div>
              <div id="u1460_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1461" class="ax_default primary_button transition notrs">
              <div id="u1461_div" class=""></div>
              <div id="u1461_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1456_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1456_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1462" class="ax_default box_1 transition notrs">
              <div id="u1462_div" class=""></div>
              <div id="u1462_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1463" class="ax_default label transition notrs">
              <div id="u1463_div" class=""></div>
              <div id="u1463_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1464" class="ax_default label transition notrs">
              <div id="u1464_div" class=""></div>
              <div id="u1464_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1465" class="ax_default button transition notrs">
              <div id="u1465_div" class=""></div>
              <div id="u1465_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1466" class="ax_default primary_button transition notrs">
              <div id="u1466_div" class=""></div>
              <div id="u1466_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1467" class="ax_default label transition notrs">
              <div id="u1467_div" class=""></div>
              <div id="u1467_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1468" class="ax_default text_field transition notrs">
              <div id="u1468_div" class=""></div>
              <input id="u1468_input" type="text" value="" class="u1468_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1469" class="ax_default label transition notrs">
              <div id="u1469_div" class=""></div>
              <div id="u1469_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1470" class="ax_default" data-left="28" data-top="175" data-width="107" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1471" class="ax_default label transition notrs">
          <div id="u1471_div" class=""></div>
          <div id="u1471_text" class="text ">
            <p><span>辅导项目：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1472" class="ax_default shape transition notrs">
          <div id="u1472_div" class=""></div>
          <div id="u1472_text" class="text ">
            <p><span>作业</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1473" class="ax_default" data-left="298" data-top="214" data-width="145" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1474" class="ax_default label transition notrs">
          <div id="u1474_div" class=""></div>
          <div id="u1474_text" class="text ">
            <p><span>预算区间：1000-2000元</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1475" class="ax_default" data-left="28" data-top="215" data-width="79" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1476" class="ax_default label transition notrs">
          <div id="u1476_div" class=""></div>
          <div id="u1476_text" class="text ">
            <p><span>学生：张菲菲</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1477" class="ax_default" data-left="380" data-top="175" data-width="66" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1478" class="ax_default label transition notrs">
          <div id="u1478_div" class=""></div>
          <div id="u1478_text" class="text ">
            <p><span>专业：会计</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1479" class="ax_default label transition notrs">
        <div id="u1479_div" class=""></div>
        <div id="u1479_text" class="text ">
          <p><span>投递中（3）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1480" class="ax_default label transition notrs">
        <div id="u1480_div" class=""></div>
        <div id="u1480_text" class="text ">
          <p><span>交付中（1）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1481" class="ax_default label transition notrs">
        <div id="u1481_div" class=""></div>
        <div id="u1481_text" class="text ">
          <p><span>已完成（2）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1482" class="ax_default label transition notrs">
        <div id="u1482_div" class=""></div>
        <div id="u1482_text" class="text ">
          <p><span>已拒绝（5）</span></p>
        </div>
      </div>

      <!-- Unnamed (热区) -->
      <div id="u1483" class="ax_default">
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1484" class="ax_default sticky_1 transition notrs">
        <div id="u1484_div" class=""></div>
        <div id="u1484_text" class="text ">
          <p><span>排序：按照拒绝时间倒序</span></p><p><span>分页：每页10个</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
