﻿<!DOCTYPE html>
<html>
  <head>
    <title>发版节奏</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/发版节奏/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/发版节奏/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (表格) -->
      <div id="u2331" class="ax_default">

        <!-- Unnamed (单元格) -->
        <div id="u2332" class="ax_default table_cell transition notrs">
          <svg data="images/发版节奏/u2332.svg" id="u2332_img" class="img generatedImage" viewbox="0 0 92 30">

  <path d="M 1 1  L 92 1  L 92 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" class="stroke" />
  <path d="M 0 0.5  L 92 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" class="stroke" />
          </svg>
          <div id="u2332_text" class="text ">
            <p><span>版本</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2333" class="ax_default table_cell transition notrs">
          <svg data="images/发版节奏/u2333.svg" id="u2333_img" class="img generatedImage" viewbox="92 0 369 30">

  <path d="M 1 1  L 369 1  L 369 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 92 0 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 92 0 )" class="stroke" />
  <path d="M 0 0.5  L 369 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 92 0 )" class="stroke" />
          </svg>
          <div id="u2333_text" class="text ">
            <p><span>主要内容</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2334" class="ax_default table_cell transition notrs">
          <svg data="images/发版节奏/u2334.svg" id="u2334_img" class="img generatedImage" viewbox="461 0 66 30">

  <path d="M 1 1  L 65 1  L 65 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 461 0 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 461 0 )" class="stroke" />
  <path d="M 0 0.5  L 66 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 461 0 )" class="stroke" />
  <path d="M 65.5 1  L 65.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 461 0 )" class="stroke" />
          </svg>
          <div id="u2334_text" class="text ">
            <p><span>发版时间</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2335" class="ax_default table_cell transition notrs">
          <svg data="images/发版节奏/u2335.svg" id="u2335_img" class="img generatedImage" viewbox="0 30 92 112">

  <path d="M 1 1  L 92 1  L 92 112  L 1 112  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 30 )" class="fill" />
  <path d="M 0.5 1  L 0.5 112  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 30 )" class="stroke" />
  <path d="M 0 0.5  L 92 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 30 )" class="stroke" />
          </svg>
          <div id="u2335_text" class="text ">
            <p><span>v1.0</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2336" class="ax_default table_cell transition notrs">
          <svg data="images/发版节奏/u2336.svg" id="u2336_img" class="img generatedImage" viewbox="92 30 369 112">

  <path d="M 1 1  L 369 1  L 369 112  L 1 112  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 92 30 )" class="fill" />
  <path d="M 0.5 1  L 0.5 112  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 92 30 )" class="stroke" />
  <path d="M 0 0.5  L 369 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 92 30 )" class="stroke" />
          </svg>
          <div id="u2336_text" class="text ">
            <p><span>学生端：查看首页、人员详情查看页面、发布需求、我的订单（待接单）</span></p><p><span>老师端：首页、投递、认证、我的订单（投递中）、擅长辅导</span></p><p><span style="color:#D9001B;"><br></span></p><p><span style="color:#D9001B;">学生端的不合适、老师端的已拒绝先不做</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2337" class="ax_default table_cell transition notrs">
          <svg data="images/发版节奏/u2337.svg" id="u2337_img" class="img generatedImage" viewbox="461 30 66 112">

  <path d="M 1 1  L 65 1  L 65 112  L 1 112  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 461 30 )" class="fill" />
  <path d="M 0.5 1  L 0.5 112  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 461 30 )" class="stroke" />
  <path d="M 0 0.5  L 66 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 461 30 )" class="stroke" />
  <path d="M 65.5 1  L 65.5 112  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 461 30 )" class="stroke" />
          </svg>
          <div id="u2337_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2338" class="ax_default table_cell transition notrs">
          <svg data="images/发版节奏/u2338.svg" id="u2338_img" class="img generatedImage" viewbox="0 142 92 79">

  <path d="M 1 1  L 92 1  L 92 78  L 1 78  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 142 )" class="fill" />
  <path d="M 0.5 1  L 0.5 78  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 142 )" class="stroke" />
  <path d="M 0 0.5  L 92 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 142 )" class="stroke" />
  <path d="M 0 78.5  L 92 78.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 142 )" class="stroke" />
          </svg>
          <div id="u2338_text" class="text ">
            <p><span>v2.0</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2339" class="ax_default table_cell transition notrs">
          <svg data="images/发版节奏/u2339.svg" id="u2339_img" class="img generatedImage" viewbox="92 142 369 79">

  <path d="M 1 1  L 369 1  L 369 78  L 1 78  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 92 142 )" class="fill" />
  <path d="M 0.5 1  L 0.5 78  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 92 142 )" class="stroke" />
  <path d="M 0 0.5  L 369 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 92 142 )" class="stroke" />
  <path d="M 0 78.5  L 369 78.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 92 142 )" class="stroke" />
          </svg>
          <div id="u2339_text" class="text ">
            <p><span>学生端：</span></p><p><span>老师端：首页、投递、认证、我的订单（投递中、已拒绝）</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2340" class="ax_default table_cell transition notrs">
          <svg data="images/发版节奏/u2340.svg" id="u2340_img" class="img generatedImage" viewbox="461 142 66 79">

  <path d="M 1 1  L 65 1  L 65 78  L 1 78  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 461 142 )" class="fill" />
  <path d="M 0.5 1  L 0.5 78  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 461 142 )" class="stroke" />
  <path d="M 0 0.5  L 66 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 461 142 )" class="stroke" />
  <path d="M 65.5 1  L 65.5 78  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 461 142 )" class="stroke" />
  <path d="M 0 78.5  L 66 78.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 461 142 )" class="stroke" />
          </svg>
          <div id="u2340_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
