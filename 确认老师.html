<!DOCTYPE html>
<html>
  <head>
    <title>确认老师</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/确认老师/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/确认老师/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u492" class="ax_default box_1 transition notrs">
        <div id="u492_div" class=""></div>
        <div id="u492_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u493" class="ax_default label transition notrs">
        <div id="u493_div" class=""></div>
        <div id="u493_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u494" class="ax_default label transition notrs">
        <div id="u494_div" class=""></div>
        <div id="u494_text" class="text ">
          <p><span>确认老师</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u495" class="ax_default _图片 transition notrs">
        <img id="u495_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u495_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u496" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u496_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u496_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u497" class="ax_default box_1 transition notrs">
              <div id="u497_div" class=""></div>
              <div id="u497_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u498" class="ax_default label transition notrs">
              <div id="u498_div" class=""></div>
              <div id="u498_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u499" class="ax_default label transition notrs">
              <div id="u499_div" class=""></div>
              <div id="u499_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u500" class="ax_default button transition notrs">
              <div id="u500_div" class=""></div>
              <div id="u500_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u501" class="ax_default primary_button transition notrs">
              <div id="u501_div" class=""></div>
              <div id="u501_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u496_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u496_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u502" class="ax_default box_1 transition notrs">
              <div id="u502_div" class=""></div>
              <div id="u502_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u503" class="ax_default label transition notrs">
              <div id="u503_div" class=""></div>
              <div id="u503_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u504" class="ax_default label transition notrs">
              <div id="u504_div" class=""></div>
              <div id="u504_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u505" class="ax_default button transition notrs">
              <div id="u505_div" class=""></div>
              <div id="u505_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u506" class="ax_default primary_button transition notrs">
              <div id="u506_div" class=""></div>
              <div id="u506_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u507" class="ax_default label transition notrs">
              <div id="u507_div" class=""></div>
              <div id="u507_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u508" class="ax_default text_field transition notrs">
              <div id="u508_div" class=""></div>
              <input id="u508_input" type="text" value="" class="u508_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u509" class="ax_default label transition notrs">
              <div id="u509_div" class=""></div>
              <div id="u509_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u510" class="ax_default label transition notrs">
        <div id="u510_div" class=""></div>
        <div id="u510_text" class="text ">
          <p><span>提示：</span></p><p><span>1、请和辅导老师沟通清楚，再进行确认。</span></p><p><span>2、请和老师确认付款和交付阶段，确认后填写交付阶段信息，填写后</span></p><p><span>不允许修改。</span></p><p><span>3、每个阶段先付款，再交付。</span></p><p><span>4、您付的款都在平台托管，在订单结束14日内如果您无异议，辅导</span></p><p><span>老师才能提现。</span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u511" class="ax_default" data-left="17.999483828313867" data-top="250" data-width="453.0010323433722" data-height="24.62717305005009" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u512" class="ax_default label transition notrs">
          <div id="u512_div" class=""></div>
          <div id="u512_text" class="text ">
            <p><span>交付阶段</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u513" class="ax_default" data-left="245" data-top="250" data-width="70" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u514" class="ax_default label transition notrs">
            <div id="u514_div" class=""></div>
            <div id="u514_text" class="text ">
              <p><span>阶段费用</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u515" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u515_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u515_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u516" class="ax_default" data-left="44" data-top="281" data-width="405" data-height="31" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u517" class="ax_default label transition notrs">
          <div id="u517_div" class=""></div>
          <div id="u517_text" class="text ">
            <p><span>第一阶段</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u518" class="ax_default text_field transition notrs">
          <div id="u518_div" class=""></div>
          <input id="u518_input" type="text" value="" class="u518_input"/>
        </div>

        <!-- Unnamed (椭圆) -->
        <div id="u519" class="ax_default ellipse transition notrs">
          <svg data="images/确认老师/u519.svg" id="u519_img" class="img generatedImage">

  <defs>
    <pattern id="u519_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u519_img_cl53">
      <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -418 -281 )">
    <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " fill-rule="nonzero" fill="rgba(2, 167, 240, 1)" stroke="none" transform="matrix(1 0 0 1 418 281 )" class="fill" />
    <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 418 281 )" class="stroke" mask="url(#u519_img_cl53)" />
  </g>
          </svg>
          <div id="u519_text" class="text ">
            <p><span>+</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u520" class="ax_default" data-left="44" data-top="330" data-width="405" data-height="31" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u521" class="ax_default label transition notrs">
          <div id="u521_div" class=""></div>
          <div id="u521_text" class="text ">
            <p><span>第二阶段</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u522" class="ax_default text_field transition notrs">
          <div id="u522_div" class=""></div>
          <input id="u522_input" type="text" value="" class="u522_input"/>
        </div>

        <!-- Unnamed (椭圆) -->
        <div id="u523" class="ax_default ellipse transition notrs">
          <svg data="images/确认老师/u519.svg" id="u523_img" class="img generatedImage">

  <defs>
    <pattern id="u523_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u523_img_cl53">
      <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -418 -281 )">
    <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " fill-rule="nonzero" fill="rgba(2, 167, 240, 1)" stroke="none" transform="matrix(1 0 0 1 418 281 )" class="fill" />
    <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 418 281 )" class="stroke" mask="url(#u523_img_cl53)" />
  </g>
          </svg>
          <div id="u523_text" class="text ">
            <p><span>+</span></p>
          </div>
        </div>

        <!-- Unnamed (椭圆) -->
        <div id="u524" class="ax_default ellipse transition notrs">
          <svg data="images/确认老师/u519.svg" id="u524_img" class="img generatedImage">

  <defs>
    <pattern id="u524_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u524_img_cl53">
      <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -418 -281 )">
    <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " fill-rule="nonzero" fill="rgba(2, 167, 240, 1)" stroke="none" transform="matrix(1 0 0 1 418 281 )" class="fill" />
    <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 418 281 )" class="stroke" mask="url(#u524_img_cl53)" />
  </g>
          </svg>
          <div id="u524_text" class="text ">
            <p><span>-</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u525" class="ax_default" data-left="44" data-top="376" data-width="405" data-height="31" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u526" class="ax_default label transition notrs">
          <div id="u526_div" class=""></div>
          <div id="u526_text" class="text ">
            <p><span>第三阶段</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u527" class="ax_default text_field transition notrs">
          <div id="u527_div" class=""></div>
          <input id="u527_input" type="text" value="" class="u527_input"/>
        </div>

        <!-- Unnamed (椭圆) -->
        <div id="u528" class="ax_default ellipse transition notrs">
          <svg data="images/确认老师/u519.svg" id="u528_img" class="img generatedImage">

  <defs>
    <pattern id="u528_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u528_img_cl53">
      <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -418 -281 )">
    <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " fill-rule="nonzero" fill="rgba(2, 167, 240, 1)" stroke="none" transform="matrix(1 0 0 1 418 281 )" class="fill" />
    <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 418 281 )" class="stroke" mask="url(#u528_img_cl53)" />
  </g>
          </svg>
          <div id="u528_text" class="text ">
            <p><span>+</span></p>
          </div>
        </div>

        <!-- Unnamed (椭圆) -->
        <div id="u529" class="ax_default ellipse transition notrs">
          <svg data="images/确认老师/u519.svg" id="u529_img" class="img generatedImage">

  <defs>
    <pattern id="u529_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u529_img_cl53">
      <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -418 -281 )">
    <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " fill-rule="nonzero" fill="rgba(2, 167, 240, 1)" stroke="none" transform="matrix(1 0 0 1 418 281 )" class="fill" />
    <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 418 281 )" class="stroke" mask="url(#u529_img_cl53)" />
  </g>
          </svg>
          <div id="u529_text" class="text ">
            <p><span>-</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u530" class="ax_default" data-left="14.999483828313878" data-top="421.3728269499499" data-width="453.00103234337223" data-height="31.62717305005009" layer-opacity="1">

        <!-- Unnamed (线段) -->
        <div id="u531" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u531_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u531_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u532" class="ax_default" data-left="44" data-top="433" data-width="48" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u533" class="ax_default label transition notrs">
            <div id="u533_div" class=""></div>
            <div id="u533_text" class="text ">
              <p><span>共3阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u534" class="ax_default" data-left="247" data-top="433" data-width="40" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u535" class="ax_default label transition notrs">
            <div id="u535_div" class=""></div>
            <div id="u535_text" class="text ">
              <p><span>13.000</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u536" class="ax_default primary_button transition notrs">
        <div id="u536_div" class=""></div>
        <div id="u536_text" class="text ">
          <p><span>确认老师及交付阶段</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u537" class="ax_default label transition notrs">
        <div id="u537_div" class=""></div>
        <div id="u537_text" class="text ">
          <p><span>请先和辅导老师确认交付阶段，双方无异议后再最终确认</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
