﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情（评价信息）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情（评价信息）_1/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情（评价信息）_1/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1961" class="ax_default box_1 transition notrs">
        <div id="u1961_div" class=""></div>
        <div id="u1961_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1962" class="ax_default label transition notrs">
        <div id="u1962_div" class=""></div>
        <div id="u1962_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1963" class="ax_default label transition notrs">
        <div id="u1963_div" class=""></div>
        <div id="u1963_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1964" class="ax_default _图片 transition notrs">
        <img id="u1964_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1964_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1965" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1965_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1965_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1966" class="ax_default box_1 transition notrs">
              <div id="u1966_div" class=""></div>
              <div id="u1966_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1967" class="ax_default label transition notrs">
              <div id="u1967_div" class=""></div>
              <div id="u1967_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1968" class="ax_default label transition notrs">
              <div id="u1968_div" class=""></div>
              <div id="u1968_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1969" class="ax_default button transition notrs">
              <div id="u1969_div" class=""></div>
              <div id="u1969_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1970" class="ax_default primary_button transition notrs">
              <div id="u1970_div" class=""></div>
              <div id="u1970_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1965_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1965_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1971" class="ax_default box_1 transition notrs">
              <div id="u1971_div" class=""></div>
              <div id="u1971_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1972" class="ax_default label transition notrs">
              <div id="u1972_div" class=""></div>
              <div id="u1972_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1973" class="ax_default label transition notrs">
              <div id="u1973_div" class=""></div>
              <div id="u1973_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1974" class="ax_default button transition notrs">
              <div id="u1974_div" class=""></div>
              <div id="u1974_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1975" class="ax_default primary_button transition notrs">
              <div id="u1975_div" class=""></div>
              <div id="u1975_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1976" class="ax_default label transition notrs">
              <div id="u1976_div" class=""></div>
              <div id="u1976_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1977" class="ax_default text_field transition notrs">
              <div id="u1977_div" class=""></div>
              <input id="u1977_input" type="text" value="" class="u1977_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1978" class="ax_default label transition notrs">
              <div id="u1978_div" class=""></div>
              <div id="u1978_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1979" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1980" class="ax_default label transition notrs">
        <div id="u1980_div" class=""></div>
        <div id="u1980_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1981" class="ax_default label transition notrs">
        <div id="u1981_div" class=""></div>
        <div id="u1981_text" class="text ">
          <p><span>交付信息</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1982" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u1982_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u1982_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1983" class="ax_default label transition notrs">
        <div id="u1983_div" class=""></div>
        <div id="u1983_text" class="text ">
          <p><span>评价信息</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1984" class="ax_default" data-left="56" data-top="133" data-width="400" data-height="381" layer-opacity="1">

        <!-- Unnamed (图片) -->
        <div id="u1985" class="ax_default _图片 transition notrs">
          <img id="u1985_img" class="img " src="images/交付中/u566.png"/>
          <div id="u1985_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1986" class="ax_default label transition notrs">
          <div id="u1986_div" class=""></div>
          <div id="u1986_text" class="text ">
            <p><span>专业水平</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1987" class="ax_default label transition notrs">
          <div id="u1987_div" class=""></div>
          <div id="u1987_text" class="text ">
            <p><span>沟通能力</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1988" class="ax_default label transition notrs">
          <div id="u1988_div" class=""></div>
          <div id="u1988_text" class="text ">
            <p><span>响应速度</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1989" class="ax_default" data-left="288" data-top="267" data-width="168" data-height="26" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u1990" class="ax_default _图片 transition notrs">
            <img id="u1990_img" class="img " src="images/交付中/u566.png"/>
            <div id="u1990_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1991" class="ax_default label transition notrs">
            <div id="u1991_div" class=""></div>
            <div id="u1991_text" class="text ">
              <p><span>4.5分</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1992" class="ax_default" data-left="288" data-top="299" data-width="168" data-height="26" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u1993" class="ax_default _图片 transition notrs">
            <img id="u1993_img" class="img " src="images/交付中/u566.png"/>
            <div id="u1993_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1994" class="ax_default label transition notrs">
            <div id="u1994_div" class=""></div>
            <div id="u1994_text" class="text ">
              <p><span>4.5分</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1995" class="ax_default label transition notrs">
          <div id="u1995_div" class=""></div>
          <div id="u1995_text" class="text ">
            <p><span>感谢您完成本次辅导，请您为老师打分！</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1996" class="ax_default label transition notrs">
          <div id="u1996_div" class=""></div>
          <div id="u1996_text" class="text ">
            <p><span>服务态度</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1997" class="ax_default label transition notrs">
          <div id="u1997_div" class=""></div>
          <div id="u1997_text" class="text ">
            <p><span>结果质量</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1998" class="ax_default" data-left="288" data-top="332" data-width="168" data-height="26" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u1999" class="ax_default _图片 transition notrs">
            <img id="u1999_img" class="img " src="images/交付中/u566.png"/>
            <div id="u1999_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u2000" class="ax_default label transition notrs">
            <div id="u2000_div" class=""></div>
            <div id="u2000_text" class="text ">
              <p><span>4.5分</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2001" class="ax_default" data-left="288" data-top="362" data-width="168" data-height="26" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u2002" class="ax_default _图片 transition notrs">
            <img id="u2002_img" class="img " src="images/交付中/u566.png"/>
            <div id="u2002_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u2003" class="ax_default label transition notrs">
            <div id="u2003_div" class=""></div>
            <div id="u2003_text" class="text ">
              <p><span>4.5分</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2004" class="ax_default" data-left="288" data-top="395" data-width="168" data-height="26" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u2005" class="ax_default _图片 transition notrs">
            <img id="u2005_img" class="img " src="images/交付中/u566.png"/>
            <div id="u2005_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u2006" class="ax_default label transition notrs">
            <div id="u2006_div" class=""></div>
            <div id="u2006_text" class="text ">
              <p><span>4.5分</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2007" class="ax_default button transition notrs">
          <div id="u2007_div" class=""></div>
          <div id="u2007_text" class="text ">
            <p><span>耐心细致</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2008" class="ax_default button transition notrs">
          <div id="u2008_div" class=""></div>
          <div id="u2008_text" class="text ">
            <p><span>专业扎实</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2009" class="ax_default button transition notrs">
          <div id="u2009_div" class=""></div>
          <div id="u2009_text" class="text ">
            <p><span>响应及时</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2010" class="ax_default button transition notrs">
          <div id="u2010_div" class=""></div>
          <div id="u2010_text" class="text ">
            <p><span>服务周到</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2011" class="ax_default button transition notrs">
          <div id="u2011_div" class=""></div>
          <div id="u2011_text" class="text ">
            <p><span>讲解清楚</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2012" class="ax_default button transition notrs">
          <div id="u2012_div" class=""></div>
          <div id="u2012_text" class="text ">
            <p><span>保质保量</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2013" class="ax_default label transition notrs">
        <div id="u2013_div" class=""></div>
        <div id="u2013_text" class="text ">
          <p><span>感谢老师的辅导，论文终于通过审核了感谢老师的辅导，论文终于</span></p><p><span>通过审核了。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2014" class="ax_default sticky_4 transition notrs">
        <div id="u2014_div" class=""></div>
        <div id="u2014_text" class="text ">
          <p><span>后面加回复评论</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
