﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情 (交付中）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情__交付中）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情__交付中）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1604" class="ax_default box_1 transition notrs">
        <div id="u1604_div" class=""></div>
        <div id="u1604_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1605" class="ax_default label transition notrs">
        <div id="u1605_div" class=""></div>
        <div id="u1605_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1606" class="ax_default label transition notrs">
        <div id="u1606_div" class=""></div>
        <div id="u1606_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1607" class="ax_default _图片 transition notrs">
        <img id="u1607_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1607_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1608" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1608_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1608_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1609" class="ax_default box_1 transition notrs">
              <div id="u1609_div" class=""></div>
              <div id="u1609_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1610" class="ax_default label transition notrs">
              <div id="u1610_div" class=""></div>
              <div id="u1610_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1611" class="ax_default label transition notrs">
              <div id="u1611_div" class=""></div>
              <div id="u1611_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1612" class="ax_default button transition notrs">
              <div id="u1612_div" class=""></div>
              <div id="u1612_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1613" class="ax_default primary_button transition notrs">
              <div id="u1613_div" class=""></div>
              <div id="u1613_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1608_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1608_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1614" class="ax_default box_1 transition notrs">
              <div id="u1614_div" class=""></div>
              <div id="u1614_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1615" class="ax_default label transition notrs">
              <div id="u1615_div" class=""></div>
              <div id="u1615_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1616" class="ax_default label transition notrs">
              <div id="u1616_div" class=""></div>
              <div id="u1616_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1617" class="ax_default button transition notrs">
              <div id="u1617_div" class=""></div>
              <div id="u1617_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1618" class="ax_default primary_button transition notrs">
              <div id="u1618_div" class=""></div>
              <div id="u1618_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1619" class="ax_default label transition notrs">
              <div id="u1619_div" class=""></div>
              <div id="u1619_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1620" class="ax_default text_field transition notrs">
              <div id="u1620_div" class=""></div>
              <input id="u1620_input" type="text" value="" class="u1620_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1621" class="ax_default label transition notrs">
              <div id="u1621_div" class=""></div>
              <div id="u1621_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1622" class="ax_default" data-left="19.999483828313867" data-top="244" data-width="453.0010323433722" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1623" class="ax_default" data-left="38" data-top="246" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1624" class="ax_default label transition notrs">
            <div id="u1624_div" class=""></div>
            <div id="u1624_text" class="text ">
              <p><span>辅导项目</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1625" class="ax_default" data-left="435" data-top="244" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1626" class="ax_default label transition notrs">
            <div id="u1626_div" class=""></div>
            <div id="u1626_text" class="text ">
              <p><span>作业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1627" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1627_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1627_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1628" class="ax_default" data-left="16.999483828313878" data-top="126" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1629" class="ax_default" data-left="35" data-top="126" data-width="40" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1630" class="ax_default label transition notrs">
            <div id="u1630_div" class=""></div>
            <div id="u1630_text" class="text ">
              <p><span>订单号</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1631" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1631_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1631_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1632" class="ax_default" data-left="363" data-top="126" data-width="96" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1633" class="ax_default label transition notrs">
            <div id="u1633_div" class=""></div>
            <div id="u1633_text" class="text ">
              <p><span>JS13455555333</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1634" class="ax_default" data-left="16.999483828313867" data-top="168" data-width="453.00103234337223" data-height="29.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1635" class="ax_default" data-left="35" data-top="169" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1636" class="ax_default label transition notrs">
            <div id="u1636_div" class=""></div>
            <div id="u1636_text" class="text ">
              <p><span>发布时间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1637" class="ax_default" data-left="358" data-top="168" data-width="112" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1638" class="ax_default label transition notrs">
            <div id="u1638_div" class=""></div>
            <div id="u1638_text" class="text ">
              <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp;2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1639" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1639_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1639_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1640" class="ax_default" data-left="19.99948382831389" data-top="286" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1641" class="ax_default" data-left="38" data-top="288" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1642" class="ax_default label transition notrs">
            <div id="u1642_div" class=""></div>
            <div id="u1642_text" class="text ">
              <p><span>学习阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1643" class="ax_default" data-left="435" data-top="286" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1644" class="ax_default label transition notrs">
            <div id="u1644_div" class=""></div>
            <div id="u1644_text" class="text ">
              <p><span>本科</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1645" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1645_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1645_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1646" class="ax_default" data-left="19.999483828313863" data-top="321" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1647" class="ax_default" data-left="38" data-top="323" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1648" class="ax_default label transition notrs">
            <div id="u1648_div" class=""></div>
            <div id="u1648_text" class="text ">
              <p><span>学习位置</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1649" class="ax_default" data-left="435" data-top="321" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1650" class="ax_default label transition notrs">
            <div id="u1650_div" class=""></div>
            <div id="u1650_text" class="text ">
              <p><span>国内</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1651" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1651_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1651_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1652" class="ax_default" data-left="19.99948382831388" data-top="362" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1653" class="ax_default" data-left="38" data-top="364" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1654" class="ax_default label transition notrs">
            <div id="u1654_div" class=""></div>
            <div id="u1654_text" class="text ">
              <p><span>学习专业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1655" class="ax_default" data-left="435" data-top="362" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1656" class="ax_default label transition notrs">
            <div id="u1656_div" class=""></div>
            <div id="u1656_text" class="text ">
              <p><span>会计</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1657" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1657_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1657_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1658" class="ax_default" data-left="19.999483828313874" data-top="403" data-width="453.0010323433722" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1659" class="ax_default" data-left="38" data-top="405" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1660" class="ax_default label transition notrs">
            <div id="u1660_div" class=""></div>
            <div id="u1660_text" class="text ">
              <p><span>预算区间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1661" class="ax_default" data-left="382" data-top="403" data-width="80" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1662" class="ax_default label transition notrs">
            <div id="u1662_div" class=""></div>
            <div id="u1662_text" class="text ">
              <p><span>1000-2000元</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1663" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1663_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1663_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1664" class="ax_default" data-left="16.999483828313885" data-top="442" data-width="453.00103234337223" data-height="90" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1665" class="ax_default" data-left="35" data-top="442" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1666" class="ax_default label transition notrs">
            <div id="u1666_div" class=""></div>
            <div id="u1666_text" class="text ">
              <p><span>需求描述</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1667" class="ax_default label transition notrs">
          <div id="u1667_div" class=""></div>
          <div id="u1667_text" class="text ">
            <p><span>想要找一个辅导论文的老师，想要找一个辅导论文的老师想要找一个</span></p><p><span>辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想</span></p><p><span>要找一个辅导论文的老师</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1668" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1668_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1668_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1669" class="ax_default" data-left="16.999483828313867" data-top="558" data-width="453.0010323433722" data-height="165" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1670" class="ax_default label transition notrs">
          <div id="u1670_div" class=""></div>
          <div id="u1670_text" class="text ">
            <p><span>希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，</span></p><p><span>有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热</span></p><p><span>情专业，有耐心。</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1671" class="ax_default" data-left="16.999483828313867" data-top="558" data-width="453.0010323433722" data-height="28.627173050050146" layer-opacity="1">

          <!-- Unnamed (组合) -->
          <div id="u1672" class="ax_default" data-left="35" data-top="558" data-width="53" data-height="18" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1673" class="ax_default label transition notrs">
              <div id="u1673_div" class=""></div>
              <div id="u1673_text" class="text ">
                <p><span>老师要求</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1674" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
          </div>

          <!-- Unnamed (线段) -->
          <div id="u1675" class="ax_default line transition notrs">
            <svg data="images/订单详情__待接单）/u316.svg" id="u1675_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
            </svg>
            <div id="u1675_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1676" class="ax_default" data-left="35" data-top="600" data-width="121" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1677" class="ax_default label transition notrs">
            <div id="u1677_div" class=""></div>
            <div id="u1677_text" class="text ">
              <p><span>老师情况</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1678" class="ax_default shape transition notrs">
            <div id="u1678_div" class=""></div>
            <div id="u1678_text" class="text ">
              <p><span>博士</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1679" class="ax_default" data-left="35" data-top="632" data-width="132" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1680" class="ax_default label transition notrs">
            <div id="u1680_div" class=""></div>
            <div id="u1680_text" class="text ">
              <p><span>是否留学</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1681" class="ax_default box_1 transition notrs">
            <div id="u1681_div" class=""></div>
            <div id="u1681_text" class="text ">
              <p><span>不要求</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1682" class="ax_default label transition notrs">
        <div id="u1682_div" class=""></div>
        <div id="u1682_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1683" class="ax_default label transition notrs">
        <div id="u1683_div" class=""></div>
        <div id="u1683_text" class="text ">
          <p><span>交付信息</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1684" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u1684_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u1684_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1685" class="ax_default" data-left="18.999483828313878" data-top="205" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1686" class="ax_default" data-left="37" data-top="207" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1687" class="ax_default label transition notrs">
            <div id="u1687_div" class=""></div>
            <div id="u1687_text" class="text ">
              <p><span>学生名称</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1688" class="ax_default" data-left="422" data-top="205" data-width="40" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1689" class="ax_default label transition notrs">
            <div id="u1689_div" class=""></div>
            <div id="u1689_text" class="text ">
              <p><span>张菲菲</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1690" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1690_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1690_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
