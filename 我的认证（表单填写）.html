﻿<!DOCTYPE html>
<html>
  <head>
    <title>我的认证（表单填写）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/我的认证（表单填写）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/我的认证（表单填写）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u2143" class="ax_default box_1 transition notrs">
        <div id="u2143_div" class=""></div>
        <div id="u2143_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2144" class="ax_default label transition notrs">
        <div id="u2144_div" class=""></div>
        <div id="u2144_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2145" class="ax_default label transition notrs">
        <div id="u2145_div" class=""></div>
        <div id="u2145_text" class="text ">
          <p><span>我的信息</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2146" class="ax_default _图片 transition notrs">
        <img id="u2146_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u2146_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2147" class="ax_default" data-left="35" data-top="124" data-width="410" data-height="51" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2148" class="ax_default label transition notrs">
          <div id="u2148_div" class=""></div>
          <div id="u2148_text" class="text ">
            <p><span>头像</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u2149" class="ax_default line transition notrs">
          <svg data="images/我的认证（表单填写）/u2149.svg" id="u2149_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
          </svg>
          <div id="u2149_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u2150" class="ax_default icon transition notrs">
          <svg data="images/我的认证（表单填写）/u2150.svg" id="u2150_img" class="img generatedImage">

  <defs>
    <pattern id="u2150_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u2150_img_cl55">
      <path d="M 34.933035714285715 43.98716517857143  C 38.002232142857146 42.359561011904766  40.522693452380956 40.15066964285714  42.494419642857146 37.36049107142857  C 42.085193452380956 34.477306547619044  41.27139136904762 32.082403273809526  40.053013392857146 30.17578125  C 38.83463541666667 28.269159226190478  37.1186755952381 27.167038690476186  34.90513392857143 26.86941964285714  C 33.65885416666667 28.245907738095234  32.175409226190474 29.320126488095234  30.454799107142854 30.09207589285714  C 28.734188988095244 30.864025297619047  26.91592261904762 31.25  25 31.25  C 23.08407738095238 31.25  21.265811011904763 30.864025297619047  19.545200892857142 30.09207589285714  C 17.824590773809526 29.320126488095234  16.341145833333336 28.245907738095234  15.094866071428573 26.86941964285714  C 12.881324404761903 27.167038690476186  11.165364583333334 28.269159226190478  9.946986607142858 30.17578125  C 8.728608630952381 32.082403273809526  7.914806547619048 34.477306547619044  7.505580357142858 37.36049107142857  C 9.477306547619047 40.15066964285714  11.997767857142858 42.359561011904766  15.066964285714285 43.98716517857143  C 18.136160714285715 45.614769345238095  21.44717261904762 46.42857142857143  25 46.42857142857143  C 28.552827380952383 46.42857142857143  31.863839285714285 45.614769345238095  34.933035714285715 43.98716517857143  Z M 32.57533482142857 25.43247767857143  C 34.66796875 23.33984375  35.714285714285715 20.814732142857146  35.714285714285715 17.857142857142854  C 35.714285714285715 14.89955357142857  34.66796875 12.374441964285715  32.57533482142857 10.281808035714285  C 30.482700892857146 8.189174107142854  27.957589285714285 7.142857142857145  25 7.142857142857145  C 22.042410714285715 7.142857142857145  19.517299107142858 8.189174107142854  17.424665178571427 10.281808035714285  C 15.33203125 12.374441964285715  14.285714285714285 14.89955357142857  14.285714285714285 17.857142857142854  C 14.285714285714285 20.814732142857146  15.33203125 23.33984375  17.424665178571427 25.43247767857143  C 19.517299107142858 27.52511160714286  22.042410714285715 28.57142857142857  25 28.57142857142857  C 27.957589285714285 28.57142857142857  30.482700892857146 27.52511160714286  32.57533482142857 25.43247767857143  Z M 48.018973214285715 15.29017857142857  C 49.33965773809524 18.377976190476186  50 21.614583333333332  50 25  C 50 28.385416666666664  49.33965773809524 31.617373511904756  48.018973214285715 34.695870535714285  C 46.6982886904762 37.77436755952381  44.92652529761906 40.434337797619044  42.703683035714285 42.67578125  C 40.480840773809526 44.91722470238095  37.825520833333336 46.69828869047619  34.737723214285715 48.018973214285715  C 31.6499255952381 49.33965773809524  28.404017857142854 50  25 50  C 21.614583333333336 50  18.377976190476193 49.33965773809524  15.290178571428573 48.018973214285715  C 12.202380952380953 46.69828869047619  9.542410714285714 44.921875  7.310267857142858 42.68973214285714  C 5.078125 40.457589285714285  3.3017113095238098 37.797619047619044  1.9810267857142856 34.70982142857143  C 0.6603422619047619 31.622023809523807  0 28.385416666666664  0 25  C 0 21.614583333333332  0.6603422619047619 18.377976190476186  1.9810267857142856 15.29017857142857  C 3.3017113095238098 12.202380952380947  5.078125 9.542410714285715  7.310267857142858 7.310267857142855  C 9.542410714285714 5.078125  12.202380952380953 3.3017113095238084  15.290178571428573 1.981026785714285  C 18.377976190476193 0.6603422619047616  21.614583333333336 0  25 0  C 28.385416666666668 0  31.622023809523814 0.6603422619047616  34.70982142857143 1.981026785714285  C 37.79761904761905 3.3017113095238084  40.457589285714285 5.078125  42.689732142857146 7.310267857142855  C 44.92187500000001 9.542410714285715  46.6982886904762 12.202380952380947  48.018973214285715 15.29017857142857  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -389 -124 )">
    <path d="M 34.933035714285715 43.98716517857143  C 38.002232142857146 42.359561011904766  40.522693452380956 40.15066964285714  42.494419642857146 37.36049107142857  C 42.085193452380956 34.477306547619044  41.27139136904762 32.082403273809526  40.053013392857146 30.17578125  C 38.83463541666667 28.269159226190478  37.1186755952381 27.167038690476186  34.90513392857143 26.86941964285714  C 33.65885416666667 28.245907738095234  32.175409226190474 29.320126488095234  30.454799107142854 30.09207589285714  C 28.734188988095244 30.864025297619047  26.91592261904762 31.25  25 31.25  C 23.08407738095238 31.25  21.265811011904763 30.864025297619047  19.545200892857142 30.09207589285714  C 17.824590773809526 29.320126488095234  16.341145833333336 28.245907738095234  15.094866071428573 26.86941964285714  C 12.881324404761903 27.167038690476186  11.165364583333334 28.269159226190478  9.946986607142858 30.17578125  C 8.728608630952381 32.082403273809526  7.914806547619048 34.477306547619044  7.505580357142858 37.36049107142857  C 9.477306547619047 40.15066964285714  11.997767857142858 42.359561011904766  15.066964285714285 43.98716517857143  C 18.136160714285715 45.614769345238095  21.44717261904762 46.42857142857143  25 46.42857142857143  C 28.552827380952383 46.42857142857143  31.863839285714285 45.614769345238095  34.933035714285715 43.98716517857143  Z M 32.57533482142857 25.43247767857143  C 34.66796875 23.33984375  35.714285714285715 20.814732142857146  35.714285714285715 17.857142857142854  C 35.714285714285715 14.89955357142857  34.66796875 12.374441964285715  32.57533482142857 10.281808035714285  C 30.482700892857146 8.189174107142854  27.957589285714285 7.142857142857145  25 7.142857142857145  C 22.042410714285715 7.142857142857145  19.517299107142858 8.189174107142854  17.424665178571427 10.281808035714285  C 15.33203125 12.374441964285715  14.285714285714285 14.89955357142857  14.285714285714285 17.857142857142854  C 14.285714285714285 20.814732142857146  15.33203125 23.33984375  17.424665178571427 25.43247767857143  C 19.517299107142858 27.52511160714286  22.042410714285715 28.57142857142857  25 28.57142857142857  C 27.957589285714285 28.57142857142857  30.482700892857146 27.52511160714286  32.57533482142857 25.43247767857143  Z M 48.018973214285715 15.29017857142857  C 49.33965773809524 18.377976190476186  50 21.614583333333332  50 25  C 50 28.385416666666664  49.33965773809524 31.617373511904756  48.018973214285715 34.695870535714285  C 46.6982886904762 37.77436755952381  44.92652529761906 40.434337797619044  42.703683035714285 42.67578125  C 40.480840773809526 44.91722470238095  37.825520833333336 46.69828869047619  34.737723214285715 48.018973214285715  C 31.6499255952381 49.33965773809524  28.404017857142854 50  25 50  C 21.614583333333336 50  18.377976190476193 49.33965773809524  15.290178571428573 48.018973214285715  C 12.202380952380953 46.69828869047619  9.542410714285714 44.921875  7.310267857142858 42.68973214285714  C 5.078125 40.457589285714285  3.3017113095238098 37.797619047619044  1.9810267857142856 34.70982142857143  C 0.6603422619047619 31.622023809523807  0 28.385416666666664  0 25  C 0 21.614583333333332  0.6603422619047619 18.377976190476186  1.9810267857142856 15.29017857142857  C 3.3017113095238098 12.202380952380947  5.078125 9.542410714285715  7.310267857142858 7.310267857142855  C 9.542410714285714 5.078125  12.202380952380953 3.3017113095238084  15.290178571428573 1.981026785714285  C 18.377976190476193 0.6603422619047616  21.614583333333336 0  25 0  C 28.385416666666668 0  31.622023809523814 0.6603422619047616  34.70982142857143 1.981026785714285  C 37.79761904761905 3.3017113095238084  40.457589285714285 5.078125  42.689732142857146 7.310267857142855  C 44.92187500000001 9.542410714285715  46.6982886904762 12.202380952380947  48.018973214285715 15.29017857142857  Z " fill-rule="nonzero" fill="rgba(170, 170, 170, 1)" stroke="none" transform="matrix(1 0 0 1 389 124 )" class="fill" />
    <path d="M 34.933035714285715 43.98716517857143  C 38.002232142857146 42.359561011904766  40.522693452380956 40.15066964285714  42.494419642857146 37.36049107142857  C 42.085193452380956 34.477306547619044  41.27139136904762 32.082403273809526  40.053013392857146 30.17578125  C 38.83463541666667 28.269159226190478  37.1186755952381 27.167038690476186  34.90513392857143 26.86941964285714  C 33.65885416666667 28.245907738095234  32.175409226190474 29.320126488095234  30.454799107142854 30.09207589285714  C 28.734188988095244 30.864025297619047  26.91592261904762 31.25  25 31.25  C 23.08407738095238 31.25  21.265811011904763 30.864025297619047  19.545200892857142 30.09207589285714  C 17.824590773809526 29.320126488095234  16.341145833333336 28.245907738095234  15.094866071428573 26.86941964285714  C 12.881324404761903 27.167038690476186  11.165364583333334 28.269159226190478  9.946986607142858 30.17578125  C 8.728608630952381 32.082403273809526  7.914806547619048 34.477306547619044  7.505580357142858 37.36049107142857  C 9.477306547619047 40.15066964285714  11.997767857142858 42.359561011904766  15.066964285714285 43.98716517857143  C 18.136160714285715 45.614769345238095  21.44717261904762 46.42857142857143  25 46.42857142857143  C 28.552827380952383 46.42857142857143  31.863839285714285 45.614769345238095  34.933035714285715 43.98716517857143  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 389 124 )" class="stroke" mask="url(#u2150_img_cl55)" />
    <path d="M 32.57533482142857 25.43247767857143  C 34.66796875 23.33984375  35.714285714285715 20.814732142857146  35.714285714285715 17.857142857142854  C 35.714285714285715 14.89955357142857  34.66796875 12.374441964285715  32.57533482142857 10.281808035714285  C 30.482700892857146 8.189174107142854  27.957589285714285 7.142857142857145  25 7.142857142857145  C 22.042410714285715 7.142857142857145  19.517299107142858 8.189174107142854  17.424665178571427 10.281808035714285  C 15.33203125 12.374441964285715  14.285714285714285 14.89955357142857  14.285714285714285 17.857142857142854  C 14.285714285714285 20.814732142857146  15.33203125 23.33984375  17.424665178571427 25.43247767857143  C 19.517299107142858 27.52511160714286  22.042410714285715 28.57142857142857  25 28.57142857142857  C 27.957589285714285 28.57142857142857  30.482700892857146 27.52511160714286  32.57533482142857 25.43247767857143  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 389 124 )" class="stroke" mask="url(#u2150_img_cl55)" />
    <path d="M 48.018973214285715 15.29017857142857  C 49.33965773809524 18.377976190476186  50 21.614583333333332  50 25  C 50 28.385416666666664  49.33965773809524 31.617373511904756  48.018973214285715 34.695870535714285  C 46.6982886904762 37.77436755952381  44.92652529761906 40.434337797619044  42.703683035714285 42.67578125  C 40.480840773809526 44.91722470238095  37.825520833333336 46.69828869047619  34.737723214285715 48.018973214285715  C 31.6499255952381 49.33965773809524  28.404017857142854 50  25 50  C 21.614583333333336 50  18.377976190476193 49.33965773809524  15.290178571428573 48.018973214285715  C 12.202380952380953 46.69828869047619  9.542410714285714 44.921875  7.310267857142858 42.68973214285714  C 5.078125 40.457589285714285  3.3017113095238098 37.797619047619044  1.9810267857142856 34.70982142857143  C 0.6603422619047619 31.622023809523807  0 28.385416666666664  0 25  C 0 21.614583333333332  0.6603422619047619 18.377976190476186  1.9810267857142856 15.29017857142857  C 3.3017113095238098 12.202380952380947  5.078125 9.542410714285715  7.310267857142858 7.310267857142855  C 9.542410714285714 5.078125  12.202380952380953 3.3017113095238084  15.290178571428573 1.981026785714285  C 18.377976190476193 0.6603422619047616  21.614583333333336 0  25 0  C 28.385416666666668 0  31.622023809523814 0.6603422619047616  34.70982142857143 1.981026785714285  C 37.79761904761905 3.3017113095238084  40.457589285714285 5.078125  42.689732142857146 7.310267857142855  C 44.92187500000001 9.542410714285715  46.6982886904762 12.202380952380947  48.018973214285715 15.29017857142857  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 389 124 )" class="stroke" mask="url(#u2150_img_cl55)" />
  </g>
          </svg>
          <div id="u2150_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2151" class="ax_default primary_button transition notrs">
        <div id="u2151_div" class=""></div>
        <div id="u2151_text" class="text ">
          <p><span>提交审核</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2152" class="ax_default" data-left="35" data-top="202" data-width="410" data-height="28" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2153" class="ax_default label transition notrs">
          <div id="u2153_div" class=""></div>
          <div id="u2153_text" class="text ">
            <p><span>您的昵称</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u2154" class="ax_default line transition notrs">
          <svg data="images/我的认证（表单填写）/u2149.svg" id="u2154_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
          </svg>
          <div id="u2154_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u2155" class="ax_default text_field transition notrs">
          <div id="u2155_div" class=""></div>
          <input id="u2155_input" type="text" value="" class="u2155_input"/>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2156" class="ax_default _图片 transition notrs">
        <img id="u2156_img" class="img " src="images/我的认证（表单填写）/u2156.png"/>
        <div id="u2156_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2157" class="ax_default _图片 transition notrs">
        <img id="u2157_img" class="img " src="images/我的认证（表单填写）/u2157.png"/>
        <div id="u2157_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2158" class="ax_default" data-left="35" data-top="295" data-width="410" data-height="27" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2159" class="ax_default label transition notrs">
          <div id="u2159_div" class=""></div>
          <div id="u2159_text" class="text ">
            <p><span>微信号</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u2160" class="ax_default line transition notrs">
          <svg data="images/我的认证（表单填写）/u2149.svg" id="u2160_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
          </svg>
          <div id="u2160_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2161" class="ax_default label transition notrs">
          <div id="u2161_div" class=""></div>
          <div id="u2161_text" class="text ">
            <p><span>112233212333333</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2162" class="ax_default sticky_1 transition notrs">
        <div id="u2162_div" class=""></div>
        <div id="u2162_text" class="text ">
          <p><span>微信号是否可以从基本信息中获取</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2163" class="ax_default" data-left="35" data-top="335" data-width="410" data-height="30" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u2164" class="ax_default" data-left="35" data-top="338" data-width="410" data-height="27" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2165" class="ax_default label transition notrs">
            <div id="u2165_div" class=""></div>
            <div id="u2165_text" class="text ">
              <p><span>最高学历学校</span></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u2166" class="ax_default line transition notrs">
            <svg data="images/我的认证（表单填写）/u2149.svg" id="u2166_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
            </svg>
            <div id="u2166_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u2167" class="ax_default text_field transition notrs">
          <div id="u2167_div" class=""></div>
          <input id="u2167_input" type="text" value="" class="u2167_input"/>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2168" class="ax_default" data-left="35" data-top="249" data-width="410" data-height="30" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u2169" class="ax_default" data-left="35" data-top="252" data-width="410" data-height="27" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2170" class="ax_default label transition notrs">
            <div id="u2170_div" class=""></div>
            <div id="u2170_text" class="text ">
              <p><span>手机号</span></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u2171" class="ax_default line transition notrs">
            <svg data="images/我的认证（表单填写）/u2149.svg" id="u2171_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
            </svg>
            <div id="u2171_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u2172" class="ax_default text_field transition notrs">
          <div id="u2172_div" class=""></div>
          <input id="u2172_input" type="text" value="" class="u2172_input"/>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2173" class="ax_default" data-left="35" data-top="383" data-width="410" data-height="27" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u2174" class="ax_default" data-left="35" data-top="383" data-width="410" data-height="27" layer-opacity="1">

          <!-- Unnamed (组合) -->
          <div id="u2175" class="ax_default" data-left="35" data-top="383" data-width="410" data-height="27" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u2176" class="ax_default label transition notrs">
              <div id="u2176_div" class=""></div>
              <div id="u2176_text" class="text ">
                <p><span>目前在读（职）</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u2177" class="ax_default line transition notrs">
              <svg data="images/我的认证（表单填写）/u2149.svg" id="u2177_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
              </svg>
              <div id="u2177_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2178" class="ax_default box_1 transition notrs">
          <div id="u2178_div" class=""></div>
          <div id="u2178_text" class="text ">
            <p><span>本科</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2179" class="ax_default box_1 transition notrs">
          <div id="u2179_div" class=""></div>
          <div id="u2179_text" class="text ">
            <p><span>硕士</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2180" class="ax_default shape transition notrs">
          <div id="u2180_div" class=""></div>
          <div id="u2180_text" class="text ">
            <p><span>高校老师</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2181" class="ax_default box_1 transition notrs">
          <div id="u2181_div" class=""></div>
          <div id="u2181_text" class="text ">
            <p><span>博士</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2182" class="ax_default" data-left="35" data-top="426" data-width="410" data-height="30" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u2183" class="ax_default" data-left="35" data-top="429" data-width="410" data-height="27" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2184" class="ax_default label transition notrs">
            <div id="u2184_div" class=""></div>
            <div id="u2184_text" class="text ">
              <p><span>专业或研究方向</span></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u2185" class="ax_default line transition notrs">
            <svg data="images/我的认证（表单填写）/u2149.svg" id="u2185_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
            </svg>
            <div id="u2185_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u2186" class="ax_default text_field transition notrs">
          <div id="u2186_div" class=""></div>
          <input id="u2186_input" type="text" value="" class="u2186_input"/>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2187" class="ax_default sticky_1 transition notrs">
        <div id="u2187_div" class=""></div>
        <div id="u2187_text" class="text ">
          <p><span>学校先手动填写，后面改为选择项，限制30字</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2188" class="ax_default" data-left="35" data-top="643" data-width="410" data-height="186" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2189" class="ax_default label transition notrs">
          <div id="u2189_div" class=""></div>
          <div id="u2189_text" class="text ">
            <p><span>科研经历</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u2190" class="ax_default text_field transition notrs">
          <div id="u2190_div" class=""></div>
          <input id="u2190_input" type="text" value="" class="u2190_input"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2191" class="ax_default label transition notrs">
          <div id="u2191_div" class=""></div>
          <div id="u2191_text" class="text ">
            <p><span>按照时间顺序写一下个人科研经历，让学生更加信任你，平台也会</span></p><p><span>更加精准的推荐学生。需要包括时间、项目或论文名称、期刊名称等</span></p><p><span>关键信息。（1000字）</span></p><p><span>例如：2002-2023年，独作发表2篇ESG方向的SCI三区。</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2192" class="ax_default label transition notrs">
          <div id="u2192_div" class=""></div>
          <div id="u2192_text" class="text ">
            <p><span>（请保证经历的真实性，一旦发现虚假信息，责任自担）</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2193" class="ax_default label transition notrs">
        <div id="u2193_div" class=""></div>
        <div id="u2193_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2194" class="ax_default" data-left="35" data-top="856" data-width="410" data-height="184" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2195" class="ax_default label transition notrs">
          <div id="u2195_div" class=""></div>
          <div id="u2195_text" class="text ">
            <p><span>个人自述</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u2196" class="ax_default text_field transition notrs">
          <div id="u2196_div" class=""></div>
          <input id="u2196_input" type="text" value="" class="u2196_input"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2197" class="ax_default label transition notrs">
          <div id="u2197_div" class=""></div>
          <div id="u2197_text" class="text ">
            <p><span>写段话描述自己吧，以便学员更全面地了解你，包括但不限于:精彩的</span></p><p><span>人生履历、擅长的知识经验、兴趣爱好和价值观等等。（500字）</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2198" class="ax_default" data-left="35" data-top="1080" data-width="384" data-height="239" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u2199" class="ax_default" data-left="35" data-top="1080" data-width="384" data-height="239" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2200" class="ax_default label transition notrs">
            <div id="u2200_div" class=""></div>
            <div id="u2200_text" class="text ">
              <p><span>个人证件</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u2201" class="ax_default box_2 transition notrs">
            <div id="u2201_div" class=""></div>
            <div id="u2201_text" class="text ">
              <p><span>+</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u2202" class="ax_default" data-left="35" data-top="1122" data-width="173" data-height="90" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u2203" class="ax_default box_2 transition notrs">
              <div id="u2203_div" class=""></div>
              <div id="u2203_text" class="text ">
                <p><span>+</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2204" class="ax_default label transition notrs">
              <div id="u2204_div" class=""></div>
              <div id="u2204_text" class="text ">
                <p><span>身份证人像面</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u2205" class="ax_default" data-left="35" data-top="1229" data-width="173" data-height="90" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u2206" class="ax_default box_2 transition notrs">
              <div id="u2206_div" class=""></div>
              <div id="u2206_text" class="text ">
                <p><span>+</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2207" class="ax_default label transition notrs">
              <div id="u2207_div" class=""></div>
              <div id="u2207_text" class="text ">
                <p><span>学生证、毕业证、教工卡等</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u2208" class="ax_default label transition notrs">
            <div id="u2208_div" class=""></div>
            <div id="u2208_text" class="text ">
              <p><span>（个人证件仅为平台审核使用，不会对外展示）</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u2209" class="ax_default" data-left="246" data-top="1229" data-width="173" data-height="90" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u2210" class="ax_default box_2 transition notrs">
              <div id="u2210_div" class=""></div>
              <div id="u2210_text" class="text ">
                <p><span>+</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2211" class="ax_default label transition notrs">
              <div id="u2211_div" class=""></div>
              <div id="u2211_text" class="text ">
                <p><span>学生证、毕业证、教工卡等</span></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2212" class="ax_default label transition notrs">
          <div id="u2212_div" class=""></div>
          <div id="u2212_text" class="text ">
            <p><span>身份证国徽面</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2213" class="ax_default sticky_1 transition notrs">
        <div id="u2213_div" class=""></div>
        <div id="u2213_text" class="text ">
          <p><span>个人证件之上的所有信息为必填，</span></p><p><span>个人证件可以不用上传</span></p><p><span>提交的事后校验必填项</span></p><p><span>提交之后返回上一页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2214" class="ax_default sticky_4 transition notrs">
        <div id="u2214_div" class=""></div>
        <div id="u2214_text" class="text ">
          <p><span>后期需考虑科研经历核验，让提供科研证明，进行抽查</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2215" class="ax_default sticky_1 transition notrs">
        <div id="u2215_div" class=""></div>
        <div id="u2215_text" class="text ">
          <p><span>可以从基本信息中获取，也可以修改</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2216" class="ax_default sticky_1 transition notrs">
        <div id="u2216_div" class=""></div>
        <div id="u2216_text" class="text ">
          <p><span>可以和身份证不一致</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2217" class="ax_default sticky_1 transition notrs">
        <div id="u2217_div" class=""></div>
        <div id="u2217_text" class="text ">
          <p><span>限制20字</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2218" class="ax_default sticky_4 transition notrs">
        <div id="u2218_div" class=""></div>
        <div id="u2218_text" class="text ">
          <p><span>后面需要支持解绑</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2219" class="ax_default" data-left="35" data-top="494" data-width="410" data-height="108" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2220" class="ax_default label transition notrs">
          <div id="u2220_div" class=""></div>
          <div id="u2220_text" class="text ">
            <p><span>擅长辅导</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2221" class="ax_default box_1 transition notrs">
          <div id="u2221_div" class=""></div>
          <div id="u2221_text" class="text ">
            <p><span>国内论文</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2222" class="ax_default shape transition notrs">
          <div id="u2222_div" class=""></div>
          <div id="u2222_text" class="text ">
            <p><span>专业课</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2223" class="ax_default box_1 transition notrs">
          <div id="u2223_div" class=""></div>
          <div id="u2223_text" class="text ">
            <p><span>保研</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2224" class="ax_default box_1 transition notrs">
          <div id="u2224_div" class=""></div>
          <div id="u2224_text" class="text ">
            <p><span>考研</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2225" class="ax_default box_1 transition notrs">
          <div id="u2225_div" class=""></div>
          <div id="u2225_text" class="text ">
            <p><span>留学论文</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2226" class="ax_default shape transition notrs">
          <div id="u2226_div" class=""></div>
          <div id="u2226_text" class="text ">
            <p><span>留学申请</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2227" class="ax_default box_1 transition notrs">
          <div id="u2227_div" class=""></div>
          <div id="u2227_text" class="text ">
            <p><span>申博</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2228" class="ax_default box_1 transition notrs">
          <div id="u2228_div" class=""></div>
          <div id="u2228_text" class="text ">
            <p><span>竞赛</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2229" class="ax_default label transition notrs">
          <div id="u2229_div" class=""></div>
          <div id="u2229_text" class="text ">
            <p><span>（最多选4个）</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2230" class="ax_default sticky_1 transition notrs">
        <div id="u2230_div" class=""></div>
        <div id="u2230_text" class="text ">
          <p><span>支持多选，选第5个时toast提示：最多只能选4个</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2231" class="ax_default sticky_1 transition notrs">
        <div id="u2231_div" class=""></div>
        <div id="u2231_text" class="text ">
          <p><span>需要校验手机号的格式</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
