﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单列表（投递中）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单列表（投递中）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单列表（投递中）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1408" class="ax_default box_1 transition notrs">
        <div id="u1408_div" class=""></div>
        <div id="u1408_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1409" class="ax_default box_1 transition notrs">
        <div id="u1409_div" class=""></div>
        <div id="u1409_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1410" class="ax_default label transition notrs">
        <div id="u1410_div" class=""></div>
        <div id="u1410_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">&nbsp; 订单号：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">FD13455555333&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1411" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1412" class="ax_default label transition notrs">
        <div id="u1412_div" class=""></div>
        <div id="u1412_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1413" class="ax_default label transition notrs">
        <div id="u1413_div" class=""></div>
        <div id="u1413_text" class="text ">
          <p><span>我的订单</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1414" class="ax_default _图片 transition notrs">
        <img id="u1414_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1414_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1415" class="ax_default label transition notrs">
        <div id="u1415_div" class=""></div>
        <div id="u1415_text" class="text ">
          <p><span>投递中（3）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1416" class="ax_default label transition notrs">
        <div id="u1416_div" class=""></div>
        <div id="u1416_text" class="text ">
          <p><span>交付中（1）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1417" class="ax_default label transition notrs">
        <div id="u1417_div" class=""></div>
        <div id="u1417_text" class="text ">
          <p><span>已完成（2）</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1418" class="ax_default line transition notrs">
        <svg data="images/订单列表（投递中）/u1418.svg" id="u1418_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -32.5 -110.5 )">
    <path d="M 0 1.5  L 65 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 34 112 )" class="stroke" />
  </g>
        </svg>
        <div id="u1418_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1419" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1419_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1419_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1420" class="ax_default box_1 transition notrs">
              <div id="u1420_div" class=""></div>
              <div id="u1420_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1421" class="ax_default label transition notrs">
              <div id="u1421_div" class=""></div>
              <div id="u1421_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1422" class="ax_default label transition notrs">
              <div id="u1422_div" class=""></div>
              <div id="u1422_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1423" class="ax_default button transition notrs">
              <div id="u1423_div" class=""></div>
              <div id="u1423_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1424" class="ax_default primary_button transition notrs">
              <div id="u1424_div" class=""></div>
              <div id="u1424_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1419_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1419_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1425" class="ax_default box_1 transition notrs">
              <div id="u1425_div" class=""></div>
              <div id="u1425_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1426" class="ax_default label transition notrs">
              <div id="u1426_div" class=""></div>
              <div id="u1426_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1427" class="ax_default label transition notrs">
              <div id="u1427_div" class=""></div>
              <div id="u1427_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1428" class="ax_default button transition notrs">
              <div id="u1428_div" class=""></div>
              <div id="u1428_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1429" class="ax_default primary_button transition notrs">
              <div id="u1429_div" class=""></div>
              <div id="u1429_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1430" class="ax_default label transition notrs">
              <div id="u1430_div" class=""></div>
              <div id="u1430_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1431" class="ax_default text_field transition notrs">
              <div id="u1431_div" class=""></div>
              <input id="u1431_input" type="text" value="" class="u1431_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1432" class="ax_default label transition notrs">
              <div id="u1432_div" class=""></div>
              <div id="u1432_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1433" class="ax_default" data-left="28" data-top="175" data-width="107" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1434" class="ax_default label transition notrs">
          <div id="u1434_div" class=""></div>
          <div id="u1434_text" class="text ">
            <p><span>辅导项目：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1435" class="ax_default shape transition notrs">
          <div id="u1435_div" class=""></div>
          <div id="u1435_text" class="text ">
            <p><span>作业</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1436" class="ax_default" data-left="298" data-top="214" data-width="145" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1437" class="ax_default label transition notrs">
          <div id="u1437_div" class=""></div>
          <div id="u1437_text" class="text ">
            <p><span>预算区间：1000-2000元</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1438" class="ax_default label transition notrs">
        <div id="u1438_div" class=""></div>
        <div id="u1438_text" class="text ">
          <p><span>已拒绝（5）</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1439" class="ax_default" data-left="28" data-top="215" data-width="79" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1440" class="ax_default label transition notrs">
          <div id="u1440_div" class=""></div>
          <div id="u1440_text" class="text ">
            <p><span>学生：张菲菲</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1441" class="ax_default" data-left="380" data-top="175" data-width="66" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1442" class="ax_default label transition notrs">
          <div id="u1442_div" class=""></div>
          <div id="u1442_text" class="text ">
            <p><span>专业：会计</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1443" class="ax_default sticky_1 transition notrs">
        <div id="u1443_div" class=""></div>
        <div id="u1443_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">订单</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">tab</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">包括：投递中、已拒绝、交付中、已完成</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1444" class="ax_default sticky_1 transition notrs">
        <div id="u1444_div" class=""></div>
        <div id="u1444_text" class="text ">
          <p><span>订单号</span></p><p><span>订单创建时间</span></p><p><span>辅导项目</span></p><p><span>学生专业</span></p><p><span>学生姓名</span></p><p><span>预算区间</span></p>
        </div>
      </div>

      <!-- Unnamed (热区) -->
      <div id="u1445" class="ax_default">
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1446" class="ax_default sticky_1 transition notrs">
        <div id="u1446_div" class=""></div>
        <div id="u1446_text" class="text ">
          <p><span>排序：按照投递时间倒序</span></p><p><span>分页：每页10个</span></p>
        </div>
      </div>

      <!-- Unnamed (水滴标记) -->
      <div id="u1447" class="ax_default marker transition notrs">
        <svg data="images/订单列表（投递中）/u1447.svg" id="u1447_img" class="img generatedImage">

  <defs>
    <pattern id="u1447_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u1447_img_cl54">
      <path d="M 30 14  C 30 24  21 30  15 40  C 9 30  0 24  0 14  C 0 8.799999999999999  3 0  15 0  C 27 0  30 8.799999999999999  30 14  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -199 -75 )">
    <path d="M 30 14  C 30 24  21 30  15 40  C 9 30  0 24  0 14  C 0 8.799999999999999  3 0  15 0  C 27 0  30 8.799999999999999  30 14  Z " fill-rule="nonzero" fill="rgba(0, 194, 255, 1)" stroke="none" transform="matrix(1 0 0 1 203 79 )" class="fill" />
    <path d="M 30 14  C 30 24  21 30  15 40  C 9 30  0 24  0 14  C 0 8.799999999999999  3 0  15 0  C 27 0  30 8.799999999999999  30 14  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 1)" fill="none" transform="matrix(1 0 0 1 203 79 )" class="stroke" mask="url(#u1447_img_cl54)" />
  </g>
  <style>#u1447_img { filter: drop-shadow(1px 1px 2.5px rgba(0, 0, 0, 0.6980392156862745)); }</style>
        </svg>
        <div id="u1447_text" class="text ">
          <p><span>先不做</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
