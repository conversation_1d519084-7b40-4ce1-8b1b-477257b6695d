﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情 (待接单）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情__待接单）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情__待接单）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u293" class="ax_default box_1 transition notrs">
        <div id="u293_div" class=""></div>
        <div id="u293_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u294" class="ax_default label transition notrs">
        <div id="u294_div" class=""></div>
        <div id="u294_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u295" class="ax_default label transition notrs">
        <div id="u295_div" class=""></div>
        <div id="u295_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u296" class="ax_default _图片 transition notrs">
        <img id="u296_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u296_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u297" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u297_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u297_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u298" class="ax_default box_1 transition notrs">
              <div id="u298_div" class=""></div>
              <div id="u298_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u299" class="ax_default label transition notrs">
              <div id="u299_div" class=""></div>
              <div id="u299_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u300" class="ax_default label transition notrs">
              <div id="u300_div" class=""></div>
              <div id="u300_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u301" class="ax_default button transition notrs">
              <div id="u301_div" class=""></div>
              <div id="u301_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u302" class="ax_default primary_button transition notrs">
              <div id="u302_div" class=""></div>
              <div id="u302_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u297_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u297_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u303" class="ax_default box_1 transition notrs">
              <div id="u303_div" class=""></div>
              <div id="u303_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u304" class="ax_default label transition notrs">
              <div id="u304_div" class=""></div>
              <div id="u304_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u305" class="ax_default label transition notrs">
              <div id="u305_div" class=""></div>
              <div id="u305_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u306" class="ax_default button transition notrs">
              <div id="u306_div" class=""></div>
              <div id="u306_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u307" class="ax_default primary_button transition notrs">
              <div id="u307_div" class=""></div>
              <div id="u307_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u308" class="ax_default label transition notrs">
              <div id="u308_div" class=""></div>
              <div id="u308_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u309" class="ax_default text_field transition notrs">
              <div id="u309_div" class=""></div>
              <input id="u309_input" type="text" value="" class="u309_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u310" class="ax_default label transition notrs">
              <div id="u310_div" class=""></div>
              <div id="u310_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u311" class="ax_default" data-left="17.999483828313885" data-top="207" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u312" class="ax_default" data-left="36" data-top="209" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u313" class="ax_default label transition notrs">
            <div id="u313_div" class=""></div>
            <div id="u313_text" class="text ">
              <p><span>辅导项目</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u314" class="ax_default" data-left="433" data-top="207" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u315" class="ax_default label transition notrs">
            <div id="u315_div" class=""></div>
            <div id="u315_text" class="text ">
              <p><span>作业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u316" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u316_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u316_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u317" class="ax_default" data-left="16.999483828313878" data-top="126" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u318" class="ax_default" data-left="35" data-top="126" data-width="40" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u319" class="ax_default label transition notrs">
            <div id="u319_div" class=""></div>
            <div id="u319_text" class="text ">
              <p><span>订单号</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u320" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u320_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u320_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u321" class="ax_default" data-left="363" data-top="126" data-width="98" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u322" class="ax_default label transition notrs">
            <div id="u322_div" class=""></div>
            <div id="u322_text" class="text ">
              <p><span>FD13455555333</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u323" class="ax_default" data-left="16.999483828313867" data-top="168" data-width="453.00103234337223" data-height="29.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u324" class="ax_default" data-left="35" data-top="169" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u325" class="ax_default label transition notrs">
            <div id="u325_div" class=""></div>
            <div id="u325_text" class="text ">
              <p><span>发布时间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u326" class="ax_default" data-left="358" data-top="168" data-width="112" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u327" class="ax_default label transition notrs">
            <div id="u327_div" class=""></div>
            <div id="u327_text" class="text ">
              <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp;2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u328" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u328_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u328_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u329" class="ax_default" data-left="17.999483828313878" data-top="249" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u330" class="ax_default" data-left="36" data-top="251" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u331" class="ax_default label transition notrs">
            <div id="u331_div" class=""></div>
            <div id="u331_text" class="text ">
              <p><span>学习阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u332" class="ax_default" data-left="433" data-top="249" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u333" class="ax_default label transition notrs">
            <div id="u333_div" class=""></div>
            <div id="u333_text" class="text ">
              <p><span>本科</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u334" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u334_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u334_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u335" class="ax_default" data-left="17.99948382831388" data-top="284" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u336" class="ax_default" data-left="36" data-top="286" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u337" class="ax_default label transition notrs">
            <div id="u337_div" class=""></div>
            <div id="u337_text" class="text ">
              <p><span>学习位置</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u338" class="ax_default" data-left="433" data-top="284" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u339" class="ax_default label transition notrs">
            <div id="u339_div" class=""></div>
            <div id="u339_text" class="text ">
              <p><span>国内</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u340" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u340_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u340_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u341" class="ax_default" data-left="17.999483828313874" data-top="325" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u342" class="ax_default" data-left="36" data-top="327" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u343" class="ax_default label transition notrs">
            <div id="u343_div" class=""></div>
            <div id="u343_text" class="text ">
              <p><span>学习专业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u344" class="ax_default" data-left="433" data-top="325" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u345" class="ax_default label transition notrs">
            <div id="u345_div" class=""></div>
            <div id="u345_text" class="text ">
              <p><span>会计</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u346" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u346_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u346_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u347" class="ax_default" data-left="17.999483828313863" data-top="366" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u348" class="ax_default" data-left="36" data-top="368" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u349" class="ax_default label transition notrs">
            <div id="u349_div" class=""></div>
            <div id="u349_text" class="text ">
              <p><span>预算区间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u350" class="ax_default" data-left="380" data-top="366" data-width="80" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u351" class="ax_default label transition notrs">
            <div id="u351_div" class=""></div>
            <div id="u351_text" class="text ">
              <p><span>1000-2000元</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u352" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u352_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u352_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u353" class="ax_default" data-left="17.999483828313878" data-top="518" data-width="453.0010323433723" data-height="90" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u354" class="ax_default" data-left="36" data-top="518" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u355" class="ax_default label transition notrs">
            <div id="u355_div" class=""></div>
            <div id="u355_text" class="text ">
              <p><span>需求描述</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u356" class="ax_default label transition notrs">
          <div id="u356_div" class=""></div>
          <div id="u356_text" class="text ">
            <p><span>想要找一个辅导论文的老师，想要找一个辅导论文的老师想要找一个</span></p><p><span>辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想</span></p><p><span>要找一个辅导论文的老师</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u357" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u357_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u357_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u358" class="ax_default" data-left="36" data-top="400" data-width="53" data-height="18" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u359" class="ax_default label transition notrs">
          <div id="u359_div" class=""></div>
          <div id="u359_text" class="text ">
            <p><span>老师要求</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u360" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
      </div>

      <!-- Unnamed (线段) -->
      <div id="u361" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u316.svg" id="u361_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
        </svg>
        <div id="u361_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u362" class="ax_default label transition notrs">
        <div id="u362_div" class=""></div>
        <div id="u362_text" class="text ">
          <p><span>老师情况</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u363" class="ax_default shape transition notrs">
        <div id="u363_div" class=""></div>
        <div id="u363_text" class="text ">
          <p><span>博士</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u364" class="ax_default label transition notrs">
        <div id="u364_div" class=""></div>
        <div id="u364_text" class="text ">
          <p><span>是否留学</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u365" class="ax_default box_1 transition notrs">
        <div id="u365_div" class=""></div>
        <div id="u365_text" class="text ">
          <p><span>不要求</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u366" class="ax_default label transition notrs">
        <div id="u366_div" class=""></div>
        <div id="u366_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u367" class="ax_default label transition notrs">
        <div id="u367_div" class=""></div>
        <div id="u367_text" class="text ">
          <p><span>投递人员（30）</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u368" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u368_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u368_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u369" class="ax_default primary_button transition notrs">
        <div id="u369_div" class=""></div>
        <div id="u369_text" class="text ">
          <p><span>修改信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u370" class="ax_default button transition notrs">
        <div id="u370_div" class=""></div>
        <div id="u370_text" class="text ">
          <p><span>删除订单</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u371" class="ax_default sticky_1 transition notrs">
        <div id="u371_div" class=""></div>
        <div id="u371_text" class="text ">
          <p><span>删除订单后，会发站内消息，不显示在订单列表</span></p><p><span style="color:#000000;"><br></span></p><p><span style="color:#000000;">修改信息规则：当有人投递后不允许修改信息，按钮置灰不可点击</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u372" class="ax_default" data-left="17.999483828313867" data-top="627" data-width="453.0010323433722" data-height="90" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u373" class="ax_default label transition notrs">
          <div id="u373_div" class=""></div>
          <div id="u373_text" class="text ">
            <p><span>希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，</span></p><p><span>有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热</span></p><p><span>情专业，有耐心。</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u374" class="ax_default" data-left="30" data-top="627" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u375" class="ax_default label transition notrs">
            <div id="u375_div" class=""></div>
            <div id="u375_text" class="text ">
              <p><span>其他说明</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u376" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u376_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u376_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- 删除订单确认 (动态面板) -->
      <div id="u377" class="ax_default ax_default_hidden" data-label="删除订单确认" style="display:none; visibility: hidden">
        <div id="u377_state0" class="panel_state" data-label="State 1" style="">
          <div id="u377_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u378" class="ax_default box_1 transition notrs">
              <div id="u378_div" class=""></div>
              <div id="u378_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u379" class="ax_default _一级标题 transition notrs">
              <div id="u379_div" class=""></div>
              <div id="u379_text" class="text ">
                <p><span>是否确认删除订单？</span></p><p><span>删除后不可恢复</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u380" class="ax_default line transition notrs">
              <svg data="images/订单详情__待接单）/u380.svg" id="u380_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -1 -111 )">
    <path d="M 0 0.5  L 296 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(170, 170, 170, 1)" fill="none" transform="matrix(1 0 0 1 1 111 )" class="stroke" />
  </g>
              </svg>
              <div id="u380_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u381" class="ax_default link_button transition notrs">
              <div id="u381_div" class=""></div>
              <div id="u381_text" class="text ">
                <p><span>确认</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u382" class="ax_default link_button transition notrs">
              <div id="u382_div" class=""></div>
              <div id="u382_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
