﻿<!DOCTYPE html>
<html>
  <head>
    <title>待评价</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/待评价/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/待评价/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u775" class="ax_default box_1 transition notrs">
        <div id="u775_div" class=""></div>
        <div id="u775_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u776" class="ax_default box_1 transition notrs">
        <div id="u776_div" class=""></div>
        <div id="u776_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u777" class="ax_default label transition notrs">
        <div id="u777_div" class=""></div>
        <div id="u777_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">&nbsp; 订单号：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">FD13455555333&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u778" class="ax_default label transition notrs">
        <div id="u778_div" class=""></div>
        <div id="u778_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u779" class="ax_default label transition notrs">
        <div id="u779_div" class=""></div>
        <div id="u779_text" class="text ">
          <p><span>我的订单</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u780" class="ax_default _图片 transition notrs">
        <img id="u780_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u780_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u781" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u781_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u781_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u782" class="ax_default box_1 transition notrs">
              <div id="u782_div" class=""></div>
              <div id="u782_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u783" class="ax_default label transition notrs">
              <div id="u783_div" class=""></div>
              <div id="u783_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u784" class="ax_default label transition notrs">
              <div id="u784_div" class=""></div>
              <div id="u784_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u785" class="ax_default button transition notrs">
              <div id="u785_div" class=""></div>
              <div id="u785_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u786" class="ax_default primary_button transition notrs">
              <div id="u786_div" class=""></div>
              <div id="u786_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u781_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u781_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u787" class="ax_default box_1 transition notrs">
              <div id="u787_div" class=""></div>
              <div id="u787_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u788" class="ax_default label transition notrs">
              <div id="u788_div" class=""></div>
              <div id="u788_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u789" class="ax_default label transition notrs">
              <div id="u789_div" class=""></div>
              <div id="u789_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u790" class="ax_default button transition notrs">
              <div id="u790_div" class=""></div>
              <div id="u790_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u791" class="ax_default primary_button transition notrs">
              <div id="u791_div" class=""></div>
              <div id="u791_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u792" class="ax_default label transition notrs">
              <div id="u792_div" class=""></div>
              <div id="u792_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u793" class="ax_default text_field transition notrs">
              <div id="u793_div" class=""></div>
              <input id="u793_input" type="text" value="" class="u793_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u794" class="ax_default label transition notrs">
              <div id="u794_div" class=""></div>
              <div id="u794_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u795" class="ax_default" data-left="28" data-top="175" data-width="107" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u796" class="ax_default label transition notrs">
          <div id="u796_div" class=""></div>
          <div id="u796_text" class="text ">
            <p><span>辅导项目：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u797" class="ax_default shape transition notrs">
          <div id="u797_div" class=""></div>
          <div id="u797_text" class="text ">
            <p><span>作业</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u798" class="ax_default" data-left="343" data-top="175" data-width="97" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u799" class="ax_default label transition notrs">
          <div id="u799_div" class=""></div>
          <div id="u799_text" class="text ">
            <p><span>总金额：2000元</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u800" class="ax_default" data-left="28" data-top="212" data-width="105" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u801" class="ax_default label transition notrs">
          <div id="u801_div" class=""></div>
          <div id="u801_text" class="text ">
            <p><span>交付阶段：已完成</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u802" class="ax_default primary_button transition notrs">
        <div id="u802_div" class=""></div>
        <div id="u802_text" class="text ">
          <p><span>去评价</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u803" class="ax_default label transition notrs">
        <div id="u803_div" class=""></div>
        <div id="u803_text" class="text ">
          <p><span>待接单</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u804" class="ax_default label transition notrs">
        <div id="u804_div" class=""></div>
        <div id="u804_text" class="text ">
          <p><span>交付中</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u805" class="ax_default label transition notrs">
        <div id="u805_div" class=""></div>
        <div id="u805_text" class="text ">
          <p><span>待评价</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u806" class="ax_default label transition notrs">
        <div id="u806_div" class=""></div>
        <div id="u806_text" class="text ">
          <p><span>已评价</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u807" class="ax_default line transition notrs">
        <svg data="images/订单列表/u251.svg" id="u807_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -43.5 -110.5 )">
    <path d="M 0 1.5  L 54 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 45 112 )" class="stroke" />
  </g>
        </svg>
        <div id="u807_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (热区) -->
      <div id="u808" class="ax_default">
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
