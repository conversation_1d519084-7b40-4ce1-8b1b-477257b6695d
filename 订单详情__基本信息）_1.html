﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情 (基本信息）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情__基本信息）_1/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情__基本信息）_1/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u809" class="ax_default box_1 transition notrs">
        <div id="u809_div" class=""></div>
        <div id="u809_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u810" class="ax_default label transition notrs">
        <div id="u810_div" class=""></div>
        <div id="u810_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u811" class="ax_default label transition notrs">
        <div id="u811_div" class=""></div>
        <div id="u811_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u812" class="ax_default _图片 transition notrs">
        <img id="u812_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u812_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u813" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u813_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u813_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u814" class="ax_default box_1 transition notrs">
              <div id="u814_div" class=""></div>
              <div id="u814_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u815" class="ax_default label transition notrs">
              <div id="u815_div" class=""></div>
              <div id="u815_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u816" class="ax_default label transition notrs">
              <div id="u816_div" class=""></div>
              <div id="u816_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u817" class="ax_default button transition notrs">
              <div id="u817_div" class=""></div>
              <div id="u817_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u818" class="ax_default primary_button transition notrs">
              <div id="u818_div" class=""></div>
              <div id="u818_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u813_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u813_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u819" class="ax_default box_1 transition notrs">
              <div id="u819_div" class=""></div>
              <div id="u819_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u820" class="ax_default label transition notrs">
              <div id="u820_div" class=""></div>
              <div id="u820_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u821" class="ax_default label transition notrs">
              <div id="u821_div" class=""></div>
              <div id="u821_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u822" class="ax_default button transition notrs">
              <div id="u822_div" class=""></div>
              <div id="u822_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u823" class="ax_default primary_button transition notrs">
              <div id="u823_div" class=""></div>
              <div id="u823_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u824" class="ax_default label transition notrs">
              <div id="u824_div" class=""></div>
              <div id="u824_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u825" class="ax_default text_field transition notrs">
              <div id="u825_div" class=""></div>
              <input id="u825_input" type="text" value="" class="u825_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u826" class="ax_default label transition notrs">
              <div id="u826_div" class=""></div>
              <div id="u826_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u827" class="ax_default" data-left="17.999483828313885" data-top="207" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u828" class="ax_default" data-left="36" data-top="209" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u829" class="ax_default label transition notrs">
            <div id="u829_div" class=""></div>
            <div id="u829_text" class="text ">
              <p><span>辅导项目</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u830" class="ax_default" data-left="433" data-top="207" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u831" class="ax_default label transition notrs">
            <div id="u831_div" class=""></div>
            <div id="u831_text" class="text ">
              <p><span>作业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u832" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u832_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u832_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u833" class="ax_default" data-left="16.999483828313878" data-top="126" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u834" class="ax_default" data-left="35" data-top="126" data-width="40" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u835" class="ax_default label transition notrs">
            <div id="u835_div" class=""></div>
            <div id="u835_text" class="text ">
              <p><span>订单号</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u836" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u836_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u836_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u837" class="ax_default" data-left="363" data-top="126" data-width="96" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u838" class="ax_default label transition notrs">
            <div id="u838_div" class=""></div>
            <div id="u838_text" class="text ">
              <p><span>JS13455555333</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u839" class="ax_default" data-left="16.999483828313867" data-top="168" data-width="453.00103234337223" data-height="29.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u840" class="ax_default" data-left="35" data-top="169" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u841" class="ax_default label transition notrs">
            <div id="u841_div" class=""></div>
            <div id="u841_text" class="text ">
              <p><span>发布时间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u842" class="ax_default" data-left="358" data-top="168" data-width="112" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u843" class="ax_default label transition notrs">
            <div id="u843_div" class=""></div>
            <div id="u843_text" class="text ">
              <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp;2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u844" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u844_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u844_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u845" class="ax_default" data-left="17.999483828313878" data-top="249" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u846" class="ax_default" data-left="36" data-top="251" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u847" class="ax_default label transition notrs">
            <div id="u847_div" class=""></div>
            <div id="u847_text" class="text ">
              <p><span>学习阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u848" class="ax_default" data-left="433" data-top="249" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u849" class="ax_default label transition notrs">
            <div id="u849_div" class=""></div>
            <div id="u849_text" class="text ">
              <p><span>本科</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u850" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u850_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u850_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u851" class="ax_default" data-left="17.99948382831388" data-top="284" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u852" class="ax_default" data-left="36" data-top="286" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u853" class="ax_default label transition notrs">
            <div id="u853_div" class=""></div>
            <div id="u853_text" class="text ">
              <p><span>学习位置</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u854" class="ax_default" data-left="433" data-top="284" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u855" class="ax_default label transition notrs">
            <div id="u855_div" class=""></div>
            <div id="u855_text" class="text ">
              <p><span>国内</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u856" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u856_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u856_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u857" class="ax_default" data-left="17.999483828313874" data-top="325" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u858" class="ax_default" data-left="36" data-top="327" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u859" class="ax_default label transition notrs">
            <div id="u859_div" class=""></div>
            <div id="u859_text" class="text ">
              <p><span>学习专业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u860" class="ax_default" data-left="433" data-top="325" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u861" class="ax_default label transition notrs">
            <div id="u861_div" class=""></div>
            <div id="u861_text" class="text ">
              <p><span>会计</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u862" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u862_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u862_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u863" class="ax_default" data-left="17.999483828313863" data-top="366" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u864" class="ax_default" data-left="36" data-top="368" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u865" class="ax_default label transition notrs">
            <div id="u865_div" class=""></div>
            <div id="u865_text" class="text ">
              <p><span>预算区间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u866" class="ax_default" data-left="380" data-top="366" data-width="80" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u867" class="ax_default label transition notrs">
            <div id="u867_div" class=""></div>
            <div id="u867_text" class="text ">
              <p><span>1000-2000元</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u868" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u868_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u868_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u869" class="ax_default" data-left="17.999483828313874" data-top="527" data-width="453.00103234337223" data-height="90" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u870" class="ax_default" data-left="36" data-top="527" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u871" class="ax_default label transition notrs">
            <div id="u871_div" class=""></div>
            <div id="u871_text" class="text ">
              <p><span>需求描述</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u872" class="ax_default label transition notrs">
          <div id="u872_div" class=""></div>
          <div id="u872_text" class="text ">
            <p><span>想要找一个辅导论文的老师，想要找一个辅导论文的老师想要找一个</span></p><p><span>辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想</span></p><p><span>要找一个辅导论文的老师</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u873" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u873_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u873_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u874" class="ax_default" data-left="17.999483828313878" data-top="407" data-width="453.0010323433723" data-height="28.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u875" class="ax_default" data-left="36" data-top="407" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u876" class="ax_default label transition notrs">
            <div id="u876_div" class=""></div>
            <div id="u876_text" class="text ">
              <p><span>老师要求</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u877" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
        </div>

        <!-- Unnamed (线段) -->
        <div id="u878" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u878_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u878_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u879" class="ax_default" data-left="36" data-top="449" data-width="424" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u880" class="ax_default label transition notrs">
          <div id="u880_div" class=""></div>
          <div id="u880_text" class="text ">
            <p><span>老师情况</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u881" class="ax_default shape transition notrs">
          <div id="u881_div" class=""></div>
          <div id="u881_text" class="text ">
            <p><span>博士</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u882" class="ax_default" data-left="36" data-top="483" data-width="424" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u883" class="ax_default label transition notrs">
          <div id="u883_div" class=""></div>
          <div id="u883_text" class="text ">
            <p><span>是否留学</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u884" class="ax_default box_1 transition notrs">
          <div id="u884_div" class=""></div>
          <div id="u884_text" class="text ">
            <p><span>不要求</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u885" class="ax_default label transition notrs">
        <div id="u885_div" class=""></div>
        <div id="u885_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u886" class="ax_default label transition notrs">
        <div id="u886_div" class=""></div>
        <div id="u886_text" class="text ">
          <p><span>交付信息</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u887" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u887_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u887_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u888" class="ax_default" data-left="17.999483828313885" data-top="635" data-width="453.0010323433723" data-height="90" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u889" class="ax_default label transition notrs">
          <div id="u889_div" class=""></div>
          <div id="u889_text" class="text ">
            <p><span>希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，</span></p><p><span>有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热</span></p><p><span>情专业，有耐心。</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u890" class="ax_default" data-left="30" data-top="635" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u891" class="ax_default label transition notrs">
            <div id="u891_div" class=""></div>
            <div id="u891_text" class="text ">
              <p><span>其他说明</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u892" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u892_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u892_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u893" class="ax_default primary_button transition notrs">
        <div id="u893_div" class=""></div>
        <div id="u893_text" class="text ">
          <p><span>去评价</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
