﻿<!DOCTYPE html>
<html>
  <head>
    <title>需求详情</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/需求详情/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/需求详情/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1292" class="ax_default box_1 transition notrs">
        <div id="u1292_div" class=""></div>
        <div id="u1292_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1293" class="ax_default label transition notrs">
        <div id="u1293_div" class=""></div>
        <div id="u1293_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1294" class="ax_default label transition notrs">
        <div id="u1294_div" class=""></div>
        <div id="u1294_text" class="text ">
          <p><span>需求详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1295" class="ax_default _图片 transition notrs">
        <img id="u1295_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1295_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1296" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1296_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1296_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1297" class="ax_default box_1 transition notrs">
              <div id="u1297_div" class=""></div>
              <div id="u1297_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1298" class="ax_default label transition notrs">
              <div id="u1298_div" class=""></div>
              <div id="u1298_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1299" class="ax_default label transition notrs">
              <div id="u1299_div" class=""></div>
              <div id="u1299_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1300" class="ax_default button transition notrs">
              <div id="u1300_div" class=""></div>
              <div id="u1300_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1301" class="ax_default primary_button transition notrs">
              <div id="u1301_div" class=""></div>
              <div id="u1301_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1296_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1296_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1302" class="ax_default box_1 transition notrs">
              <div id="u1302_div" class=""></div>
              <div id="u1302_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1303" class="ax_default label transition notrs">
              <div id="u1303_div" class=""></div>
              <div id="u1303_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1304" class="ax_default label transition notrs">
              <div id="u1304_div" class=""></div>
              <div id="u1304_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1305" class="ax_default button transition notrs">
              <div id="u1305_div" class=""></div>
              <div id="u1305_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1306" class="ax_default primary_button transition notrs">
              <div id="u1306_div" class=""></div>
              <div id="u1306_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1307" class="ax_default label transition notrs">
              <div id="u1307_div" class=""></div>
              <div id="u1307_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1308" class="ax_default text_field transition notrs">
              <div id="u1308_div" class=""></div>
              <input id="u1308_input" type="text" value="" class="u1308_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1309" class="ax_default label transition notrs">
              <div id="u1309_div" class=""></div>
              <div id="u1309_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1310" class="ax_default" data-left="17.999483828313878" data-top="127" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1311" class="ax_default" data-left="36" data-top="129" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1312" class="ax_default label transition notrs">
            <div id="u1312_div" class=""></div>
            <div id="u1312_text" class="text ">
              <p><span>辅导项目</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1313" class="ax_default" data-left="433" data-top="127" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1314" class="ax_default label transition notrs">
            <div id="u1314_div" class=""></div>
            <div id="u1314_text" class="text ">
              <p><span>作业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1315" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1315_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1315_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1316" class="ax_default" data-left="16.999483828313892" data-top="88" data-width="453.00103234337223" data-height="29.627173050050104" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1317" class="ax_default" data-left="35" data-top="89" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1318" class="ax_default label transition notrs">
            <div id="u1318_div" class=""></div>
            <div id="u1318_text" class="text ">
              <p><span>发布时间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1319" class="ax_default" data-left="358" data-top="88" data-width="112" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1320" class="ax_default label transition notrs">
            <div id="u1320_div" class=""></div>
            <div id="u1320_text" class="text ">
              <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp;2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1321" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1321_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1321_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1322" class="ax_default" data-left="17.999483828313874" data-top="169" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1323" class="ax_default" data-left="36" data-top="171" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1324" class="ax_default label transition notrs">
            <div id="u1324_div" class=""></div>
            <div id="u1324_text" class="text ">
              <p><span>学习阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1325" class="ax_default" data-left="433" data-top="169" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1326" class="ax_default label transition notrs">
            <div id="u1326_div" class=""></div>
            <div id="u1326_text" class="text ">
              <p><span>本科</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1327" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1327_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1327_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1328" class="ax_default" data-left="17.999483828313878" data-top="204" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1329" class="ax_default" data-left="36" data-top="206" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1330" class="ax_default label transition notrs">
            <div id="u1330_div" class=""></div>
            <div id="u1330_text" class="text ">
              <p><span>学习位置</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1331" class="ax_default" data-left="433" data-top="204" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1332" class="ax_default label transition notrs">
            <div id="u1332_div" class=""></div>
            <div id="u1332_text" class="text ">
              <p><span>国内</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1333" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1333_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1333_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1334" class="ax_default" data-left="17.999483828313867" data-top="245" data-width="453.0010323433722" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1335" class="ax_default" data-left="36" data-top="247" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1336" class="ax_default label transition notrs">
            <div id="u1336_div" class=""></div>
            <div id="u1336_text" class="text ">
              <p><span>学习专业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1337" class="ax_default" data-left="433" data-top="245" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1338" class="ax_default label transition notrs">
            <div id="u1338_div" class=""></div>
            <div id="u1338_text" class="text ">
              <p><span>会计</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1339" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1339_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1339_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1340" class="ax_default" data-left="17.99948382831389" data-top="286" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1341" class="ax_default" data-left="36" data-top="288" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1342" class="ax_default label transition notrs">
            <div id="u1342_div" class=""></div>
            <div id="u1342_text" class="text ">
              <p><span>预算区间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1343" class="ax_default" data-left="370" data-top="286" data-width="96" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1344" class="ax_default label transition notrs">
            <div id="u1344_div" class=""></div>
            <div id="u1344_text" class="text ">
              <p><span>10000-20000元</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1345" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1345_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1345_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1346" class="ax_default" data-left="17.99948382831388" data-top="408" data-width="453.0010323433723" data-height="90" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1347" class="ax_default" data-left="36" data-top="408" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1348" class="ax_default label transition notrs">
            <div id="u1348_div" class=""></div>
            <div id="u1348_text" class="text ">
              <p><span>需求描述</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1349" class="ax_default label transition notrs">
          <div id="u1349_div" class=""></div>
          <div id="u1349_text" class="text ">
            <p><span>想要找一个辅导论文的老师，想要找一个辅导论文的老师想要找一个</span></p><p><span>辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想</span></p><p><span>要找一个辅导论文的老师</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1350" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1350_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1350_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1351" class="ax_default" data-left="17.99948382831388" data-top="530" data-width="453.00103234337223" data-height="96" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1352" class="ax_default label transition notrs">
          <div id="u1352_div" class=""></div>
          <div id="u1352_text" class="text ">
            <p><span>希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，</span></p><p><span>有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热</span></p><p><span>情专业，有耐心。</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1353" class="ax_default" data-left="17.99948382831388" data-top="530" data-width="453.00103234337223" data-height="28.62717305005026" layer-opacity="1">

          <!-- Unnamed (组合) -->
          <div id="u1354" class="ax_default" data-left="36" data-top="530" data-width="53" data-height="18" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1355" class="ax_default label transition notrs">
              <div id="u1355_div" class=""></div>
              <div id="u1355_text" class="text ">
                <p><span>其他说明</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1356" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
          </div>

          <!-- Unnamed (线段) -->
          <div id="u1357" class="ax_default line transition notrs">
            <svg data="images/订单详情__待接单）/u316.svg" id="u1357_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
            </svg>
            <div id="u1357_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1358" class="ax_default primary_button transition notrs">
        <div id="u1358_div" class=""></div>
        <div id="u1358_text" class="text ">
          <p><span>投递</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1359" class="ax_default" data-left="17.99948382831388" data-top="330" data-width="453.00103234337223" data-height="28.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1360" class="ax_default" data-left="36" data-top="330" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1361" class="ax_default label transition notrs">
            <div id="u1361_div" class=""></div>
            <div id="u1361_text" class="text ">
              <p><span>老师学历</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1362" class="ax_default" data-left="433" data-top="330" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1363" class="ax_default label transition notrs">
            <div id="u1363_div" class=""></div>
            <div id="u1363_text" class="text ">
              <p><span>博士</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1364" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1364_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1364_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1365" class="ax_default" data-left="17.99948382831388" data-top="363" data-width="453.0010323433723" data-height="28.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1366" class="ax_default" data-left="36" data-top="363" data-width="79" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1367" class="ax_default label transition notrs">
            <div id="u1367_div" class=""></div>
            <div id="u1367_text" class="text ">
              <p><span>老师是否留学</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1368" class="ax_default" data-left="420" data-top="363" data-width="40" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1369" class="ax_default label transition notrs">
            <div id="u1369_div" class=""></div>
            <div id="u1369_text" class="text ">
              <p><span>不要求</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1370" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1370_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1370_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1371" class="ax_default label transition notrs">
        <div id="u1371_div" class=""></div>
        <div id="u1371_text" class="text ">
          <p><span>已有20人投递</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1372" class="ax_default sticky_1 transition notrs">
        <div id="u1372_div" class=""></div>
        <div id="u1372_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">投递后返回到首页，给一个</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">toast</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">提示：投递成功</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
