﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情（交付信息）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情（交付信息）_1/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情（交付信息）_1/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u894" class="ax_default box_1 transition notrs">
        <div id="u894_div" class=""></div>
        <div id="u894_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u895" class="ax_default label transition notrs">
        <div id="u895_div" class=""></div>
        <div id="u895_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u896" class="ax_default label transition notrs">
        <div id="u896_div" class=""></div>
        <div id="u896_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u897" class="ax_default _图片 transition notrs">
        <img id="u897_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u897_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u898" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u898_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u898_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u899" class="ax_default box_1 transition notrs">
              <div id="u899_div" class=""></div>
              <div id="u899_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u900" class="ax_default label transition notrs">
              <div id="u900_div" class=""></div>
              <div id="u900_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u901" class="ax_default label transition notrs">
              <div id="u901_div" class=""></div>
              <div id="u901_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u902" class="ax_default button transition notrs">
              <div id="u902_div" class=""></div>
              <div id="u902_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u903" class="ax_default primary_button transition notrs">
              <div id="u903_div" class=""></div>
              <div id="u903_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u898_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u898_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u904" class="ax_default box_1 transition notrs">
              <div id="u904_div" class=""></div>
              <div id="u904_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u905" class="ax_default label transition notrs">
              <div id="u905_div" class=""></div>
              <div id="u905_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u906" class="ax_default label transition notrs">
              <div id="u906_div" class=""></div>
              <div id="u906_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u907" class="ax_default button transition notrs">
              <div id="u907_div" class=""></div>
              <div id="u907_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u908" class="ax_default primary_button transition notrs">
              <div id="u908_div" class=""></div>
              <div id="u908_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u909" class="ax_default label transition notrs">
              <div id="u909_div" class=""></div>
              <div id="u909_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u910" class="ax_default text_field transition notrs">
              <div id="u910_div" class=""></div>
              <input id="u910_input" type="text" value="" class="u910_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u911" class="ax_default label transition notrs">
              <div id="u911_div" class=""></div>
              <div id="u911_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u912" class="ax_default label transition notrs">
        <div id="u912_div" class=""></div>
        <div id="u912_text" class="text ">
          <p><span>第1阶段</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u913" class="ax_default label transition notrs">
        <div id="u913_div" class=""></div>
        <div id="u913_text" class="text ">
          <p><span>1.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u914" class="ax_default label transition notrs">
        <div id="u914_div" class=""></div>
        <div id="u914_text" class="text ">
          <p><span>交付阶段</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u915" class="ax_default" data-left="128" data-top="144" data-width="62" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u916" class="ax_default label transition notrs">
          <div id="u916_div" class=""></div>
          <div id="u916_text" class="text ">
            <p><span>阶段费用</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u917" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u316.svg" id="u917_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
        </svg>
        <div id="u917_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u918" class="ax_default label transition notrs">
        <div id="u918_div" class=""></div>
        <div id="u918_text" class="text ">
          <p><span>第2阶段</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u919" class="ax_default label transition notrs">
        <div id="u919_div" class=""></div>
        <div id="u919_text" class="text ">
          <p><span>已验收</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u920" class="ax_default" data-left="18.99948382831387" data-top="101" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u921" class="ax_default" data-left="37" data-top="103" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u922" class="ax_default label transition notrs">
            <div id="u922_div" class=""></div>
            <div id="u922_text" class="text ">
              <p><span>辅导老师</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u923" class="ax_default" data-left="421" data-top="101" data-width="35" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u924" class="ax_default label transition notrs">
            <div id="u924_div" class=""></div>
            <div id="u924_text" class="text ">
              <p><span>张伟&gt;</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u925" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u925_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u925_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u926" class="ax_default" data-left="37" data-top="258" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u927" class="ax_default" data-left="37" data-top="258" data-width="48" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u928" class="ax_default label transition notrs">
            <div id="u928_div" class=""></div>
            <div id="u928_text" class="text ">
              <p><span>第3阶段</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u929" class="ax_default" data-left="37" data-top="292" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u930" class="ax_default label transition notrs">
          <div id="u930_div" class=""></div>
          <div id="u930_text" class="text ">
            <p><span>第4阶段</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u931" class="ax_default" data-left="39" data-top="323" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u932" class="ax_default label transition notrs">
          <div id="u932_div" class=""></div>
          <div id="u932_text" class="text ">
            <p><span>第5阶段</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u933" class="ax_default label transition notrs">
        <div id="u933_div" class=""></div>
        <div id="u933_text" class="text ">
          <p><span>注释：</span></p><p><span>1、每个阶段需要先支付，老师收到支付信息后才会开始作业。</span></p><p><span>2、老师提交阶段交付时，请先确认是否达到约定的标准，达标后再点击确认验收。</span></p><p><span>3、所有阶段都是已验收后辅导老师才能提现。</span></p><p><span>4、如果和辅导老师之前差生分歧可以找客服进行协商。</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u934" class="ax_default" data-left="225" data-top="144" data-width="57" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u935" class="ax_default label transition notrs">
          <div id="u935_div" class=""></div>
          <div id="u935_text" class="text ">
            <p><span>阶段状态</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u936" class="ax_default" data-left="386" data-top="144" data-width="53" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u937" class="ax_default label transition notrs">
          <div id="u937_div" class=""></div>
          <div id="u937_text" class="text ">
            <p><span>操作</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u938" class="ax_default label transition notrs">
        <div id="u938_div" class=""></div>
        <div id="u938_text" class="text ">
          <p><span>已验收</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u939" class="ax_default label transition notrs">
        <div id="u939_div" class=""></div>
        <div id="u939_text" class="text ">
          <p><span>2.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u940" class="ax_default label transition notrs">
        <div id="u940_div" class=""></div>
        <div id="u940_text" class="text ">
          <p><span>3.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u941" class="ax_default label transition notrs">
        <div id="u941_div" class=""></div>
        <div id="u941_text" class="text ">
          <p><span>4.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u942" class="ax_default label transition notrs">
        <div id="u942_div" class=""></div>
        <div id="u942_text" class="text ">
          <p><span>3.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u943" class="ax_default label transition notrs">
        <div id="u943_div" class=""></div>
        <div id="u943_text" class="text ">
          <p><span>已验收</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u944" class="ax_default label transition notrs">
        <div id="u944_div" class=""></div>
        <div id="u944_text" class="text ">
          <p><span>已验收</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u945" class="ax_default label transition notrs">
        <div id="u945_div" class=""></div>
        <div id="u945_text" class="text ">
          <p><span>已验收</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u946" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u316.svg" id="u946_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
        </svg>
        <div id="u946_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u947" class="ax_default" data-left="39" data-top="364" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u948" class="ax_default label transition notrs">
          <div id="u948_div" class=""></div>
          <div id="u948_text" class="text ">
            <p><span>共5阶段</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u949" class="ax_default" data-left="119" data-top="364" data-width="40" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u950" class="ax_default label transition notrs">
          <div id="u950_div" class=""></div>
          <div id="u950_text" class="text ">
            <p><span>13.000</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u951" class="ax_default" data-left="228" data-top="364" data-width="40" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u952" class="ax_default label transition notrs">
          <div id="u952_div" class=""></div>
          <div id="u952_text" class="text ">
            <p><span>已验收</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u953" class="ax_default label transition notrs">
        <div id="u953_div" class=""></div>
        <div id="u953_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u954" class="ax_default label transition notrs">
        <div id="u954_div" class=""></div>
        <div id="u954_text" class="text ">
          <p><span>交付信息</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u955" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u955_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u955_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u956" class="ax_default primary_button transition notrs">
        <div id="u956_div" class=""></div>
        <div id="u956_text" class="text ">
          <p><span>去评价</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
