﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="100px" height="51px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip58">
      <path d="M 0 25.5  C 0 11.219999999999999  21.999999999999996 0  50 0  C 78 0  100 11.219999999999999  100 25.5  C 100 39.78  78 51  50 51  C 21.999999999999996 51  0 39.78  0 25.5  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -185 -123 )">
    <path d="M 0 25.5  C 0 11.219999999999999  21.999999999999996 0  50 0  C 78 0  100 11.219999999999999  100 25.5  C 100 39.78  78 51  50 51  C 21.999999999999996 51  0 39.78  0 25.5  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 185 123 )" class="fill" />
    <path d="M 0 25.5  C 0 11.219999999999999  21.999999999999996 0  50 0  C 78 0  100 11.219999999999999  100 25.5  C 100 39.78  78 51  50 51  C 21.999999999999996 51  0 39.78  0 25.5  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 185 123 )" class="stroke" mask="url(#Clip58)" />
  </g>
</svg>