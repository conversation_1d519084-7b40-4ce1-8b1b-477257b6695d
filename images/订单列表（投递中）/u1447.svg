﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40px" height="50px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip54">
      <path d="M 30 14  C 30 24  21 30  15 40  C 9 30  0 24  0 14  C 0 8.799999999999999  3 0  15 0  C 27 0  30 8.799999999999999  30 14  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -199 -75 )">
    <path d="M 30 14  C 30 24  21 30  15 40  C 9 30  0 24  0 14  C 0 8.799999999999999  3 0  15 0  C 27 0  30 8.799999999999999  30 14  Z " fill-rule="nonzero" fill="rgba(0, 194, 255, 1)" stroke="none" transform="matrix(1 0 0 1 203 79 )" class="fill" />
    <path d="M 30 14  C 30 24  21 30  15 40  C 9 30  0 24  0 14  C 0 8.799999999999999  3 0  15 0  C 27 0  30 8.799999999999999  30 14  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 1)" fill="none" transform="matrix(1 0 0 1 203 79 )" class="stroke" mask="url(#Clip54)" />
  </g>
  <style>svg { filter: drop-shadow(1px 1px 2.5px rgba(0, 0, 0, 0.6980392156862745)); }</style>
</svg>