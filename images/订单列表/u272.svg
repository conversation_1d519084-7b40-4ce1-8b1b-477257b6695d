﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="33px" height="32px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip52">
      <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -28 -226 )">
    <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 28 226 )" class="fill" />
    <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 28 226 )" class="stroke" mask="url(#Clip52)" />
  </g>
</svg>