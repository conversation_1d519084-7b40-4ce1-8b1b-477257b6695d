﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="31px" height="31px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip53">
      <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -418 -281 )">
    <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " fill-rule="nonzero" fill="rgba(2, 167, 240, 1)" stroke="none" transform="matrix(1 0 0 1 418 281 )" class="fill" />
    <path d="M 0 15.5  C 0 6.819999999999999  6.819999999999999 0  15.5 0  C 24.18 0  31 6.819999999999999  31 15.5  C 31 24.18  24.18 31  15.5 31  C 6.819999999999999 31  0 24.18  0 15.5  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 418 281 )" class="stroke" mask="url(#Clip53)" />
  </g>
</svg>