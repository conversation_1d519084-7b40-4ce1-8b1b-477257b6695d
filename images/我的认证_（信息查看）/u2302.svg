﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="245px" height="27px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip57">
      <path d="M 0 19.058823529411764  L 0 7.9411764705882355  C 0 3.4941176470588284  1.3146341463414906 0  2.9878048780487805 0  L 242.0121951219512 0  C 243.6853658536585 0  245 3.4941176470588284  245 7.9411764705882355  L 245 19.058823529411764  C 245 23.50588235294117  243.6853658536585 27  242.0121951219512 27  L 130.74634146341637 27  L 2.9878048780487805 27  C 1.3146341463414906 27  0 23.50588235294117  0 19.058823529411764  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -915 -72 )">
    <path d="M 0 19.058823529411764  L 0 7.9411764705882355  C 0 3.4941176470588284  1.3146341463414906 0  2.9878048780487805 0  L 242.0121951219512 0  C 243.6853658536585 0  245 3.4941176470588284  245 7.9411764705882355  L 245 19.058823529411764  C 245 23.50588235294117  243.6853658536585 27  242.0121951219512 27  L 130.74634146341637 27  L 2.9878048780487805 27  C 1.3146341463414906 27  0 23.50588235294117  0 19.058823529411764  Z " fill-rule="nonzero" fill="rgba(215, 215, 215, 1)" stroke="none" transform="matrix(1 0 0 1 915 72 )" class="fill" />
    <path d="M 0 19.058823529411764  L 0 7.9411764705882355  C 0 3.4941176470588284  1.3146341463414906 0  2.9878048780487805 0  L 242.0121951219512 0  C 243.6853658536585 0  245 3.4941176470588284  245 7.9411764705882355  L 245 19.058823529411764  C 245 23.50588235294117  243.6853658536585 27  242.0121951219512 27  L 130.74634146341637 27  L 2.9878048780487805 27  C 1.3146341463414906 27  0 23.50588235294117  0 19.058823529411764  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 915 72 )" class="stroke" mask="url(#Clip57)" />
  </g>
</svg>