﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="94px" height="94px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="BGPattern" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="Clip51">
      <path d="M 0 47  C 0 20.679999999999996  20.679999999999996 0  47 0  C 73.32000000000001 0  94 20.679999999999996  94 47  C 94 73.32000000000001  73.32000000000001 94  47 94  C 20.679999999999996 94  0 73.32000000000001  0 47  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -205 -136 )">
    <path d="M 0 47  C 0 20.679999999999996  20.679999999999996 0  47 0  C 73.32000000000001 0  94 20.679999999999996  94 47  C 94 73.32000000000001  73.32000000000001 94  47 94  C 20.679999999999996 94  0 73.32000000000001  0 47  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 205 136 )" class="fill" />
    <path d="M 0 47  C 0 20.679999999999996  20.679999999999996 0  47 0  C 73.32000000000001 0  94 20.679999999999996  94 47  C 94 73.32000000000001  73.32000000000001 94  47 94  C 20.679999999999996 94  0 73.32000000000001  0 47  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 205 136 )" class="stroke" mask="url(#Clip51)" />
  </g>
</svg>