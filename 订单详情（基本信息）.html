﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情（基本信息）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情（基本信息）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情（基本信息）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1032" class="ax_default box_1 transition notrs">
        <div id="u1032_div" class=""></div>
        <div id="u1032_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1033" class="ax_default label transition notrs">
        <div id="u1033_div" class=""></div>
        <div id="u1033_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1034" class="ax_default label transition notrs">
        <div id="u1034_div" class=""></div>
        <div id="u1034_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1035" class="ax_default _图片 transition notrs">
        <img id="u1035_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1035_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1036" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1036_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1036_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1037" class="ax_default box_1 transition notrs">
              <div id="u1037_div" class=""></div>
              <div id="u1037_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1038" class="ax_default label transition notrs">
              <div id="u1038_div" class=""></div>
              <div id="u1038_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1039" class="ax_default label transition notrs">
              <div id="u1039_div" class=""></div>
              <div id="u1039_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1040" class="ax_default button transition notrs">
              <div id="u1040_div" class=""></div>
              <div id="u1040_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1041" class="ax_default primary_button transition notrs">
              <div id="u1041_div" class=""></div>
              <div id="u1041_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1036_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1036_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1042" class="ax_default box_1 transition notrs">
              <div id="u1042_div" class=""></div>
              <div id="u1042_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1043" class="ax_default label transition notrs">
              <div id="u1043_div" class=""></div>
              <div id="u1043_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1044" class="ax_default label transition notrs">
              <div id="u1044_div" class=""></div>
              <div id="u1044_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1045" class="ax_default button transition notrs">
              <div id="u1045_div" class=""></div>
              <div id="u1045_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1046" class="ax_default primary_button transition notrs">
              <div id="u1046_div" class=""></div>
              <div id="u1046_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1047" class="ax_default label transition notrs">
              <div id="u1047_div" class=""></div>
              <div id="u1047_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1048" class="ax_default text_field transition notrs">
              <div id="u1048_div" class=""></div>
              <input id="u1048_input" type="text" value="" class="u1048_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1049" class="ax_default label transition notrs">
              <div id="u1049_div" class=""></div>
              <div id="u1049_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1050" class="ax_default" data-left="17.999483828313885" data-top="207" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1051" class="ax_default" data-left="36" data-top="209" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1052" class="ax_default label transition notrs">
            <div id="u1052_div" class=""></div>
            <div id="u1052_text" class="text ">
              <p><span>辅导项目</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1053" class="ax_default" data-left="433" data-top="207" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1054" class="ax_default label transition notrs">
            <div id="u1054_div" class=""></div>
            <div id="u1054_text" class="text ">
              <p><span>作业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1055" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1055_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1055_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1056" class="ax_default" data-left="16.999483828313878" data-top="126" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1057" class="ax_default" data-left="35" data-top="126" data-width="40" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1058" class="ax_default label transition notrs">
            <div id="u1058_div" class=""></div>
            <div id="u1058_text" class="text ">
              <p><span>订单号</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1059" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1059_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1059_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1060" class="ax_default" data-left="363" data-top="126" data-width="98" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1061" class="ax_default label transition notrs">
            <div id="u1061_div" class=""></div>
            <div id="u1061_text" class="text ">
              <p><span>FD13455555333</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1062" class="ax_default" data-left="16.999483828313867" data-top="168" data-width="453.00103234337223" data-height="29.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1063" class="ax_default" data-left="35" data-top="169" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1064" class="ax_default label transition notrs">
            <div id="u1064_div" class=""></div>
            <div id="u1064_text" class="text ">
              <p><span>发布时间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1065" class="ax_default" data-left="358" data-top="168" data-width="112" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1066" class="ax_default label transition notrs">
            <div id="u1066_div" class=""></div>
            <div id="u1066_text" class="text ">
              <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp;2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1067" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1067_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1067_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1068" class="ax_default" data-left="17.999483828313878" data-top="249" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1069" class="ax_default" data-left="36" data-top="251" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1070" class="ax_default label transition notrs">
            <div id="u1070_div" class=""></div>
            <div id="u1070_text" class="text ">
              <p><span>学习阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1071" class="ax_default" data-left="433" data-top="249" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1072" class="ax_default label transition notrs">
            <div id="u1072_div" class=""></div>
            <div id="u1072_text" class="text ">
              <p><span>本科</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1073" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1073_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1073_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1074" class="ax_default" data-left="17.99948382831388" data-top="284" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1075" class="ax_default" data-left="36" data-top="286" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1076" class="ax_default label transition notrs">
            <div id="u1076_div" class=""></div>
            <div id="u1076_text" class="text ">
              <p><span>学习位置</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1077" class="ax_default" data-left="433" data-top="284" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1078" class="ax_default label transition notrs">
            <div id="u1078_div" class=""></div>
            <div id="u1078_text" class="text ">
              <p><span>国内</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1079" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1079_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1079_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1080" class="ax_default" data-left="17.999483828313874" data-top="325" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1081" class="ax_default" data-left="36" data-top="327" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1082" class="ax_default label transition notrs">
            <div id="u1082_div" class=""></div>
            <div id="u1082_text" class="text ">
              <p><span>学习专业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1083" class="ax_default" data-left="433" data-top="325" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1084" class="ax_default label transition notrs">
            <div id="u1084_div" class=""></div>
            <div id="u1084_text" class="text ">
              <p><span>会计</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1085" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1085_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1085_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1086" class="ax_default" data-left="17.999483828313863" data-top="366" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1087" class="ax_default" data-left="36" data-top="368" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1088" class="ax_default label transition notrs">
            <div id="u1088_div" class=""></div>
            <div id="u1088_text" class="text ">
              <p><span>预算区间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1089" class="ax_default" data-left="380" data-top="366" data-width="80" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1090" class="ax_default label transition notrs">
            <div id="u1090_div" class=""></div>
            <div id="u1090_text" class="text ">
              <p><span>1000-2000元</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1091" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1091_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1091_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1092" class="ax_default" data-left="17.999483828313878" data-top="518" data-width="453.0010323433723" data-height="90" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1093" class="ax_default" data-left="36" data-top="518" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1094" class="ax_default label transition notrs">
            <div id="u1094_div" class=""></div>
            <div id="u1094_text" class="text ">
              <p><span>需求描述</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1095" class="ax_default label transition notrs">
          <div id="u1095_div" class=""></div>
          <div id="u1095_text" class="text ">
            <p><span>想要找一个辅导论文的老师，想要找一个辅导论文的老师想要找一个</span></p><p><span>辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想</span></p><p><span>要找一个辅导论文的老师</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1096" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1096_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1096_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1097" class="ax_default" data-left="36" data-top="400" data-width="53" data-height="18" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1098" class="ax_default label transition notrs">
          <div id="u1098_div" class=""></div>
          <div id="u1098_text" class="text ">
            <p><span>老师要求</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1099" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1100" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u316.svg" id="u1100_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
        </svg>
        <div id="u1100_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1101" class="ax_default label transition notrs">
        <div id="u1101_div" class=""></div>
        <div id="u1101_text" class="text ">
          <p><span>老师情况</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1102" class="ax_default shape transition notrs">
        <div id="u1102_div" class=""></div>
        <div id="u1102_text" class="text ">
          <p><span>博士</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1103" class="ax_default label transition notrs">
        <div id="u1103_div" class=""></div>
        <div id="u1103_text" class="text ">
          <p><span>是否留学</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1104" class="ax_default box_1 transition notrs">
        <div id="u1104_div" class=""></div>
        <div id="u1104_text" class="text ">
          <p><span>不要求</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1105" class="ax_default label transition notrs">
        <div id="u1105_div" class=""></div>
        <div id="u1105_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1106" class="ax_default label transition notrs">
        <div id="u1106_div" class=""></div>
        <div id="u1106_text" class="text ">
          <p><span>交付信息</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1107" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u1107_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u1107_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1108" class="ax_default" data-left="17.999483828313867" data-top="627" data-width="453.0010323433722" data-height="90" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1109" class="ax_default label transition notrs">
          <div id="u1109_div" class=""></div>
          <div id="u1109_text" class="text ">
            <p><span>希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，</span></p><p><span>有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热</span></p><p><span>情专业，有耐心。</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1110" class="ax_default" data-left="30" data-top="627" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1111" class="ax_default label transition notrs">
            <div id="u1111_div" class=""></div>
            <div id="u1111_text" class="text ">
              <p><span>其他说明</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1112" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1112_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1112_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1113" class="ax_default label transition notrs">
        <div id="u1113_div" class=""></div>
        <div id="u1113_text" class="text ">
          <p><span>评价信息</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
