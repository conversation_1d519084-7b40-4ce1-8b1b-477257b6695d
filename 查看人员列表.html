﻿<!DOCTYPE html>
<html>
  <head>
    <title>查看人员列表</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/查看人员列表/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/查看人员列表/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u383" class="ax_default box_1 transition notrs">
        <div id="u383_div" class=""></div>
        <div id="u383_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u384" class="ax_default label transition notrs">
        <div id="u384_div" class=""></div>
        <div id="u384_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u385" class="ax_default label transition notrs">
        <div id="u385_div" class=""></div>
        <div id="u385_text" class="text ">
          <p><span>报名人列表</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u386" class="ax_default _图片 transition notrs">
        <img id="u386_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u386_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u387" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u387_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u387_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u388" class="ax_default box_1 transition notrs">
              <div id="u388_div" class=""></div>
              <div id="u388_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u389" class="ax_default label transition notrs">
              <div id="u389_div" class=""></div>
              <div id="u389_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u390" class="ax_default label transition notrs">
              <div id="u390_div" class=""></div>
              <div id="u390_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u391" class="ax_default button transition notrs">
              <div id="u391_div" class=""></div>
              <div id="u391_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u392" class="ax_default primary_button transition notrs">
              <div id="u392_div" class=""></div>
              <div id="u392_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u387_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u387_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u393" class="ax_default box_1 transition notrs">
              <div id="u393_div" class=""></div>
              <div id="u393_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u394" class="ax_default label transition notrs">
              <div id="u394_div" class=""></div>
              <div id="u394_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u395" class="ax_default label transition notrs">
              <div id="u395_div" class=""></div>
              <div id="u395_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u396" class="ax_default button transition notrs">
              <div id="u396_div" class=""></div>
              <div id="u396_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u397" class="ax_default primary_button transition notrs">
              <div id="u397_div" class=""></div>
              <div id="u397_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u398" class="ax_default label transition notrs">
              <div id="u398_div" class=""></div>
              <div id="u398_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u399" class="ax_default text_field transition notrs">
              <div id="u399_div" class=""></div>
              <input id="u399_input" type="text" value="" class="u399_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u400" class="ax_default label transition notrs">
              <div id="u400_div" class=""></div>
              <div id="u400_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u401" class="ax_default" data-left="34.99941039141416" data-top="132" data-width="437.00058960858587" data-height="209" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u402" class="ax_default" data-left="321" data-top="312" data-width="151" data-height="29" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u403" class="ax_default primary_button transition notrs">
            <div id="u403_div" class=""></div>
            <div id="u403_text" class="text ">
              <p><span>复制微信</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u404" class="ax_default primary_button transition notrs">
            <div id="u404_div" class=""></div>
            <div id="u404_text" class="text ">
              <p><span>确认老师</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u405" class="ax_default" data-left="34.99941039141416" data-top="132" data-width="424.00117921717174" data-height="170.50236615205284" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u406" class="ax_default _图片 transition notrs">
            <img id="u406_img" class="img " src="images/首页（学生端）/u21.png"/>
            <div id="u406_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u407" class="ax_default label transition notrs">
            <div id="u407_div" class=""></div>
            <div id="u407_text" class="text ">
              <p><span>张伟 | 博士</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u408" class="ax_default label transition notrs">
            <div id="u408_div" class=""></div>
            <div id="u408_text" class="text ">
              <p><span>已实名认证</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u409" class="ax_default label transition notrs">
            <div id="u409_div" class=""></div>
            <div id="u409_text" class="text ">
              <p><span>清华大学 | 博士 | 环境会计</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u410" class="ax_default label transition notrs">
            <div id="u410_div" class=""></div>
            <div id="u410_text" class="text ">
              <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">已辅导：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">12</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u411" class="ax_default label transition notrs">
            <div id="u411_div" class=""></div>
            <div id="u411_text" class="text ">
              <p><span>我是来自清华大学的张飞，在读博士，已发表2篇SCI。已发表2篇SCI</span></p><p><span>已发表2篇SCI已发表2篇SCI已发表2篇SCI已发表2篇SCI已发表2……</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u412" class="ax_default label transition notrs">
            <div id="u412_div" class=""></div>
            <div id="u412_text" class="text ">
              <p><span>论文辅导</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u413" class="ax_default label transition notrs">
            <div id="u413_div" class=""></div>
            <div id="u413_text" class="text ">
              <p><span>专业课辅导</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u414" class="ax_default label transition notrs">
            <div id="u414_div" class=""></div>
            <div id="u414_text" class="text ">
              <p><span>保研辅导</span></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u415" class="ax_default line transition notrs">
            <svg data="images/首页（学生端）/u30.svg" id="u415_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -24 -454 )">
    <path d="M 0 0.5  L 424 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(215, 215, 215, 1)" fill="none" transform="matrix(1 0 0 1 24 454 )" class="stroke" />
  </g>
            </svg>
            <div id="u415_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u416" class="ax_default" data-left="34.99941039141415" data-top="357" data-width="437.00058960858587" data-height="209" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u417" class="ax_default" data-left="322" data-top="537" data-width="150" data-height="29" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u418" class="ax_default primary_button transition notrs">
            <div id="u418_div" class=""></div>
            <div id="u418_text" class="text ">
              <p><span>复制微信</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u419" class="ax_default primary_button transition notrs">
            <div id="u419_div" class=""></div>
            <div id="u419_text" class="text ">
              <p><span>确认老师</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u420" class="ax_default" data-left="34.99941039141415" data-top="357" data-width="424.0011792171717" data-height="170.5023661520529" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u421" class="ax_default _图片 transition notrs">
            <img id="u421_img" class="img " src="images/首页（学生端）/u21.png"/>
            <div id="u421_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u422" class="ax_default label transition notrs">
            <div id="u422_div" class=""></div>
            <div id="u422_text" class="text ">
              <p><span>张伟 | 博士</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u423" class="ax_default label transition notrs">
            <div id="u423_div" class=""></div>
            <div id="u423_text" class="text ">
              <p><span>已实名认证</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u424" class="ax_default label transition notrs">
            <div id="u424_div" class=""></div>
            <div id="u424_text" class="text ">
              <p><span>清华大学 | 博士 | 环境会计</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u425" class="ax_default label transition notrs">
            <div id="u425_div" class=""></div>
            <div id="u425_text" class="text ">
              <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">已辅导：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">12</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u426" class="ax_default label transition notrs">
            <div id="u426_div" class=""></div>
            <div id="u426_text" class="text ">
              <p><span>我是来自清华大学的张飞，在读博士，已发表2篇SCI。已发表2篇SCI</span></p><p><span>已发表2篇SCI已发表2篇SCI已发表2篇SCI已发表2篇SCI已发表2……</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u427" class="ax_default label transition notrs">
            <div id="u427_div" class=""></div>
            <div id="u427_text" class="text ">
              <p><span>论文辅导</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u428" class="ax_default label transition notrs">
            <div id="u428_div" class=""></div>
            <div id="u428_text" class="text ">
              <p><span>专业课辅导</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u429" class="ax_default label transition notrs">
            <div id="u429_div" class=""></div>
            <div id="u429_text" class="text ">
              <p><span>保研辅导</span></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u430" class="ax_default line transition notrs">
            <svg data="images/首页（学生端）/u30.svg" id="u430_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -24 -454 )">
    <path d="M 0 0.5  L 424 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(215, 215, 215, 1)" fill="none" transform="matrix(1 0 0 1 24 454 )" class="stroke" />
  </g>
            </svg>
            <div id="u430_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u431" class="ax_default label transition notrs">
        <div id="u431_div" class=""></div>
        <div id="u431_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u432" class="ax_default label transition notrs">
        <div id="u432_div" class=""></div>
        <div id="u432_text" class="text ">
          <p><span>投递人员（30）</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u433" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u433_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u433_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (热区) -->
      <div id="u434" class="ax_default">
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u435" class="ax_default sticky_1 transition notrs">
        <div id="u435_div" class=""></div>
        <div id="u435_text" class="text ">
          <p><span>复制微信后需要toast提示：已复制成功，可以添加微信。</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
