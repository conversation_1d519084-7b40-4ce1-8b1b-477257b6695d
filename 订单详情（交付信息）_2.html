﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情（交付信息）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情（交付信息）_2/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情（交付信息）_2/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1114" class="ax_default box_1 transition notrs">
        <div id="u1114_div" class=""></div>
        <div id="u1114_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1115" class="ax_default label transition notrs">
        <div id="u1115_div" class=""></div>
        <div id="u1115_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1116" class="ax_default label transition notrs">
        <div id="u1116_div" class=""></div>
        <div id="u1116_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1117" class="ax_default _图片 transition notrs">
        <img id="u1117_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1117_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1118" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1118_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1118_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1119" class="ax_default box_1 transition notrs">
              <div id="u1119_div" class=""></div>
              <div id="u1119_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1120" class="ax_default label transition notrs">
              <div id="u1120_div" class=""></div>
              <div id="u1120_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1121" class="ax_default label transition notrs">
              <div id="u1121_div" class=""></div>
              <div id="u1121_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1122" class="ax_default button transition notrs">
              <div id="u1122_div" class=""></div>
              <div id="u1122_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1123" class="ax_default primary_button transition notrs">
              <div id="u1123_div" class=""></div>
              <div id="u1123_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1118_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1118_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1124" class="ax_default box_1 transition notrs">
              <div id="u1124_div" class=""></div>
              <div id="u1124_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1125" class="ax_default label transition notrs">
              <div id="u1125_div" class=""></div>
              <div id="u1125_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1126" class="ax_default label transition notrs">
              <div id="u1126_div" class=""></div>
              <div id="u1126_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1127" class="ax_default button transition notrs">
              <div id="u1127_div" class=""></div>
              <div id="u1127_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1128" class="ax_default primary_button transition notrs">
              <div id="u1128_div" class=""></div>
              <div id="u1128_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1129" class="ax_default label transition notrs">
              <div id="u1129_div" class=""></div>
              <div id="u1129_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1130" class="ax_default text_field transition notrs">
              <div id="u1130_div" class=""></div>
              <input id="u1130_input" type="text" value="" class="u1130_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1131" class="ax_default label transition notrs">
              <div id="u1131_div" class=""></div>
              <div id="u1131_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1132" class="ax_default label transition notrs">
        <div id="u1132_div" class=""></div>
        <div id="u1132_text" class="text ">
          <p><span>第1阶段</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1133" class="ax_default label transition notrs">
        <div id="u1133_div" class=""></div>
        <div id="u1133_text" class="text ">
          <p><span>1.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1134" class="ax_default label transition notrs">
        <div id="u1134_div" class=""></div>
        <div id="u1134_text" class="text ">
          <p><span>交付阶段</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1135" class="ax_default" data-left="128" data-top="144" data-width="62" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1136" class="ax_default label transition notrs">
          <div id="u1136_div" class=""></div>
          <div id="u1136_text" class="text ">
            <p><span>阶段费用</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1137" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u316.svg" id="u1137_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
        </svg>
        <div id="u1137_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1138" class="ax_default label transition notrs">
        <div id="u1138_div" class=""></div>
        <div id="u1138_text" class="text ">
          <p><span>第2阶段</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1139" class="ax_default label transition notrs">
        <div id="u1139_div" class=""></div>
        <div id="u1139_text" class="text ">
          <p><span>已验收</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1140" class="ax_default" data-left="18.99948382831387" data-top="101" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1141" class="ax_default" data-left="37" data-top="103" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1142" class="ax_default label transition notrs">
            <div id="u1142_div" class=""></div>
            <div id="u1142_text" class="text ">
              <p><span>辅导老师</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1143" class="ax_default" data-left="421" data-top="101" data-width="35" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1144" class="ax_default label transition notrs">
            <div id="u1144_div" class=""></div>
            <div id="u1144_text" class="text ">
              <p><span>张伟&gt;</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1145" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1145_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1145_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1146" class="ax_default" data-left="37" data-top="258" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1147" class="ax_default" data-left="37" data-top="258" data-width="48" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1148" class="ax_default label transition notrs">
            <div id="u1148_div" class=""></div>
            <div id="u1148_text" class="text ">
              <p><span>第3阶段</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1149" class="ax_default" data-left="37" data-top="292" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1150" class="ax_default label transition notrs">
          <div id="u1150_div" class=""></div>
          <div id="u1150_text" class="text ">
            <p><span>第4阶段</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1151" class="ax_default" data-left="39" data-top="323" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1152" class="ax_default label transition notrs">
          <div id="u1152_div" class=""></div>
          <div id="u1152_text" class="text ">
            <p><span>第5阶段</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1153" class="ax_default label transition notrs">
        <div id="u1153_div" class=""></div>
        <div id="u1153_text" class="text ">
          <p><span>注释：</span></p><p><span>1、每个阶段需要先支付，老师收到支付信息后才会开始作业。</span></p><p><span>2、老师提交阶段交付时，请先确认是否达到约定的标准，达标后再点击确认验收。</span></p><p><span>3、所有阶段都是已验收后辅导老师才能提现。</span></p><p><span>4、如果和辅导老师之前差生分歧可以找客服进行协商。</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1154" class="ax_default" data-left="225" data-top="144" data-width="57" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1155" class="ax_default label transition notrs">
          <div id="u1155_div" class=""></div>
          <div id="u1155_text" class="text ">
            <p><span>阶段状态</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1156" class="ax_default" data-left="386" data-top="144" data-width="53" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1157" class="ax_default label transition notrs">
          <div id="u1157_div" class=""></div>
          <div id="u1157_text" class="text ">
            <p><span>操作</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1158" class="ax_default label transition notrs">
        <div id="u1158_div" class=""></div>
        <div id="u1158_text" class="text ">
          <p><span>已验收</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1159" class="ax_default label transition notrs">
        <div id="u1159_div" class=""></div>
        <div id="u1159_text" class="text ">
          <p><span>2.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1160" class="ax_default label transition notrs">
        <div id="u1160_div" class=""></div>
        <div id="u1160_text" class="text ">
          <p><span>3.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1161" class="ax_default label transition notrs">
        <div id="u1161_div" class=""></div>
        <div id="u1161_text" class="text ">
          <p><span>4.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1162" class="ax_default label transition notrs">
        <div id="u1162_div" class=""></div>
        <div id="u1162_text" class="text ">
          <p><span>3.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1163" class="ax_default label transition notrs">
        <div id="u1163_div" class=""></div>
        <div id="u1163_text" class="text ">
          <p><span>已验收</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1164" class="ax_default label transition notrs">
        <div id="u1164_div" class=""></div>
        <div id="u1164_text" class="text ">
          <p><span>已验收</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1165" class="ax_default label transition notrs">
        <div id="u1165_div" class=""></div>
        <div id="u1165_text" class="text ">
          <p><span>已验收</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1166" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u316.svg" id="u1166_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
        </svg>
        <div id="u1166_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1167" class="ax_default" data-left="39" data-top="364" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1168" class="ax_default label transition notrs">
          <div id="u1168_div" class=""></div>
          <div id="u1168_text" class="text ">
            <p><span>共5阶段</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1169" class="ax_default" data-left="119" data-top="364" data-width="40" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1170" class="ax_default label transition notrs">
          <div id="u1170_div" class=""></div>
          <div id="u1170_text" class="text ">
            <p><span>13.000</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1171" class="ax_default" data-left="228" data-top="364" data-width="40" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1172" class="ax_default label transition notrs">
          <div id="u1172_div" class=""></div>
          <div id="u1172_text" class="text ">
            <p><span>已验收</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1173" class="ax_default label transition notrs">
        <div id="u1173_div" class=""></div>
        <div id="u1173_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1174" class="ax_default label transition notrs">
        <div id="u1174_div" class=""></div>
        <div id="u1174_text" class="text ">
          <p><span>交付信息</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1175" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u1175_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u1175_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1176" class="ax_default label transition notrs">
        <div id="u1176_div" class=""></div>
        <div id="u1176_text" class="text ">
          <p><span>评价信息</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
