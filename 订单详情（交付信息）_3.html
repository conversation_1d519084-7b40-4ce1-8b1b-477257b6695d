﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情（交付信息）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情（交付信息）_3/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情（交付信息）_3/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1691" class="ax_default box_1 transition notrs">
        <div id="u1691_div" class=""></div>
        <div id="u1691_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1692" class="ax_default label transition notrs">
        <div id="u1692_div" class=""></div>
        <div id="u1692_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1693" class="ax_default label transition notrs">
        <div id="u1693_div" class=""></div>
        <div id="u1693_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1694" class="ax_default _图片 transition notrs">
        <img id="u1694_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1694_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1695" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1695_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1695_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1696" class="ax_default box_1 transition notrs">
              <div id="u1696_div" class=""></div>
              <div id="u1696_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1697" class="ax_default label transition notrs">
              <div id="u1697_div" class=""></div>
              <div id="u1697_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1698" class="ax_default label transition notrs">
              <div id="u1698_div" class=""></div>
              <div id="u1698_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1699" class="ax_default button transition notrs">
              <div id="u1699_div" class=""></div>
              <div id="u1699_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1700" class="ax_default primary_button transition notrs">
              <div id="u1700_div" class=""></div>
              <div id="u1700_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1695_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1695_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1701" class="ax_default box_1 transition notrs">
              <div id="u1701_div" class=""></div>
              <div id="u1701_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1702" class="ax_default label transition notrs">
              <div id="u1702_div" class=""></div>
              <div id="u1702_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1703" class="ax_default label transition notrs">
              <div id="u1703_div" class=""></div>
              <div id="u1703_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1704" class="ax_default button transition notrs">
              <div id="u1704_div" class=""></div>
              <div id="u1704_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1705" class="ax_default primary_button transition notrs">
              <div id="u1705_div" class=""></div>
              <div id="u1705_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1706" class="ax_default label transition notrs">
              <div id="u1706_div" class=""></div>
              <div id="u1706_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1707" class="ax_default text_field transition notrs">
              <div id="u1707_div" class=""></div>
              <input id="u1707_input" type="text" value="" class="u1707_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1708" class="ax_default label transition notrs">
              <div id="u1708_div" class=""></div>
              <div id="u1708_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1709" class="ax_default" data-left="37" data-top="174" data-width="410" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1710" class="ax_default label transition notrs">
          <div id="u1710_div" class=""></div>
          <div id="u1710_text" class="text ">
            <p><span>第1阶段</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1711" class="ax_default label transition notrs">
          <div id="u1711_div" class=""></div>
          <div id="u1711_text" class="text ">
            <p><span>1.000</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1712" class="ax_default label transition notrs">
          <div id="u1712_div" class=""></div>
          <div id="u1712_text" class="text ">
            <p><span>已验收</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1713" class="ax_default" data-left="16.999483828313878" data-top="133" data-width="453.00103234337223" data-height="24.62717305005009" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1714" class="ax_default label transition notrs">
          <div id="u1714_div" class=""></div>
          <div id="u1714_text" class="text ">
            <p><span>交付阶段</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1715" class="ax_default" data-left="203" data-top="133" data-width="61" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1716" class="ax_default label transition notrs">
            <div id="u1716_div" class=""></div>
            <div id="u1716_text" class="text ">
              <p><span>阶段金额</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1717" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1717_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1717_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1718" class="ax_default" data-left="370" data-top="133" data-width="61" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1719" class="ax_default label transition notrs">
            <div id="u1719_div" class=""></div>
            <div id="u1719_text" class="text ">
              <p><span>阶段状态</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1720" class="ax_default" data-left="37" data-top="211" data-width="410" data-height="23" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1721" class="ax_default label transition notrs">
          <div id="u1721_div" class=""></div>
          <div id="u1721_text" class="text ">
            <p><span>第2阶段</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1722" class="ax_default label transition notrs">
          <div id="u1722_div" class=""></div>
          <div id="u1722_text" class="text ">
            <p><span>已验收</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1723" class="ax_default label transition notrs">
          <div id="u1723_div" class=""></div>
          <div id="u1723_text" class="text ">
            <p><span>2.000</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1724" class="ax_default" data-left="37" data-top="247" data-width="404" data-height="20" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1725" class="ax_default" data-left="37" data-top="247" data-width="85" data-height="20" layer-opacity="1">

          <!-- Unnamed (组合) -->
          <div id="u1726" class="ax_default" data-left="37" data-top="247" data-width="85" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1727" class="ax_default label transition notrs">
              <div id="u1727_div" class=""></div>
              <div id="u1727_text" class="text ">
                <p><span>第3阶段</span></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1728" class="ax_default label transition notrs">
          <div id="u1728_div" class=""></div>
          <div id="u1728_text" class="text ">
            <p><span>3.000</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1729" class="ax_default label transition notrs">
          <div id="u1729_div" class=""></div>
          <div id="u1729_text" class="text ">
            <p><span>待交付</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1730" class="ax_default" data-left="37" data-top="284" data-width="419" data-height="20" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1731" class="ax_default" data-left="37" data-top="284" data-width="83" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1732" class="ax_default label transition notrs">
            <div id="u1732_div" class=""></div>
            <div id="u1732_text" class="text ">
              <p><span>第4阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1733" class="ax_default label transition notrs">
          <div id="u1733_div" class=""></div>
          <div id="u1733_text" class="text ">
            <p><span>4.000</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1734" class="ax_default label transition notrs">
          <div id="u1734_div" class=""></div>
          <div id="u1734_text" class="text ">
            <p><span>已交付待验收</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1735" class="ax_default" data-left="37" data-top="321" data-width="404" data-height="22" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1736" class="ax_default" data-left="37" data-top="323" data-width="86" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1737" class="ax_default label transition notrs">
            <div id="u1737_div" class=""></div>
            <div id="u1737_text" class="text ">
              <p><span>第5阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1738" class="ax_default label transition notrs">
          <div id="u1738_div" class=""></div>
          <div id="u1738_text" class="text ">
            <p><span>3.000</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1739" class="ax_default label transition notrs">
          <div id="u1739_div" class=""></div>
          <div id="u1739_text" class="text ">
            <p><span>待支付</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1740" class="ax_default label transition notrs">
        <div id="u1740_div" class=""></div>
        <div id="u1740_text" class="text ">
          <p><span>注释：</span></p><p><span>1、分阶段支付：学生需预先支付当前阶段费用，状态变为待交付后方可开始作业，防止作业后不付款。</span></p><p><span>2、验收机制：</span></p><p><span>老师完成阶段任务后，需与学生线下确认成果；</span></p><p><span>学生须在平台进行线上验收，状态变更为【已验收】后方可进入下一阶段。</span></p><p><span>3、提现条件：所有阶段均达【已验收】状态，且学生14天无异议期结束后，方可提现。</span></p><p><span>4、订单终止：需师生协商一致，由学生在平台主动终止订单。</span></p><p><span>5、争议处理：若产生分歧，双方可申请平台客服介入协商。</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1741" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u316.svg" id="u1741_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
        </svg>
        <div id="u1741_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1742" class="ax_default" data-left="37" data-top="372" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1743" class="ax_default label transition notrs">
          <div id="u1743_div" class=""></div>
          <div id="u1743_text" class="text ">
            <p><span>共5阶段</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1744" class="ax_default" data-left="198" data-top="372" data-width="40" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1745" class="ax_default label transition notrs">
          <div id="u1745_div" class=""></div>
          <div id="u1745_text" class="text ">
            <p><span>13.000</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1746" class="ax_default" data-left="375" data-top="372" data-width="40" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1747" class="ax_default label transition notrs">
          <div id="u1747_div" class=""></div>
          <div id="u1747_text" class="text ">
            <p><span>进行中</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1748" class="ax_default primary_button transition notrs">
        <div id="u1748_div" class=""></div>
        <div id="u1748_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">注意</span><span style="font-family:&quot;AppleColorEmoji&quot;, &quot;Apple Color Emoji&quot;, sans-serif;">📢</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">：线下交易有风险，出现问题无人管。</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">一旦发现会封号，也不利于攒人气。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1749" class="ax_default label transition notrs">
        <div id="u1749_div" class=""></div>
        <div id="u1749_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1750" class="ax_default label transition notrs">
        <div id="u1750_div" class=""></div>
        <div id="u1750_text" class="text ">
          <p><span>交付信息</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1751" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u1751_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u1751_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
