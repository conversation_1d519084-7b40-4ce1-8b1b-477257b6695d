﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情 (投递中、已拒绝）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情__投递中、已拒绝）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情__投递中、已拒绝）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1485" class="ax_default box_1 transition notrs">
        <div id="u1485_div" class=""></div>
        <div id="u1485_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1486" class="ax_default label transition notrs">
        <div id="u1486_div" class=""></div>
        <div id="u1486_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1487" class="ax_default label transition notrs">
        <div id="u1487_div" class=""></div>
        <div id="u1487_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1488" class="ax_default _图片 transition notrs">
        <img id="u1488_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1488_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1489" class="ax_default sticky_1 transition notrs">
        <div id="u1489_div" class=""></div>
        <div id="u1489_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">阶段状态：</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">待付款</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">—&gt;</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">已付款待交付</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">—&gt;</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">已交付待验收</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">—&gt;</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">已验收</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">当前一个阶段为已验收时，下个阶段自动变化待付款</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">如果当前阶段为最后一个阶段，已验收后整个订单状态为待评价</span></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1490" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1490_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1490_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1491" class="ax_default box_1 transition notrs">
              <div id="u1491_div" class=""></div>
              <div id="u1491_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1492" class="ax_default label transition notrs">
              <div id="u1492_div" class=""></div>
              <div id="u1492_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1493" class="ax_default label transition notrs">
              <div id="u1493_div" class=""></div>
              <div id="u1493_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1494" class="ax_default button transition notrs">
              <div id="u1494_div" class=""></div>
              <div id="u1494_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1495" class="ax_default primary_button transition notrs">
              <div id="u1495_div" class=""></div>
              <div id="u1495_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1490_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1490_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1496" class="ax_default box_1 transition notrs">
              <div id="u1496_div" class=""></div>
              <div id="u1496_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1497" class="ax_default label transition notrs">
              <div id="u1497_div" class=""></div>
              <div id="u1497_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1498" class="ax_default label transition notrs">
              <div id="u1498_div" class=""></div>
              <div id="u1498_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1499" class="ax_default button transition notrs">
              <div id="u1499_div" class=""></div>
              <div id="u1499_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1500" class="ax_default primary_button transition notrs">
              <div id="u1500_div" class=""></div>
              <div id="u1500_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1501" class="ax_default label transition notrs">
              <div id="u1501_div" class=""></div>
              <div id="u1501_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1502" class="ax_default text_field transition notrs">
              <div id="u1502_div" class=""></div>
              <input id="u1502_input" type="text" value="" class="u1502_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1503" class="ax_default label transition notrs">
              <div id="u1503_div" class=""></div>
              <div id="u1503_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1504" class="ax_default" data-left="17.999483828313885" data-top="207" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1505" class="ax_default" data-left="36" data-top="209" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1506" class="ax_default label transition notrs">
            <div id="u1506_div" class=""></div>
            <div id="u1506_text" class="text ">
              <p><span>辅导项目</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1507" class="ax_default" data-left="433" data-top="207" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1508" class="ax_default label transition notrs">
            <div id="u1508_div" class=""></div>
            <div id="u1508_text" class="text ">
              <p><span>作业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1509" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1509_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1509_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1510" class="ax_default" data-left="16.999483828313878" data-top="126" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1511" class="ax_default" data-left="35" data-top="126" data-width="40" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1512" class="ax_default label transition notrs">
            <div id="u1512_div" class=""></div>
            <div id="u1512_text" class="text ">
              <p><span>订单号</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1513" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1513_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1513_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1514" class="ax_default" data-left="363" data-top="126" data-width="98" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1515" class="ax_default label transition notrs">
            <div id="u1515_div" class=""></div>
            <div id="u1515_text" class="text ">
              <p><span>FD13455555333</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1516" class="ax_default" data-left="16.999483828313867" data-top="168" data-width="453.00103234337223" data-height="29.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1517" class="ax_default" data-left="35" data-top="169" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1518" class="ax_default label transition notrs">
            <div id="u1518_div" class=""></div>
            <div id="u1518_text" class="text ">
              <p><span>发布时间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1519" class="ax_default" data-left="358" data-top="168" data-width="112" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1520" class="ax_default label transition notrs">
            <div id="u1520_div" class=""></div>
            <div id="u1520_text" class="text ">
              <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">&nbsp;2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1521" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1521_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1521_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1522" class="ax_default" data-left="17.999483828313878" data-top="249" data-width="453.0010323433723" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1523" class="ax_default" data-left="36" data-top="251" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1524" class="ax_default label transition notrs">
            <div id="u1524_div" class=""></div>
            <div id="u1524_text" class="text ">
              <p><span>学习阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1525" class="ax_default" data-left="433" data-top="249" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1526" class="ax_default label transition notrs">
            <div id="u1526_div" class=""></div>
            <div id="u1526_text" class="text ">
              <p><span>本科</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1527" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1527_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1527_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1528" class="ax_default" data-left="17.99948382831388" data-top="284" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1529" class="ax_default" data-left="36" data-top="286" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1530" class="ax_default label transition notrs">
            <div id="u1530_div" class=""></div>
            <div id="u1530_text" class="text ">
              <p><span>学习位置</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1531" class="ax_default" data-left="433" data-top="284" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1532" class="ax_default label transition notrs">
            <div id="u1532_div" class=""></div>
            <div id="u1532_text" class="text ">
              <p><span>国内</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1533" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1533_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1533_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1534" class="ax_default" data-left="17.999483828313874" data-top="325" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1535" class="ax_default" data-left="36" data-top="327" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1536" class="ax_default label transition notrs">
            <div id="u1536_div" class=""></div>
            <div id="u1536_text" class="text ">
              <p><span>学习专业</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1537" class="ax_default" data-left="433" data-top="325" data-width="27" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1538" class="ax_default label transition notrs">
            <div id="u1538_div" class=""></div>
            <div id="u1538_text" class="text ">
              <p><span>会计</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1539" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1539_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1539_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1540" class="ax_default" data-left="17.999483828313863" data-top="366" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1541" class="ax_default" data-left="36" data-top="368" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1542" class="ax_default label transition notrs">
            <div id="u1542_div" class=""></div>
            <div id="u1542_text" class="text ">
              <p><span>预算区间</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1543" class="ax_default" data-left="380" data-top="366" data-width="80" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1544" class="ax_default label transition notrs">
            <div id="u1544_div" class=""></div>
            <div id="u1544_text" class="text ">
              <p><span>1000-2000元</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1545" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1545_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1545_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1546" class="ax_default" data-left="17.99948382831388" data-top="519" data-width="453.0010323433723" data-height="90" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1547" class="ax_default" data-left="36" data-top="519" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1548" class="ax_default label transition notrs">
            <div id="u1548_div" class=""></div>
            <div id="u1548_text" class="text ">
              <p><span>需求描述</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1549" class="ax_default label transition notrs">
          <div id="u1549_div" class=""></div>
          <div id="u1549_text" class="text ">
            <p><span>想要找一个辅导论文的老师，想要找一个辅导论文的老师想要找一个</span></p><p><span>辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想</span></p><p><span>要找一个辅导论文的老师</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1550" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1550_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1550_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1551" class="ax_default label transition notrs">
        <div id="u1551_div" class=""></div>
        <div id="u1551_text" class="text ">
          <p><span>希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，</span></p><p><span>有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热</span></p><p><span>情专业，有耐心。</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1552" class="ax_default" data-left="17.999483828313885" data-top="409" data-width="453.0010323433723" data-height="28.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1553" class="ax_default" data-left="36" data-top="409" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1554" class="ax_default label transition notrs">
            <div id="u1554_div" class=""></div>
            <div id="u1554_text" class="text ">
              <p><span>老师要求</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1555" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1556" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1556_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1556_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1557" class="ax_default" data-left="36" data-top="451" data-width="419" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1558" class="ax_default label transition notrs">
          <div id="u1558_div" class=""></div>
          <div id="u1558_text" class="text ">
            <p><span>老师情况</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1559" class="ax_default shape transition notrs">
          <div id="u1559_div" class=""></div>
          <div id="u1559_text" class="text ">
            <p><span>博士</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1560" class="ax_default" data-left="36" data-top="485" data-width="424" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1561" class="ax_default label transition notrs">
          <div id="u1561_div" class=""></div>
          <div id="u1561_text" class="text ">
            <p><span>是否留学</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1562" class="ax_default box_1 transition notrs">
          <div id="u1562_div" class=""></div>
          <div id="u1562_text" class="text ">
            <p><span>不要求</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1563" class="ax_default" data-left="18.999483828313863" data-top="623" data-width="453.0010323433722" data-height="28.627173050050146" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1564" class="ax_default" data-left="37" data-top="623" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1565" class="ax_default label transition notrs">
            <div id="u1565_div" class=""></div>
            <div id="u1565_text" class="text ">
              <p><span>其他说明</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1566" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1567" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1567_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1567_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
