﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情（评价信息）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情（评价信息）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情（评价信息）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1177" class="ax_default box_1 transition notrs">
        <div id="u1177_div" class=""></div>
        <div id="u1177_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1178" class="ax_default label transition notrs">
        <div id="u1178_div" class=""></div>
        <div id="u1178_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1179" class="ax_default label transition notrs">
        <div id="u1179_div" class=""></div>
        <div id="u1179_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1180" class="ax_default _图片 transition notrs">
        <img id="u1180_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1180_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1181" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1181_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1181_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1182" class="ax_default box_1 transition notrs">
              <div id="u1182_div" class=""></div>
              <div id="u1182_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1183" class="ax_default label transition notrs">
              <div id="u1183_div" class=""></div>
              <div id="u1183_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1184" class="ax_default label transition notrs">
              <div id="u1184_div" class=""></div>
              <div id="u1184_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1185" class="ax_default button transition notrs">
              <div id="u1185_div" class=""></div>
              <div id="u1185_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1186" class="ax_default primary_button transition notrs">
              <div id="u1186_div" class=""></div>
              <div id="u1186_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1181_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1181_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1187" class="ax_default box_1 transition notrs">
              <div id="u1187_div" class=""></div>
              <div id="u1187_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1188" class="ax_default label transition notrs">
              <div id="u1188_div" class=""></div>
              <div id="u1188_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1189" class="ax_default label transition notrs">
              <div id="u1189_div" class=""></div>
              <div id="u1189_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1190" class="ax_default button transition notrs">
              <div id="u1190_div" class=""></div>
              <div id="u1190_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1191" class="ax_default primary_button transition notrs">
              <div id="u1191_div" class=""></div>
              <div id="u1191_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1192" class="ax_default label transition notrs">
              <div id="u1192_div" class=""></div>
              <div id="u1192_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1193" class="ax_default text_field transition notrs">
              <div id="u1193_div" class=""></div>
              <input id="u1193_input" type="text" value="" class="u1193_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1194" class="ax_default label transition notrs">
              <div id="u1194_div" class=""></div>
              <div id="u1194_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1195" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1196" class="ax_default label transition notrs">
        <div id="u1196_div" class=""></div>
        <div id="u1196_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1197" class="ax_default label transition notrs">
        <div id="u1197_div" class=""></div>
        <div id="u1197_text" class="text ">
          <p><span>交付信息</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1198" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u1198_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u1198_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1199" class="ax_default label transition notrs">
        <div id="u1199_div" class=""></div>
        <div id="u1199_text" class="text ">
          <p><span>评价信息</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1200" class="ax_default" data-left="56" data-top="133" data-width="400" data-height="381" layer-opacity="1">

        <!-- Unnamed (图片) -->
        <div id="u1201" class="ax_default _图片 transition notrs">
          <img id="u1201_img" class="img " src="images/交付中/u566.png"/>
          <div id="u1201_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1202" class="ax_default label transition notrs">
          <div id="u1202_div" class=""></div>
          <div id="u1202_text" class="text ">
            <p><span>专业水平</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1203" class="ax_default label transition notrs">
          <div id="u1203_div" class=""></div>
          <div id="u1203_text" class="text ">
            <p><span>沟通能力</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1204" class="ax_default label transition notrs">
          <div id="u1204_div" class=""></div>
          <div id="u1204_text" class="text ">
            <p><span>响应速度</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1205" class="ax_default" data-left="288" data-top="267" data-width="168" data-height="26" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u1206" class="ax_default _图片 transition notrs">
            <img id="u1206_img" class="img " src="images/交付中/u566.png"/>
            <div id="u1206_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1207" class="ax_default label transition notrs">
            <div id="u1207_div" class=""></div>
            <div id="u1207_text" class="text ">
              <p><span>4.5分</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1208" class="ax_default" data-left="288" data-top="299" data-width="168" data-height="26" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u1209" class="ax_default _图片 transition notrs">
            <img id="u1209_img" class="img " src="images/交付中/u566.png"/>
            <div id="u1209_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1210" class="ax_default label transition notrs">
            <div id="u1210_div" class=""></div>
            <div id="u1210_text" class="text ">
              <p><span>4.5分</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1211" class="ax_default label transition notrs">
          <div id="u1211_div" class=""></div>
          <div id="u1211_text" class="text ">
            <p><span>感谢您完成本次辅导，请您为老师打分！</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1212" class="ax_default label transition notrs">
          <div id="u1212_div" class=""></div>
          <div id="u1212_text" class="text ">
            <p><span>服务态度</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1213" class="ax_default label transition notrs">
          <div id="u1213_div" class=""></div>
          <div id="u1213_text" class="text ">
            <p><span>结果质量</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1214" class="ax_default" data-left="288" data-top="332" data-width="168" data-height="26" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u1215" class="ax_default _图片 transition notrs">
            <img id="u1215_img" class="img " src="images/交付中/u566.png"/>
            <div id="u1215_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1216" class="ax_default label transition notrs">
            <div id="u1216_div" class=""></div>
            <div id="u1216_text" class="text ">
              <p><span>4.5分</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1217" class="ax_default" data-left="288" data-top="362" data-width="168" data-height="26" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u1218" class="ax_default _图片 transition notrs">
            <img id="u1218_img" class="img " src="images/交付中/u566.png"/>
            <div id="u1218_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1219" class="ax_default label transition notrs">
            <div id="u1219_div" class=""></div>
            <div id="u1219_text" class="text ">
              <p><span>4.5分</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1220" class="ax_default" data-left="288" data-top="395" data-width="168" data-height="26" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u1221" class="ax_default _图片 transition notrs">
            <img id="u1221_img" class="img " src="images/交付中/u566.png"/>
            <div id="u1221_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1222" class="ax_default label transition notrs">
            <div id="u1222_div" class=""></div>
            <div id="u1222_text" class="text ">
              <p><span>4.5分</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1223" class="ax_default button transition notrs">
          <div id="u1223_div" class=""></div>
          <div id="u1223_text" class="text ">
            <p><span>耐心细致</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1224" class="ax_default button transition notrs">
          <div id="u1224_div" class=""></div>
          <div id="u1224_text" class="text ">
            <p><span>专业扎实</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1225" class="ax_default button transition notrs">
          <div id="u1225_div" class=""></div>
          <div id="u1225_text" class="text ">
            <p><span>响应及时</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1226" class="ax_default button transition notrs">
          <div id="u1226_div" class=""></div>
          <div id="u1226_text" class="text ">
            <p><span>服务周到</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1227" class="ax_default button transition notrs">
          <div id="u1227_div" class=""></div>
          <div id="u1227_text" class="text ">
            <p><span>讲解清楚</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1228" class="ax_default button transition notrs">
          <div id="u1228_div" class=""></div>
          <div id="u1228_text" class="text ">
            <p><span>保质保量</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1229" class="ax_default label transition notrs">
        <div id="u1229_div" class=""></div>
        <div id="u1229_text" class="text ">
          <p><span>感谢老师的辅导，论文终于通过审核了感谢老师的辅导，论文终于</span></p><p><span>通过审核了。</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
