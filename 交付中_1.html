﻿<!DOCTYPE html>
<html>
  <head>
    <title>交付中</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/交付中_1/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/交付中_1/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1568" class="ax_default box_1 transition notrs">
        <div id="u1568_div" class=""></div>
        <div id="u1568_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1569" class="ax_default box_1 transition notrs">
        <div id="u1569_div" class=""></div>
        <div id="u1569_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1570" class="ax_default label transition notrs">
        <div id="u1570_div" class=""></div>
        <div id="u1570_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">&nbsp; 订单号：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">JS13455555333&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1571" class="ax_default label transition notrs">
        <div id="u1571_div" class=""></div>
        <div id="u1571_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1572" class="ax_default label transition notrs">
        <div id="u1572_div" class=""></div>
        <div id="u1572_text" class="text ">
          <p><span>我的订单</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1573" class="ax_default _图片 transition notrs">
        <img id="u1573_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1573_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1574" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1574_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1574_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1575" class="ax_default box_1 transition notrs">
              <div id="u1575_div" class=""></div>
              <div id="u1575_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1576" class="ax_default label transition notrs">
              <div id="u1576_div" class=""></div>
              <div id="u1576_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1577" class="ax_default label transition notrs">
              <div id="u1577_div" class=""></div>
              <div id="u1577_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1578" class="ax_default button transition notrs">
              <div id="u1578_div" class=""></div>
              <div id="u1578_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1579" class="ax_default primary_button transition notrs">
              <div id="u1579_div" class=""></div>
              <div id="u1579_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1574_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1574_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1580" class="ax_default box_1 transition notrs">
              <div id="u1580_div" class=""></div>
              <div id="u1580_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1581" class="ax_default label transition notrs">
              <div id="u1581_div" class=""></div>
              <div id="u1581_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1582" class="ax_default label transition notrs">
              <div id="u1582_div" class=""></div>
              <div id="u1582_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1583" class="ax_default button transition notrs">
              <div id="u1583_div" class=""></div>
              <div id="u1583_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1584" class="ax_default primary_button transition notrs">
              <div id="u1584_div" class=""></div>
              <div id="u1584_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1585" class="ax_default label transition notrs">
              <div id="u1585_div" class=""></div>
              <div id="u1585_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1586" class="ax_default text_field transition notrs">
              <div id="u1586_div" class=""></div>
              <input id="u1586_input" type="text" value="" class="u1586_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1587" class="ax_default label transition notrs">
              <div id="u1587_div" class=""></div>
              <div id="u1587_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1588" class="ax_default" data-left="28" data-top="175" data-width="107" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1589" class="ax_default label transition notrs">
          <div id="u1589_div" class=""></div>
          <div id="u1589_text" class="text ">
            <p><span>辅导项目：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1590" class="ax_default shape transition notrs">
          <div id="u1590_div" class=""></div>
          <div id="u1590_text" class="text ">
            <p><span>作业</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1591" class="ax_default" data-left="343" data-top="175" data-width="97" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1592" class="ax_default label transition notrs">
          <div id="u1592_div" class=""></div>
          <div id="u1592_text" class="text ">
            <p><span>总金额：2000元</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1593" class="ax_default" data-left="28" data-top="212" data-width="85" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1594" class="ax_default label transition notrs">
          <div id="u1594_div" class=""></div>
          <div id="u1594_text" class="text ">
            <p><span>交付阶段：1/5</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1595" class="ax_default line transition notrs">
        <svg data="images/订单列表/u251.svg" id="u1595_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -43.5 -110.5 )">
    <path d="M 0 1.5  L 54 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 45 112 )" class="stroke" />
  </g>
        </svg>
        <div id="u1595_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1596" class="ax_default" data-left="361" data-top="210" data-width="79" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1597" class="ax_default label transition notrs">
          <div id="u1597_div" class=""></div>
          <div id="u1597_text" class="text ">
            <p><span>学生：张菲菲</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (热区) -->
      <div id="u1598" class="ax_default">
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1599" class="ax_default label transition notrs">
        <div id="u1599_div" class=""></div>
        <div id="u1599_text" class="text ">
          <p><span>投递中（3）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1600" class="ax_default label transition notrs">
        <div id="u1600_div" class=""></div>
        <div id="u1600_text" class="text ">
          <p><span>交付中（1）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1601" class="ax_default label transition notrs">
        <div id="u1601_div" class=""></div>
        <div id="u1601_text" class="text ">
          <p><span>已完成（2）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1602" class="ax_default label transition notrs">
        <div id="u1602_div" class=""></div>
        <div id="u1602_text" class="text ">
          <p><span>已拒绝（5）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1603" class="ax_default sticky_1 transition notrs">
        <div id="u1603_div" class=""></div>
        <div id="u1603_text" class="text ">
          <p><span>排序：按照开始合作时间倒序</span></p><p><span>分页：每页10个</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
