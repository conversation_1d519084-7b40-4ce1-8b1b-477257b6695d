﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情（交付信息）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情（交付信息）_4/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情（交付信息）_4/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1899" class="ax_default box_1 transition notrs">
        <div id="u1899_div" class=""></div>
        <div id="u1899_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1900" class="ax_default label transition notrs">
        <div id="u1900_div" class=""></div>
        <div id="u1900_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1901" class="ax_default label transition notrs">
        <div id="u1901_div" class=""></div>
        <div id="u1901_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1902" class="ax_default _图片 transition notrs">
        <img id="u1902_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1902_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1903" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1903_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1903_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1904" class="ax_default box_1 transition notrs">
              <div id="u1904_div" class=""></div>
              <div id="u1904_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1905" class="ax_default label transition notrs">
              <div id="u1905_div" class=""></div>
              <div id="u1905_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1906" class="ax_default label transition notrs">
              <div id="u1906_div" class=""></div>
              <div id="u1906_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1907" class="ax_default button transition notrs">
              <div id="u1907_div" class=""></div>
              <div id="u1907_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1908" class="ax_default primary_button transition notrs">
              <div id="u1908_div" class=""></div>
              <div id="u1908_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1903_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1903_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1909" class="ax_default box_1 transition notrs">
              <div id="u1909_div" class=""></div>
              <div id="u1909_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1910" class="ax_default label transition notrs">
              <div id="u1910_div" class=""></div>
              <div id="u1910_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1911" class="ax_default label transition notrs">
              <div id="u1911_div" class=""></div>
              <div id="u1911_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1912" class="ax_default button transition notrs">
              <div id="u1912_div" class=""></div>
              <div id="u1912_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1913" class="ax_default primary_button transition notrs">
              <div id="u1913_div" class=""></div>
              <div id="u1913_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1914" class="ax_default label transition notrs">
              <div id="u1914_div" class=""></div>
              <div id="u1914_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1915" class="ax_default text_field transition notrs">
              <div id="u1915_div" class=""></div>
              <input id="u1915_input" type="text" value="" class="u1915_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1916" class="ax_default label transition notrs">
              <div id="u1916_div" class=""></div>
              <div id="u1916_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1917" class="ax_default" data-left="37" data-top="174" data-width="410" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1918" class="ax_default label transition notrs">
          <div id="u1918_div" class=""></div>
          <div id="u1918_text" class="text ">
            <p><span>第1阶段</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1919" class="ax_default label transition notrs">
          <div id="u1919_div" class=""></div>
          <div id="u1919_text" class="text ">
            <p><span>1.000</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1920" class="ax_default label transition notrs">
          <div id="u1920_div" class=""></div>
          <div id="u1920_text" class="text ">
            <p><span>已验收</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1921" class="ax_default" data-left="16.999483828313878" data-top="133" data-width="453.00103234337223" data-height="24.62717305005009" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1922" class="ax_default label transition notrs">
          <div id="u1922_div" class=""></div>
          <div id="u1922_text" class="text ">
            <p><span>交付阶段</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1923" class="ax_default" data-left="203" data-top="133" data-width="53" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1924" class="ax_default label transition notrs">
            <div id="u1924_div" class=""></div>
            <div id="u1924_text" class="text ">
              <p><span>阶段金额</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1925" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u1925_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u1925_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u1926" class="ax_default" data-left="370" data-top="133" data-width="53" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1927" class="ax_default label transition notrs">
            <div id="u1927_div" class=""></div>
            <div id="u1927_text" class="text ">
              <p><span>阶段状态</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1928" class="ax_default" data-left="37" data-top="211" data-width="410" data-height="23" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1929" class="ax_default label transition notrs">
          <div id="u1929_div" class=""></div>
          <div id="u1929_text" class="text ">
            <p><span>第2阶段</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1930" class="ax_default label transition notrs">
          <div id="u1930_div" class=""></div>
          <div id="u1930_text" class="text ">
            <p><span>已验收</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1931" class="ax_default label transition notrs">
          <div id="u1931_div" class=""></div>
          <div id="u1931_text" class="text ">
            <p><span>2.000</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1932" class="ax_default" data-left="37" data-top="247" data-width="404" data-height="20" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1933" class="ax_default" data-left="37" data-top="247" data-width="85" data-height="20" layer-opacity="1">

          <!-- Unnamed (组合) -->
          <div id="u1934" class="ax_default" data-left="37" data-top="247" data-width="85" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1935" class="ax_default label transition notrs">
              <div id="u1935_div" class=""></div>
              <div id="u1935_text" class="text ">
                <p><span>第3阶段</span></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1936" class="ax_default label transition notrs">
          <div id="u1936_div" class=""></div>
          <div id="u1936_text" class="text ">
            <p><span>3.000</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1937" class="ax_default label transition notrs">
          <div id="u1937_div" class=""></div>
          <div id="u1937_text" class="text ">
            <p><span>待交付</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1938" class="ax_default" data-left="37" data-top="284" data-width="419" data-height="20" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1939" class="ax_default" data-left="37" data-top="284" data-width="83" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1940" class="ax_default label transition notrs">
            <div id="u1940_div" class=""></div>
            <div id="u1940_text" class="text ">
              <p><span>第4阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1941" class="ax_default label transition notrs">
          <div id="u1941_div" class=""></div>
          <div id="u1941_text" class="text ">
            <p><span>4.000</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1942" class="ax_default label transition notrs">
          <div id="u1942_div" class=""></div>
          <div id="u1942_text" class="text ">
            <p><span>已交付待验收</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1943" class="ax_default" data-left="37" data-top="321" data-width="404" data-height="22" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1944" class="ax_default" data-left="37" data-top="323" data-width="86" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1945" class="ax_default label transition notrs">
            <div id="u1945_div" class=""></div>
            <div id="u1945_text" class="text ">
              <p><span>第5阶段</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1946" class="ax_default label transition notrs">
          <div id="u1946_div" class=""></div>
          <div id="u1946_text" class="text ">
            <p><span>3.000</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1947" class="ax_default label transition notrs">
          <div id="u1947_div" class=""></div>
          <div id="u1947_text" class="text ">
            <p><span>待支付</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1948" class="ax_default label transition notrs">
        <div id="u1948_div" class=""></div>
        <div id="u1948_text" class="text ">
          <p><span>注释：</span></p><p><span>1、分阶段支付：学生需预先支付当前阶段费用，状态变为待交付后方可开始作业，防止作业后不付款。</span></p><p><span>2、验收机制：</span></p><p><span>老师完成阶段任务后，需与学生线下确认成果；</span></p><p><span>学生须在平台进行线上验收，状态变更为【已验收】后方可进入下一阶段。</span></p><p><span>3、提现条件：所有阶段均达【已验收】状态，且学生14天无异议期结束后，方可提现。</span></p><p><span>4、订单终止：需师生协商一致，由学生在平台主动终止订单。</span></p><p><span>5、争议处理：若产生分歧，双方可申请平台客服介入协商。</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1949" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u316.svg" id="u1949_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
        </svg>
        <div id="u1949_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1950" class="ax_default" data-left="37" data-top="372" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1951" class="ax_default label transition notrs">
          <div id="u1951_div" class=""></div>
          <div id="u1951_text" class="text ">
            <p><span>共5阶段</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1952" class="ax_default" data-left="198" data-top="372" data-width="40" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1953" class="ax_default label transition notrs">
          <div id="u1953_div" class=""></div>
          <div id="u1953_text" class="text ">
            <p><span>13.000</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1954" class="ax_default" data-left="375" data-top="372" data-width="40" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u1955" class="ax_default label transition notrs">
          <div id="u1955_div" class=""></div>
          <div id="u1955_text" class="text ">
            <p><span>进行中</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1956" class="ax_default primary_button transition notrs">
        <div id="u1956_div" class=""></div>
        <div id="u1956_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">注意</span><span style="font-family:&quot;AppleColorEmoji&quot;, &quot;Apple Color Emoji&quot;, sans-serif;">📢</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">：线下交易有风险，出现问题无人管。</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">一旦发现会封号，也不利于攒人气。</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1957" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u1957_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u1957_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1958" class="ax_default label transition notrs">
        <div id="u1958_div" class=""></div>
        <div id="u1958_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1959" class="ax_default label transition notrs">
        <div id="u1959_div" class=""></div>
        <div id="u1959_text" class="text ">
          <p><span>交付信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1960" class="ax_default label transition notrs">
        <div id="u1960_div" class=""></div>
        <div id="u1960_text" class="text ">
          <p><span>评价信息</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
