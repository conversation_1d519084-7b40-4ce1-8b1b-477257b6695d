﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单详情（交付信息）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单详情（交付信息）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单详情（交付信息）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u686" class="ax_default box_1 transition notrs">
        <div id="u686_div" class=""></div>
        <div id="u686_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u687" class="ax_default label transition notrs">
        <div id="u687_div" class=""></div>
        <div id="u687_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u688" class="ax_default label transition notrs">
        <div id="u688_div" class=""></div>
        <div id="u688_text" class="text ">
          <p><span>订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u689" class="ax_default _图片 transition notrs">
        <img id="u689_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u689_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u690" class="ax_default sticky_1 transition notrs">
        <div id="u690_div" class=""></div>
        <div id="u690_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">阶段状态：</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">待支付</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">—&gt;</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">已付款待交付</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">—&gt;</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">已验收</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">当前一个阶段为已验收时，下个阶段自动变化待付款</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">如果当前阶段为最后一个阶段，已验收后整个订单状态为待评价</span></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u691" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u691_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u691_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u692" class="ax_default box_1 transition notrs">
              <div id="u692_div" class=""></div>
              <div id="u692_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u693" class="ax_default label transition notrs">
              <div id="u693_div" class=""></div>
              <div id="u693_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u694" class="ax_default label transition notrs">
              <div id="u694_div" class=""></div>
              <div id="u694_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u695" class="ax_default button transition notrs">
              <div id="u695_div" class=""></div>
              <div id="u695_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u696" class="ax_default primary_button transition notrs">
              <div id="u696_div" class=""></div>
              <div id="u696_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u691_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u691_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u697" class="ax_default box_1 transition notrs">
              <div id="u697_div" class=""></div>
              <div id="u697_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u698" class="ax_default label transition notrs">
              <div id="u698_div" class=""></div>
              <div id="u698_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u699" class="ax_default label transition notrs">
              <div id="u699_div" class=""></div>
              <div id="u699_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u700" class="ax_default button transition notrs">
              <div id="u700_div" class=""></div>
              <div id="u700_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u701" class="ax_default primary_button transition notrs">
              <div id="u701_div" class=""></div>
              <div id="u701_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u702" class="ax_default label transition notrs">
              <div id="u702_div" class=""></div>
              <div id="u702_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u703" class="ax_default text_field transition notrs">
              <div id="u703_div" class=""></div>
              <input id="u703_input" type="text" value="" class="u703_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u704" class="ax_default label transition notrs">
              <div id="u704_div" class=""></div>
              <div id="u704_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u705" class="ax_default label transition notrs">
        <div id="u705_div" class=""></div>
        <div id="u705_text" class="text ">
          <p><span>第1阶段</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u706" class="ax_default primary_button transition notrs">
        <div id="u706_div" class=""></div>
        <div id="u706_text" class="text ">
          <p><span>去支付</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u707" class="ax_default label transition notrs">
        <div id="u707_div" class=""></div>
        <div id="u707_text" class="text ">
          <p><span>1.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u708" class="ax_default label transition notrs">
        <div id="u708_div" class=""></div>
        <div id="u708_text" class="text ">
          <p><span>交付阶段</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u709" class="ax_default" data-left="128" data-top="144" data-width="62" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u710" class="ax_default label transition notrs">
          <div id="u710_div" class=""></div>
          <div id="u710_text" class="text ">
            <p><span>阶段费用</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u711" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u316.svg" id="u711_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
        </svg>
        <div id="u711_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u712" class="ax_default label transition notrs">
        <div id="u712_div" class=""></div>
        <div id="u712_text" class="text ">
          <p><span>第2阶段</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u713" class="ax_default label transition notrs">
        <div id="u713_div" class=""></div>
        <div id="u713_text" class="text ">
          <p><span>已验收</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u714" class="ax_default" data-left="18.99948382831387" data-top="101" data-width="453.00103234337223" data-height="30.62717305005009" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u715" class="ax_default" data-left="37" data-top="103" data-width="53" data-height="18" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u716" class="ax_default label transition notrs">
            <div id="u716_div" class=""></div>
            <div id="u716_text" class="text ">
              <p><span>辅导老师</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u717" class="ax_default" data-left="421" data-top="101" data-width="35" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u718" class="ax_default label transition notrs">
            <div id="u718_div" class=""></div>
            <div id="u718_text" class="text ">
              <p><span>张伟&gt;</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u719" class="ax_default line transition notrs">
          <svg data="images/订单详情__待接单）/u316.svg" id="u719_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
          </svg>
          <div id="u719_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u720" class="ax_default" data-left="37" data-top="258" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u721" class="ax_default" data-left="37" data-top="258" data-width="48" data-height="20" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u722" class="ax_default label transition notrs">
            <div id="u722_div" class=""></div>
            <div id="u722_text" class="text ">
              <p><span>第3阶段</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u723" class="ax_default" data-left="37" data-top="292" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u724" class="ax_default label transition notrs">
          <div id="u724_div" class=""></div>
          <div id="u724_text" class="text ">
            <p><span>第4阶段</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u725" class="ax_default" data-left="39" data-top="323" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u726" class="ax_default label transition notrs">
          <div id="u726_div" class=""></div>
          <div id="u726_text" class="text ">
            <p><span>第5阶段</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u727" class="ax_default label transition notrs">
        <div id="u727_div" class=""></div>
        <div id="u727_text" class="text ">
          <p><span>注释：</span></p><p><span>1、每个阶段需要先支付，老师收到支付信息后才会开始作业。</span></p><p><span>2、老师提交阶段交付时，请先确认是否达到约定的标准，达标后再点击确认验收。</span></p><p><span>3、所有阶段都是已验收后辅导老师才能提现。</span></p><p><span>4、如果和辅导老师之前差生分歧可以找客服进行协商。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u728" class="ax_default primary_button transition notrs">
        <div id="u728_div" class=""></div>
        <div id="u728_text" class="text ">
          <p><span>确认验收</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u729" class="ax_default" data-left="225" data-top="144" data-width="57" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u730" class="ax_default label transition notrs">
          <div id="u730_div" class=""></div>
          <div id="u730_text" class="text ">
            <p><span>阶段状态</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u731" class="ax_default" data-left="386" data-top="144" data-width="53" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u732" class="ax_default label transition notrs">
          <div id="u732_div" class=""></div>
          <div id="u732_text" class="text ">
            <p><span>操作</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u733" class="ax_default label transition notrs">
        <div id="u733_div" class=""></div>
        <div id="u733_text" class="text ">
          <p><span>已验收</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u734" class="ax_default label transition notrs">
        <div id="u734_div" class=""></div>
        <div id="u734_text" class="text ">
          <p><span>2.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u735" class="ax_default label transition notrs">
        <div id="u735_div" class=""></div>
        <div id="u735_text" class="text ">
          <p><span>3.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u736" class="ax_default label transition notrs">
        <div id="u736_div" class=""></div>
        <div id="u736_text" class="text ">
          <p><span>4.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u737" class="ax_default label transition notrs">
        <div id="u737_div" class=""></div>
        <div id="u737_text" class="text ">
          <p><span>3.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u738" class="ax_default label transition notrs">
        <div id="u738_div" class=""></div>
        <div id="u738_text" class="text ">
          <p><span>待交付</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u739" class="ax_default label transition notrs">
        <div id="u739_div" class=""></div>
        <div id="u739_text" class="text ">
          <p><span>已交付待验收</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u740" class="ax_default label transition notrs">
        <div id="u740_div" class=""></div>
        <div id="u740_text" class="text ">
          <p><span>待支付</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u741" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u316.svg" id="u741_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -18 -236 )">
    <path d="M 0 0.5  L 453 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 18 236 )" class="stroke" />
  </g>
        </svg>
        <div id="u741_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u742" class="ax_default" data-left="39" data-top="364" data-width="48" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u743" class="ax_default label transition notrs">
          <div id="u743_div" class=""></div>
          <div id="u743_text" class="text ">
            <p><span>共5阶段</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u744" class="ax_default" data-left="119" data-top="364" data-width="40" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u745" class="ax_default label transition notrs">
          <div id="u745_div" class=""></div>
          <div id="u745_text" class="text ">
            <p><span>13.000</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u746" class="ax_default" data-left="228" data-top="364" data-width="40" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u747" class="ax_default label transition notrs">
          <div id="u747_div" class=""></div>
          <div id="u747_text" class="text ">
            <p><span>进行中</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (表格) -->
      <div id="u748" class="ax_default">

        <!-- Unnamed (单元格) -->
        <div id="u749" class="ax_default table_cell transition notrs">
          <svg data="images/订单详情（交付信息）/u749.svg" id="u749_img" class="img generatedImage" viewbox="0 0 100 40">

  <path d="M 1 1  L 100 1  L 100 40  L 1 40  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" class="fill" />
  <path d="M 0.5 1  L 0.5 40  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" class="stroke" />
  <path d="M 0 0.5  L 100 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" class="stroke" />
          </svg>
          <div id="u749_text" class="text ">
            <p><span>阶段</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u750" class="ax_default table_cell transition notrs">
          <svg data="images/订单详情（交付信息）/u750.svg" id="u750_img" class="img generatedImage" viewbox="100 0 421 40">

  <path d="M 1 1  L 420 1  L 420 40  L 1 40  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 100 0 )" class="fill" />
  <path d="M 0.5 1  L 0.5 40  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 0 )" class="stroke" />
  <path d="M 0 0.5  L 421 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 0 )" class="stroke" />
  <path d="M 420.5 1  L 420.5 40  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 0 )" class="stroke" />
          </svg>
          <div id="u750_text" class="text ">
            <p><span>确认合作时填写的阶段</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u751" class="ax_default table_cell transition notrs">
          <svg data="images/订单详情（交付信息）/u751.svg" id="u751_img" class="img generatedImage" viewbox="0 40 100 30">

  <path d="M 1 1  L 100 1  L 100 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 40 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 40 )" class="stroke" />
  <path d="M 0 0.5  L 100 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 40 )" class="stroke" />
          </svg>
          <div id="u751_text" class="text ">
            <p><span>阶段金额</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u752" class="ax_default table_cell transition notrs">
          <svg data="images/订单详情（交付信息）/u752.svg" id="u752_img" class="img generatedImage" viewbox="100 40 421 30">

  <path d="M 1 1  L 420 1  L 420 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 100 40 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 40 )" class="stroke" />
  <path d="M 0 0.5  L 421 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 40 )" class="stroke" />
  <path d="M 420.5 1  L 420.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 40 )" class="stroke" />
          </svg>
          <div id="u752_text" class="text ">
            <p><span>确认合作时填写的阶段金额</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u753" class="ax_default table_cell transition notrs">
          <svg data="images/订单详情（交付信息）/u753.svg" id="u753_img" class="img generatedImage" viewbox="0 70 100 111">

  <path d="M 1 1  L 100 1  L 100 111  L 1 111  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 70 )" class="fill" />
  <path d="M 0.5 1  L 0.5 111  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 70 )" class="stroke" />
  <path d="M 0 0.5  L 100 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 70 )" class="stroke" />
          </svg>
          <div id="u753_text" class="text ">
            <p><span>阶段状态</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u754" class="ax_default table_cell transition notrs">
          <svg data="images/订单详情（交付信息）/u754.svg" id="u754_img" class="img generatedImage" viewbox="100 70 421 111">

  <path d="M 1 1  L 420 1  L 420 111  L 1 111  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 100 70 )" class="fill" />
  <path d="M 0.5 1  L 0.5 111  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 70 )" class="stroke" />
  <path d="M 0 0.5  L 421 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 70 )" class="stroke" />
  <path d="M 420.5 1  L 420.5 111  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 70 )" class="stroke" />
          </svg>
          <div id="u754_text" class="text ">
            <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">1</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">、共3个状态：待支付、待交付、已交付待验收、已验收</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">2、待支付对应的操作是去支付，待交付对应的操作是确认交付</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">支付后状态变为待交付，确认交付后状态变为已验收</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u755" class="ax_default table_cell transition notrs">
          <svg data="images/订单详情（交付信息）/u755.svg" id="u755_img" class="img generatedImage" viewbox="0 181 100 111">

  <path d="M 1 1  L 100 1  L 100 110  L 1 110  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 181 )" class="fill" />
  <path d="M 0.5 1  L 0.5 110  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 181 )" class="stroke" />
  <path d="M 0 0.5  L 100 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 181 )" class="stroke" />
  <path d="M 0 110.5  L 100 110.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 181 )" class="stroke" />
          </svg>
          <div id="u755_text" class="text ">
            <p><span>汇总信息</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u756" class="ax_default table_cell transition notrs">
          <svg data="images/订单详情（交付信息）/u756.svg" id="u756_img" class="img generatedImage" viewbox="100 181 421 111">

  <path d="M 1 1  L 420 1  L 420 110  L 1 110  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 100 181 )" class="fill" />
  <path d="M 0.5 1  L 0.5 110  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 181 )" class="stroke" />
  <path d="M 0 0.5  L 421 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 181 )" class="stroke" />
  <path d="M 420.5 1  L 420.5 110  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 181 )" class="stroke" />
  <path d="M 0 110.5  L 421 110.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 181 )" class="stroke" />
          </svg>
          <div id="u756_text" class="text ">
            <p><span>阶段数量</span></p><p><span>总金额：阶段金额的汇总</span></p><p><span>状态：默认是进行中，当所有阶段的状态都变为已验收后，由进行中改为已完成</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u757" class="ax_default primary_button transition notrs">
        <div id="u757_div" class=""></div>
        <div id="u757_text" class="text ">
          <p><span>终止辅导</span></p>
        </div>
      </div>

      <!-- 确认验收 (动态面板) -->
      <div id="u758" class="ax_default ax_default_hidden" data-label="确认验收" style="display:none; visibility: hidden">
        <div id="u758_state0" class="panel_state" data-label="确认验收" style="">
          <div id="u758_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u759" class="ax_default box_1 transition notrs">
              <div id="u759_div" class=""></div>
              <div id="u759_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u760" class="ax_default _一级标题 transition notrs">
              <div id="u760_div" class=""></div>
              <div id="u760_text" class="text ">
                <p><span>是否确认验收？</span></p><p><span>验收后会将对应阶段费用打给老师</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u761" class="ax_default line transition notrs">
              <svg data="images/订单详情（交付信息）/u761.svg" id="u761_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 0 -111 )">
    <path d="M 0 0.5  L 345 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(170, 170, 170, 1)" fill="none" transform="matrix(1 0 0 1 0 111 )" class="stroke" />
  </g>
              </svg>
              <div id="u761_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u762" class="ax_default link_button transition notrs">
              <div id="u762_div" class=""></div>
              <div id="u762_text" class="text ">
                <p><span>确认</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u763" class="ax_default link_button transition notrs">
              <div id="u763_div" class=""></div>
              <div id="u763_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u758_state1" class="panel_state" data-label="终止订单" style="visibility: hidden;">
          <div id="u758_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u764" class="ax_default box_1 transition notrs">
              <div id="u764_div" class=""></div>
              <div id="u764_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u765" class="ax_default _一级标题 transition notrs">
              <div id="u765_div" class=""></div>
              <div id="u765_text" class="text ">
                <p><span>是否确认终止订单？</span></p><p><span>终止订单后无法恢复</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u766" class="ax_default line transition notrs">
              <svg data="images/订单详情（交付信息）/u761.svg" id="u766_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 0 -111 )">
    <path d="M 0 0.5  L 345 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(170, 170, 170, 1)" fill="none" transform="matrix(1 0 0 1 0 111 )" class="stroke" />
  </g>
              </svg>
              <div id="u766_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u767" class="ax_default link_button transition notrs">
              <div id="u767_div" class=""></div>
              <div id="u767_text" class="text ">
                <p><span>确认</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u768" class="ax_default text_field transition notrs">
              <div id="u768_div" class=""></div>
              <input id="u768_input" type="text" value="" class="u768_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u769" class="ax_default link_button transition notrs">
              <div id="u769_div" class=""></div>
              <div id="u769_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u770" class="ax_default primary_button transition notrs">
        <div id="u770_div" class=""></div>
        <div id="u770_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">注意</span><span style="font-family:&quot;AppleColorEmoji&quot;, &quot;Apple Color Emoji&quot;, sans-serif;">📢</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">：线下交易有风险，出现问题无人管。</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 平台交易进托管，钱财质量有保障。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u771" class="ax_default sticky_1 transition notrs">
        <div id="u771_div" class=""></div>
        <div id="u771_text" class="text ">
          <p><span>终止辅导相当于完成，变为待评价</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u772" class="ax_default label transition notrs">
        <div id="u772_div" class=""></div>
        <div id="u772_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u773" class="ax_default label transition notrs">
        <div id="u773_div" class=""></div>
        <div id="u773_text" class="text ">
          <p><span>交付信息</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u774" class="ax_default line transition notrs">
        <svg data="images/订单详情__待接单）/u368.svg" id="u774_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -68.5 -103.5 )">
    <path d="M 0 1.5  L 53 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 70 105 )" class="stroke" />
  </g>
        </svg>
        <div id="u774_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
