﻿<html>
    <head>
        <title>Install the Axure RP Chrome Extension</title>
        <style type="text/css">
            * {
                font-family: "Source Sans Pro", sans-serif;
            }
            body {
                text-align: center;
                background-color: #f7fafc;
                color: #37465e;
            }
            p {
                font-size: 14px;
                line-height: 18px;
                color: #333333;
            }
            div.container {
                width: 980px;
                margin-left: auto;
                margin-right: auto;
                text-align: left;
            }
            a {
                text-decoration: none;
                color: #ffffff;
            }
            .button {
                display: block;
                width: 240px;
                height: 30px;
                padding: 2px 8px 2px 8px;
                border-radius: 4px;
                background-color: #3eacef;
                box-sizing: border-box;
                font-family: "Source Sans Pro SemiBold", "Source Sans Pro", sans-serif;
                font-weight: 600;
                color: #ffffff;
                text-align: center;
                font-size: 14px;
                line-height: 28px;
            }
            a:hover.button {
                background-color: #1482c5;
            }
            div.left {
                width: 400px;
                float: left;
                margin-right: 80px;
            }
            div.right {
                width: 400px;
                float: left;
            }
            div.buttonContainer {
                text-align: center;
            }
            .header {
                font-size: 22px;
                color: #333333;
                line-height: 50px;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <br />
            <br />
            <img src="axure_logo.png" alt="axure" style="width: 100px" />
            <br />
            <br />
            <p class="header">
                Axure RP Extension for Chrome
            </p>
            <p>
                Google Chrome requires an extension to view locally published prototypes. Alternatively, upload your RP file to <a href="https://www.axure.cloud" style="color: #53ADE9">Axure Cloud</a> or use a different browser. You can also Preview from Axure RP.
</p>
            <img src="preview-rp.png" alt="preview" />
            <p class="header">
                View locally published prototypes in Chrome
            </p>
            <div class="left">
                <p>
                    1. Add the extension from Chrome Web Store.
                </p>
                <div class="buttonContainer">
                    <a class="button" href="https://chrome.google.com/webstore/detail/dogkpdfcklifaemcdfbildhcofnopogp"
                       target="_blank">Add Extension</a>
                </div>
            </div>
            <div class="right">
                <p>
                    2. Open <b>More tools → Extensions</b>
                </p>
                <img src="extensions.png" alt="extensions" />
            </div>
            <div style="clear: both; height: 20px;">&nbsp;</div>
            <div class="left">
                <p>
                    3. View Axure RP Extension <b>Details</b>
                </p>
                <img src="details.png" alt="extension details" />
            </div>
            <div class="right">
                <p>
                    4. Turn on <b>Allow access to file URLs</b>
                </p>
                <img src="allow-access.png" alt="allow access" />
            </div>
            <div style="clear: both; height: 20px;">&nbsp;</div>
            <div class="left">
                <p>
                    5. Click this button
                </p>
                <div class="buttonContainer">
                    <a class="button" href="../../start.html">View in Chrome</a>
                </div>
            </div>
            <div style="clear: both; height: 20px;">
            </div>
            <p class="header">
                Need private or on-premises hosting?
            </p>
            <p>
                Axure Cloud for Business gives you all of the features of Axure Cloud plus control of accounts and permissions. For hosting behind your firewall, check out Axure Cloud On-Premises.
            </p>
            <div class="buttonContainer">
                <a class="button" href="https://www.axure.com/axure-cloud#hosted">Learn more</a>
            </div>
            <div style="clear: both; height: 20px;"></div>
            <p class="header">
                FAQs
            </p>
            <p><b>Why do I need to install the extension?</b></p>
            <p>
                Google requires this extension to be installed to allow the viewing of locally published HTML files in Chrome.
            </p>
            <div style="clear: both; height: 10px;"></div>
            <p>
                <b>Why does this extension require a high access level?</b>
            </p>
            <p>
                This extension requires a high access level to allow the viewing of the file:// protocol. Axure does not track or access any of your information.
            </p>
            <div style="clear: both; height: 10px;"></div>
            <p class="header">
                We're here to help
            </p>
            <p>
                If you have any questions, please email us at <a href="mailto:<EMAIL>" style="color: #53ADE9"><EMAIL></a>.
            </p>
            <div style="clear: both; height: 20px;"></div>
        </div>
    </body>
</html>
