﻿body {
    font-family: 'Inter', ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji";
    font-size: 14px;
    color: #1e293b;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    -webkit-font-feature-settings: "kern";
    -moz-font-feature-settings: "kern";
    -moz-font-feature-settings: "kern=1";
    font-feature-settings: "kern" 1;
    font-kerning: normal;
    overflow: hidden;
}

a {
  cursor: pointer;
}

input[type="radio"], input[type="checkbox"] {
    margin: 0px 9px 0px 0px;
    vertical-align: bottom;
}

input {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

input[type=text]::-ms-clear {
    width: 0;
    height: 0;
    display: none;
}

#outerContainer {
    position: absolute;
    width:1000px;
    height:1500px;
    overflow: hidden;
    display: flex;
}

#mobileControlFrameContainer {
    position: absolute;
    width: 100%;
    pointer-events: none;
}

.splitbar {
    display: none;
    position: absolute;
    top: 0px;
    width: 3px;
    height: 100%;
    cursor: ew-resize;
    z-index: 5;
    user-select: none;
}

.splitbar:hover, .splitbar.active {
    background: #E2E8F0;
}

#lsplitbar {
    border-right: 1px solid #E2E8F0;
}

#rsplitbar {
    border-left: 1px solid #E2E8F0;
}

#mainPanel {
    background-color: #d8d8d8;
    opacity: 0; 
    flex: 1;
    overflow: hidden;
}

#clippingBounds {
    width: 100%;
    overflow: hidden;
    pointer-events: none;
    position: absolute;
    z-index: 1;
}

#clippingBounds div {
    pointer-events: auto;
}

#clippingBoundsScrollContainer {
    position: absolute;
    pointer-events: none;
}

#browserOutOfDateNotification {
    width: 100%;
    height: 289px;
    background-color: #e36154;
    padding-top: 40px;
    color: #FFFFFF;
}
    .mobileMode #browserOutOfDateNotification {
        padding-top: 15px;
    }

#supportedBrowsersListContainer {
    border-radius: 5px;
    line-height: 1.64;
    background-color: #c3463a;
    padding-top: 4px;
    width: 255px;
    margin: auto;
}

.browserName {
    display: inline-block;
    width: 55%;
    font-weight: bold;
    margin-left: 18px;
}

.browserSupportedVersion {
    display: inline-block;
    font-style: italic;
}

#browserOutOfDateNotificationButtons {
    display: flex;
    justify-content: flex-end;
    margin-top: 28px;
}
    .mobileMode #browserOutOfDateNotificationButtons {
        margin-top: 8px;
    }

#updateBrowserButton {
    display: inline-block;
    width: 330px;
    height: 35px;
    margin-left: auto;
    line-height: 35px;
    text-decoration: none;
    text-align: center;
    border-radius: 9px;
    border: solid 1px #FFFFFF;
    color: #FFFFFF;
}

#continueToPrototypeButton {
    display: inline-block;
    text-align: center;
    line-height: 37px;
    text-decoration: underline;
}

#topPanel {
    z-index: 1;
    height: 40px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #E2E8F0;
    user-select: none;
}

.leftPanel, .rightPanel, .mobileOnlyPanel {
    position: relative;
    background-color: #FFFFFF;
    overflow: hidden;
    width: 0px;
    flex-shrink: 0;
}


.popup, .leftPanel.popup {
    position: absolute;
    z-index: 20000;
    display: none;
    background-color: #f8fafc;
    border: solid 1px #e2e8f0;
    position: absolute;
    box-shadow: 0 1px 2px 0 rgba(87, 87, 87, 0.5)
}

.leftPanel.popup .sitemapHeader, .leftPanel.popup #searchDiv {
    display: none;
}

#clipFrameScroll {
}

.splitterMask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-image: url(../images/transparent.gif);
    z-index: 10000;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#maximizePanelContainer {
  font-size: 4px;
  overflow: hidden;
  z-index: 1000;
  display: none;
}

#maximizePanel {
    background-color: #FCFDFF;
    cursor: pointer;
}

#maximizePanelContainer, #maximizePanelOver, #maximizePanel {
  position:absolute;
  left: 0px;
  top: 0px;
  width: 28px;
  height: 28px;
}

#interfaceControlFrameMinimizeContainer {
    font-size: 2px; /*for IE*/
    text-align: right;
    z-index: 100;
    height: 28px;
    width: 28px;
    border-right: solid 1px #E2E8F0;
    margin: auto 0;
}
#interfaceControlFrameMinimizeContainer a {
    display: inline-block;
    width: 28px;
    height: 100%;
    font-size: 2px;
    text-decoration: none;
}

#interfaceControlFrame {
    height: 100%;
    display: flex;
    opacity: 0;
}

#interfaceControlFrameCloseContainer {
    display: none;
    font-size: 9px;
    font-weight: bold;
    letter-spacing: 1px;
    z-index: 100;
    width: 55px;
    background-color: #62666b;
    text-align: center;  
}
#interfaceControlFrameCloseContainer a {
    display: inline-block;
    width: 55px;
    color: #ffffff;
    padding: 5px 0px;
}
#inspectControlFrameHeader li {
    float: left;
    display: block;
    position: relative;
    width: 28px;
    height: 28px;
    margin: 6px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

.ax-button {
    font-family: 'Source Sans Pro', 'Trebuchet MS', Arial;
    font-weight: 600;
    font-size: 12px;
    color: #F7FAFC;
    text-align: center;
    white-space: nowrap;
    background-color: #3EACEF;
    user-select: none;
    border: none;
    padding: 3px 16px;
    display: inline-block;
    cursor: pointer;
    border-radius: 4px;
}

.ax-button:hover {
    background-color: #1482c5;
}

.pluginHint {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: end;
    top: 100%;
    box-sizing: border-box;
    padding: 10px;
    font-size: 11px;
    font-weight: normal;
    line-height: normal;
    text-align: left;
    width: 180px;
    background: #ffffff;
    border: 1px solid #d7d7d7;
    color: #718096;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0px 1px 3px rgba(26, 32, 44, 0.2);
}

.pluginHint button {
    margin-top: 10px;
}

.pluginHint.left {
    left: 50%;
    right: auto;
    transform: translateX(-20px);
}
.pluginHint.right {
    left: auto;
    right: 50%;
    transform: translateX(20px);
}

.pluginHint::after {
    content: "";
    width: 10px;
    height: 10px;
    border-top: 1px solid #d7d7d7;
    border-left: 1px solid #d7d7d7;
    background-color: #fff;
    position: absolute;
    top: -6px;
    transform: translateX(-50%) rotate(45deg);
    left: 50%;
}

.pluginHint.left::after {
    left: 20px;
    right: auto;
    transform: translateX(-50%) rotate(45deg);
}
.pluginHint.right::after {
    left: auto;
    right: 20px;
    transform: translateX(50%) rotate(45deg);
}

#inspectControlFrameHeader li a {
    height: 100%;
    display: block;
    text-align: center;
    outline: none;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

.hashover #inspectControlFrameHeader li a:hover {
    border-radius: 3px;
    background-color: #E5E7EB66;
}

/*#inspectControlFrameHeader li a.selected, #inspectControlFrameHeader li a.selected:hover {
    background-color: inherit;
}*/

#inspectControlFrameHeaderContainer {
    overflow: visible;
}

#inspectControlFrameHeader {
    position: relative;
    list-style: none;
    z-index: 50;
    letter-spacing: 1px;
    display: flex;
}

#projectControlFrameHeaderContainer {
    overflow: visible;
}

#projectControlFrameHeader {
    position: relative;
    list-style: none;
    font-size: 8px;
    z-index: 50;
    font-weight: bold;
    letter-spacing: 1px;
}

#projectControlFrameHeader li {
    position: relative;
    float: left;
    display: block;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

#projectControlFrameHeader li a {
    display: block;
    height: 100%;
    width: 32px;
    outline: none;
    margin-bottom: 3px;
    text-decoration: none;
    color: #ffffff;
    white-space: nowrap;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

.hashover #projectControlFrameHeader li a:hover {
    border-radius: 3px;
    background-color: #E5E7EB66;
}

/*#projectControlFrameHeader li a.selected, #projectControlFrameHeader li a.selected:hover {
    background-color: inherit;
}*/

#publishContainer {
    display: none;
    margin: 6px 20.5px 5px;
    width: 150px;
    height: 25px;
    border-radius: 10px;
    background-color: #008dcb;
}

#publishContainer.preview {
    display: block;
}

#overflowBtn {
    order: 5;
}

#overflowMenuButton {
    background: url('../images/overflow-icon.svg') no-repeat center center, linear-gradient(transparent,transparent);
}
#overflowMenuButton.selected {
    background: url('../images/overflow_icon_off.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

.overflowOptionCheckbox, #projectOptionsHotspotsCheckbox {
    position: relative;
    display: inline-block;
}

.overflowOptionCheckbox {
    width: 10px;
    height: 10px;
    margin-right: 10px;
    top: 1px;
}

#projectOptionsHotspotsCheckbox {
    width: 16px;
    height: 16px;
    margin-right: 25px;
    margin-left: 21px;
    top: 2.5px;
}

.overflowOptionCheckbox.selected, #projectOptionsHotspotsCheckbox.selected {
    background: url('../images/overflow_checkmark.svg') no-repeat center center;
}

#overflowMenuContainer {
    display: none;
    flex-direction: column;
    top: 36px;
    right: 80px;
    border-radius: 2px;
}

#overflowMenuContainer.popup {
    display: flex;
}

#scaleMenuContainer {
    display: none;
    flex-direction: column;
    top: 36px;
    right: 200px;
    border-radius: 2px;
}

.vpZoomValue {
    padding: 4px 9px 4px 34px;
    cursor: pointer;
    user-select: none;
    margin: 4px;
    border-radius: 2px;
}

.vpZoomValue.selected {
    background: url(../images/overflow_checkmark.svg) no-repeat 9px center;
}

.vpZoomValue:hover {
    background-color: #E5E7EB66;
}

.showOption {
    font-size: 14px;
	padding: 4px 9px;
    margin: 4px;
    border-radius: 2px;
}

.showOption:hover {
    cursor: pointer;
    background-color: #E5E7EB66;
}

#publishButton {
    display: block;
    width: 95px;
    margin: auto;
    font-size: 14px;
    line-height: 26px;
    color: #ffffff;
}

.maximizeCaret {
    width: 5px;
    height: 5px;
    object-fit: contain;
    border-right: solid 1.5px #525252;
    border-top: solid 1.5px #525252;
    margin: auto;
}
.caret {
    width: 9px;
    height: 7px;
    background: url('../images/caret_down.svg') no-repeat center center, linear-gradient(transparent,transparent);
}
.selected .caret {
    background: url('../images/caret_down_off.svg') no-repeat center center, linear-gradient(transparent,transparent);
}
.upCaret {
    transform: rotate(-45deg);
}
.leftCaret {
    transform: rotate(-135deg);
}
.downCaret {
    transform: rotate(-225deg);
}
.rightCaret {
    transform: rotate(-315deg);
}

#pageSelectDropdown, #adaptiveViewsDropdown {
    display: inline-block;
    margin-left: 8px;
}

.minimizeIcon, .maximizeIcon {
    transition: .25s linear;
    position: absolute;
    height: 28px;
    width: 28px;
}

#minimizeX {
    background: url('../images/close.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

#minimizeArrow {
    opacity: 0;
    transform: rotate(180deg);
    background: url('../images/left_arrow.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

#maximizeButton {
    transform: rotate(0deg);
    background: url('../images/left_arrow.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

.collapseHovered #minimizeX {
    transition: .25s linear;
    opacity: 0;
    transform: rotate(0deg);
}

.collapseHovered #minimizeArrow {
    transition: .25s linear;
    opacity: 1;
    transform: rotate(0deg);
}

#maximizeButton.rotated {
    transition: .20s linear;
    transform: rotate(-180deg);
}

.expandHovered #minimizeX {
    opacity: 0;
}

.expandHovered #minimizeArrow {
    opacity: 1;
}

#separatorContainer {
    display: none; 
    line-height: 24px;
    height: 28px;
    margin: 6px 0 6px 6px;
}

#separatorContainer.hasLeft {
    display: block;
}

.separator {
    display: block;
    width: 1px;
    height: 100%;
    background-color: #E2E8F0;
}

#interfacePageNameContainer {
    float: left;
    cursor: pointer;
}

#sitemapControlFrameContainer {
    display: flex;
    margin: 6px;
}

.hashover #sitemapControlFrameContainer:hover {
    border-radius: 3px;
    background-color: #E5E7EB66;
}

/*.hashover #sitemapControlFrameContainer.selected:hover {
    background-color: inherit;
}*/

#interfaceOverflowMenuContainer {
    position: absolute;
    display: none;
    width: 220px;
    background-color: #FCFDFF;
    right: 240px;
}

.pageNameHeader {
    float: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 175px;
    font-weight: 600;
}

.pageCountHeader {
    float: left;
    white-space: nowrap;
    margin-left: 5px;
    margin-right: 6px;
}

#interfaceAdaptiveViewsContainer {
    display: none;
    margin: 4px 0px 4px 30.5px;
    padding: 0px 6px;
    cursor: pointer;
}
#interfaceAdaptiveViewsContainer:hover {
    border-radius: 3px;
    background-color: #E5E7EB66;
}

.adaptiveViewHeader {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#interfaceAdaptiveViewsButton {
    float: left;
    max-width: 145px;
}

#interfaceAdaptiveViewsIconButton {
    display: none;
    float:left;
    width: 16px;
    background: url('../images/views-icon.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

#interfaceScaleContainer {
    line-height: 36px;
    padding: 0px 10px;
    cursor: pointer;
}

.scaleHeader {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#overflowMadeWith {
    line-height: 36px;
    margin: 0px 12px;
}

#axureLogo {
    display: inline-block;
    width: 56px;
    height: 36px;
    padding-top: 1px;
    background: url('../images/axure9_logo.svg') no-repeat center center, linear-gradient(transparent,transparent);
}

#overflowMenuContainer > #overflowMadeWith {
    line-height: 24px;
    border-top: 1px solid #ccc;
    margin-top: 7px;
    padding-top: 7px;
}

#interfaceControlFrameContainer {
    overflow: hidden;
    height: 40px;
    display: flex;
}

#interfaceControlFrameLeft {
    flex: 1;
    display: flex;
    font-size: 14px;
    line-height: 29px;
    color: #1e293b;
}

#interfaceControlFrameRight {
    flex: 1;
    display: flex;
    justify-content: flex-end;
}

#interfaceControlFrameLogoContainer {
    overflow: hidden;    
    margin-left: auto;
    margin-right: auto;
    height: 100%;
    display: flex;
}

#interfaceControlFrameLogoCaptionContainer {
    text-align: center;
    margin: 5px 10px 0px 10px;
    font-size: 14px;
    color: #4a4a4a;
    font-weight: bold;
    line-height: 30px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#previewNotice {
    font-size: 14px;
    line-height: 36px;
    display: none;
}

#scaleMenuButton {
    display: flex;
    align-items: center;    
    margin: 6px 0;
    padding: 0 6px;
}

#scaleMenuButton:hover {
    border-radius: 3px;
    background-color: #E5E7EB66;
    cursor: pointer;
}

#expandScaleArrow {    
    margin-left: 6px;
}

#logoImage {
    margin: 12px;
    max-width: 150px;
    max-height: 12px;
}

.emptyStateContainer {
    text-align: center;
    padding: 0px 15px;
    color: #64748B;
}

.emptyStateTitle {
    margin: 0px 0px 9px 0px;
    font-weight: bold;
}

.emptyStateContent {
    padding: 0px 10px;
    line-height: 22px;
}

.dottedDivider {
    height: 2px;
    margin: 15px 10px 15px 10px;
    border-bottom: 1px solid #E2E8F0;
    /*background: url('../images/divider.png') no-repeat center center;*/
    /*background: url('../images/divider.svg') no-repeat center center, linear-gradient(transparent,transparent);*/

}

.mobileMode .dottedDivider {
    display: none;
}

.nondottedDivider {
    height: 2px;
    margin: 9px 0px 9px 0px;
}

.lineDivider {
    height: 2px;
    margin: 10px 15px 20px 15px;
    border-bottom: solid 1px #E2E8F0;
    /*opacity: .5;*/
}

.pluginNameHeader {
    font-size: 14px;
    font-weight: 500;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    text-wrap: nowrap;
}

.mobileMode .pluginNameHeader {
    margin: 14px 12px 13px 12px;
    font-size: 18px;
    /*color: #6d6d6d;*/
}

#projectOptionsHost {
    display: flex;
    flex-direction: column;
}

#projectOptionsScrollContainer {
    overflow: auto;
    width: 100%;
    -webkit-overflow-scrolling: touch;
    flex: 1;
}

div.mobileSubHeader {
    font-size: 18px;
    font-weight: bold;
    color: #4a4a4a;
    margin: 0px 12px 11px 15px;
}

div.mobileText, span.mobileText {
    font-size: 16px;
    color: #4a4a4a;
}

.nativeMenuText {
    height: 20px;
    font-size: 14px;
    text-align: center;
    color: #ffffff;
    margin-top: 6px;
    margin-bottom: 13px;
    text-decoration: none;
}

#refreshText, #exitText {
    margin-top: 6px;
}

#returnText {
    margin-top: 7px;
}

#refreshIcon {
    height: 20px;
    object-fit: contain;
    background: url('../images/refresh.svg') no-repeat center center, linear-gradient(transparent,transparent);
    margin-top: 14px;
}

#exitIcon {
    height: 19px;
    object-fit: contain;
    background: url('../images/exit.svg') no-repeat center center, linear-gradient(transparent,transparent);
    margin-top: 14px;
}

#returnIcon {
    height: 46px;
    object-fit: contain;
    background: url('../images/return.svg') no-repeat center center, linear-gradient(transparent,transparent);
}


.nativePrototypeButton {
    width:50%;
    margin-left:auto;
    margin-right:auto;
}

.circleBackground {
    border-radius: 50%;
    behavior: url(PIE.htc);
    margin: auto;
    box-shadow: 3px 3px 3px 0 rgba(55, 55, 55, 0.5);
}

#returnBackground {
    width: 46px;
    height: 46px;
    background-color: #ffffff;
}

#closeBackground {
    width: 61px;
    height: 61px;
    background-color: #FCFDFF;
}

.closeIconSlash {
    width: 35px;
    height: 5px;
    background-color: #9b9b9b;
    position: relative;
    margin: auto;
}
#forwardSlash{
    top: 28px;
    transform: rotate(-45deg);
}
#backwardSlash{
    transform: rotate(90deg);
}

.mobilePrototypeControlFrame {
    position: absolute;
    display: none;
    width: 100%;
    min-width: 310px;
    bottom: 0px;
    z-index: 2;
    pointer-events: auto;
}

#nativeMenuBlueBackground {
    height: 72px;
    width: 100%;
    background-color: #008fe0;
}

#mHideSidebar {
    position: absolute;
    width: 10000px;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    top: 0px;
    z-index: 2;
    display: none;
    left: -60px;
}

.closeButtonContainerFb {
    position: absolute;
    right: 0;
    top: 0;
}

.closeButtonContainerFb button {
    font-size: 12px;
    padding: 12px;
    margin: 0px 6px;
    color: #718096;
    cursor: pointer;
    background: url('../images/close.svg') no-repeat center center;
    background-color: inherit;
    border: none;
    border-radius: 4px;
}

.closeButtonContainerFb button:hover {
    background-color: rgba(229, 231, 235, 0.4);
}

.closeButtonContainer {
    position: absolute;
    right: 11px;
    top: 10px;
}

.closeButtonContainer button {
    display: block;
    height: 8px;
    width: 8px;
    padding: 14px;
    margin: 4px;
    cursor: pointer;
    background: url('../images/close.svg') no-repeat center center;
    border: none;
    border-radius: 4px;
}

.closeButtonContainer button:hover {
    background-color: rgba(229, 231, 235, 0.4);
}

/*@media (max-width: 419px) {*/
    .mobileMode #topPanel {
        display: none;
    }

    .mobileMode #mainPanel {
        -webkit-overflow-scrolling: touch;
    }

    .mobileMode .leftPanel, .mobileMode .rightPanel, .mobileMode .mobileOnlyPanel {
        box-shadow: 0 5.5px 5px 0 rgba(0, 0, 0, 0.24), 0 9px 18px 0 rgba(0, 0, 0, 0.18);
        top: 45px;
        left: 100px;
    }

    .mobileMode .leftPanel, .mobileMode .rightPanel, .mobileMode .mobileOnlyPanel {
        float: left;
    }

    .mobileMode .rightPanel {
        margin-left: 13px;
    }

    .mobileMode #maximizePanelContainer {
        display: none;
    }
/*}*/

@media (max-width: 850px) {
    #overflowMenuContainer {
        right: 0px;
    }

    #overflowMadeWith, #publishContainer.preview, #separatorContainer, #separatorContainer.hasLeft {
        display: none;
    }

    #interfaceControlFrameLogoCaptionContainer {
        display: none;
    }

    #interfaceControlFrameContainer {
        justify-content: flex-end;
    }

    #interfaceAdaptiveViewsButton {
        display: none;
    }

    #interfaceAdaptiveViewsIconButton {
        display: block;
    }

    #interfaceAdaptiveViewsContainer {
        padding-left: 8px;
    }

    #scaleMenuContainer {
        right: 120px;
    }
}

@media (max-width: 700px) {
    #interfacePageNameContainer {
        display: none;
    }
}
