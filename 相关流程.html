﻿<!DOCTYPE html>
<html>
  <head>
    <title>相关流程</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/相关流程/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/相关流程/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (椭圆) -->
      <div id="u2304" class="ax_default flow_shape transition notrs">
        <svg data="images/相关流程/u2304.svg" id="u2304_img" class="img generatedImage">

  <defs>
    <pattern id="u2304_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u2304_img_cl58">
      <path d="M 0 25.5  C 0 11.219999999999999  21.999999999999996 0  50 0  C 78 0  100 11.219999999999999  100 25.5  C 100 39.78  78 51  50 51  C 21.999999999999996 51  0 39.78  0 25.5  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -185 -123 )">
    <path d="M 0 25.5  C 0 11.219999999999999  21.999999999999996 0  50 0  C 78 0  100 11.219999999999999  100 25.5  C 100 39.78  78 51  50 51  C 21.999999999999996 51  0 39.78  0 25.5  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 185 123 )" class="fill" />
    <path d="M 0 25.5  C 0 11.219999999999999  21.999999999999996 0  50 0  C 78 0  100 11.219999999999999  100 25.5  C 100 39.78  78 51  50 51  C 21.999999999999996 51  0 39.78  0 25.5  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 185 123 )" class="stroke" mask="url(#u2304_img_cl58)" />
  </g>
        </svg>
        <div id="u2304_text" class="text ">
          <p><span>开始</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2305" class="ax_default label transition notrs">
        <div id="u2305_div" class=""></div>
        <div id="u2305_text" class="text ">
          <p><span>辅导老师确认</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2306" class="ax_default flow_shape transition notrs">
        <div id="u2306_div" class=""></div>
        <div id="u2306_text" class="text ">
          <p><span>发布需求</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2307" class="ax_default flow_shape transition notrs">
        <div id="u2307_div" class=""></div>
        <div id="u2307_text" class="text ">
          <p><span>投递报名</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2308" class="ax_default flow_shape transition notrs">
        <div id="u2308_div" class=""></div>
        <div id="u2308_text" class="text ">
          <p><span>自己沟通</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2309" class="ax_default flow_shape transition notrs">
        <div id="u2309_div" class=""></div>
        <div id="u2309_text" class="text ">
          <p><span>邀请成为</span></p><p><span>辅导老师</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2310" class="ax_default flow_shape transition notrs">
        <div id="u2310_div" class=""></div>
        <div id="u2310_text" class="text ">
          <p><span>投递报名</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2311" class="ax_default flow_shape transition notrs">
        <div id="u2311_div" class=""></div>
        <div id="u2311_text" class="text ">
          <p><span>确认辅导老师</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
