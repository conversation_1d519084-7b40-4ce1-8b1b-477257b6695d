﻿<!DOCTYPE html>
<html>
  <head>
    <title>已完成</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/已完成/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/已完成/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1752" class="ax_default box_1 transition notrs">
        <div id="u1752_div" class=""></div>
        <div id="u1752_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1753" class="ax_default" data-left="16" data-top="136" data-width="436" data-height="137" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1754" class="ax_default" data-left="16" data-top="136" data-width="436" data-height="137" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1755" class="ax_default box_1 transition notrs">
            <div id="u1755_div" class=""></div>
            <div id="u1755_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1756" class="ax_default label transition notrs">
            <div id="u1756_div" class=""></div>
            <div id="u1756_text" class="text ">
              <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">&nbsp; 订单号：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">FD13455555333&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1757" class="ax_default" data-left="28" data-top="175" data-width="107" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1758" class="ax_default label transition notrs">
              <div id="u1758_div" class=""></div>
              <div id="u1758_text" class="text ">
                <p><span>辅导项目：</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1759" class="ax_default shape transition notrs">
              <div id="u1759_div" class=""></div>
              <div id="u1759_text" class="text ">
                <p><span>作业</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1760" class="ax_default" data-left="343" data-top="175" data-width="97" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1761" class="ax_default label transition notrs">
              <div id="u1761_div" class=""></div>
              <div id="u1761_text" class="text ">
                <p><span>总金额：2000元</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1762" class="ax_default" data-left="28" data-top="212" data-width="105" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1763" class="ax_default label transition notrs">
              <div id="u1763_div" class=""></div>
              <div id="u1763_text" class="text ">
                <p><span>交付阶段：已完成</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1764" class="ax_default" data-left="392" data-top="245" data-width="49" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1765" class="ax_default label transition notrs">
              <div id="u1765_div" class=""></div>
              <div id="u1765_text" class="text ">
                <p><span>4.5分</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1766" class="ax_default" data-left="361" data-top="210" data-width="79" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1767" class="ax_default label transition notrs">
              <div id="u1767_div" class=""></div>
              <div id="u1767_text" class="text ">
                <p><span>学生：张菲菲</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1768" class="ax_default" data-left="28" data-top="245" data-width="178" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1769" class="ax_default label transition notrs">
              <div id="u1769_div" class=""></div>
              <div id="u1769_text" class="text ">
                <p><span>完成时间：2025-4-12 12:11:11</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1770" class="ax_default label transition notrs">
        <div id="u1770_div" class=""></div>
        <div id="u1770_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1771" class="ax_default label transition notrs">
        <div id="u1771_div" class=""></div>
        <div id="u1771_text" class="text ">
          <p><span>我的订单</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1772" class="ax_default _图片 transition notrs">
        <img id="u1772_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1772_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u1773" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u1773_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u1773_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1774" class="ax_default box_1 transition notrs">
              <div id="u1774_div" class=""></div>
              <div id="u1774_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1775" class="ax_default label transition notrs">
              <div id="u1775_div" class=""></div>
              <div id="u1775_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1776" class="ax_default label transition notrs">
              <div id="u1776_div" class=""></div>
              <div id="u1776_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1777" class="ax_default button transition notrs">
              <div id="u1777_div" class=""></div>
              <div id="u1777_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1778" class="ax_default primary_button transition notrs">
              <div id="u1778_div" class=""></div>
              <div id="u1778_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u1773_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u1773_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1779" class="ax_default box_1 transition notrs">
              <div id="u1779_div" class=""></div>
              <div id="u1779_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1780" class="ax_default label transition notrs">
              <div id="u1780_div" class=""></div>
              <div id="u1780_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1781" class="ax_default label transition notrs">
              <div id="u1781_div" class=""></div>
              <div id="u1781_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1782" class="ax_default button transition notrs">
              <div id="u1782_div" class=""></div>
              <div id="u1782_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1783" class="ax_default primary_button transition notrs">
              <div id="u1783_div" class=""></div>
              <div id="u1783_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1784" class="ax_default label transition notrs">
              <div id="u1784_div" class=""></div>
              <div id="u1784_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u1785" class="ax_default text_field transition notrs">
              <div id="u1785_div" class=""></div>
              <input id="u1785_input" type="text" value="" class="u1785_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1786" class="ax_default label transition notrs">
              <div id="u1786_div" class=""></div>
              <div id="u1786_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1787" class="ax_default line transition notrs">
        <svg data="images/订单列表/u251.svg" id="u1787_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -43.5 -110.5 )">
    <path d="M 0 1.5  L 54 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 45 112 )" class="stroke" />
  </g>
        </svg>
        <div id="u1787_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1788" class="ax_default label transition notrs">
        <div id="u1788_div" class=""></div>
        <div id="u1788_text" class="text ">
          <p><span>投递中（3）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1789" class="ax_default label transition notrs">
        <div id="u1789_div" class=""></div>
        <div id="u1789_text" class="text ">
          <p><span>交付中（1）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1790" class="ax_default label transition notrs">
        <div id="u1790_div" class=""></div>
        <div id="u1790_text" class="text ">
          <p><span>已完成（2）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1791" class="ax_default label transition notrs">
        <div id="u1791_div" class=""></div>
        <div id="u1791_text" class="text ">
          <p><span>已拒绝（5）</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1792" class="ax_default" data-left="16" data-top="286" data-width="436" data-height="137" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1793" class="ax_default" data-left="16" data-top="286" data-width="436" data-height="137" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u1794" class="ax_default box_1 transition notrs">
            <div id="u1794_div" class=""></div>
            <div id="u1794_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1795" class="ax_default label transition notrs">
            <div id="u1795_div" class=""></div>
            <div id="u1795_text" class="text ">
              <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">&nbsp; 订单号：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">FD13455555333&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1796" class="ax_default" data-left="28" data-top="325" data-width="107" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1797" class="ax_default label transition notrs">
              <div id="u1797_div" class=""></div>
              <div id="u1797_text" class="text ">
                <p><span>辅导项目：</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1798" class="ax_default shape transition notrs">
              <div id="u1798_div" class=""></div>
              <div id="u1798_text" class="text ">
                <p><span>作业</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1799" class="ax_default" data-left="343" data-top="325" data-width="97" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1800" class="ax_default label transition notrs">
              <div id="u1800_div" class=""></div>
              <div id="u1800_text" class="text ">
                <p><span>总金额：2000元</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1801" class="ax_default" data-left="28" data-top="362" data-width="105" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1802" class="ax_default label transition notrs">
              <div id="u1802_div" class=""></div>
              <div id="u1802_text" class="text ">
                <p><span>交付阶段：已完成</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1803" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1804" class="ax_default" data-left="361" data-top="360" data-width="79" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1805" class="ax_default label transition notrs">
              <div id="u1805_div" class=""></div>
              <div id="u1805_text" class="text ">
                <p><span>学生：张菲菲</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u1806" class="ax_default" data-left="28" data-top="395" data-width="178" data-height="20" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u1807" class="ax_default label transition notrs">
              <div id="u1807_div" class=""></div>
              <div id="u1807_text" class="text ">
                <p><span>完成时间：2025-4-12 12:11:11</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (热区) -->
      <div id="u1808" class="ax_default">
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1809" class="ax_default sticky_1 transition notrs">
        <div id="u1809_div" class=""></div>
        <div id="u1809_text" class="text ">
          <p><span>有评价的显示总体评价分</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1810" class="ax_default sticky_1 transition notrs">
        <div id="u1810_div" class=""></div>
        <div id="u1810_text" class="text ">
          <p><span>排序：按照完成时间倒序</span></p><p><span>分页：每页10个</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
