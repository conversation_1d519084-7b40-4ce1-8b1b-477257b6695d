﻿<!DOCTYPE html>
<html>
  <head>
    <title>我的钱包</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/我的钱包/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/我的钱包/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u2015" class="ax_default box_1 transition notrs">
        <div id="u2015_div" class=""></div>
        <div id="u2015_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2016" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2017" class="ax_default label transition notrs">
        <div id="u2017_div" class=""></div>
        <div id="u2017_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2018" class="ax_default label transition notrs">
        <div id="u2018_div" class=""></div>
        <div id="u2018_text" class="text ">
          <p><span>我的钱包</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2019" class="ax_default _图片 transition notrs">
        <img id="u2019_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u2019_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u2020" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u2020_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u2020_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2021" class="ax_default box_1 transition notrs">
              <div id="u2021_div" class=""></div>
              <div id="u2021_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2022" class="ax_default label transition notrs">
              <div id="u2022_div" class=""></div>
              <div id="u2022_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2023" class="ax_default label transition notrs">
              <div id="u2023_div" class=""></div>
              <div id="u2023_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2024" class="ax_default button transition notrs">
              <div id="u2024_div" class=""></div>
              <div id="u2024_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2025" class="ax_default primary_button transition notrs">
              <div id="u2025_div" class=""></div>
              <div id="u2025_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u2020_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u2020_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2026" class="ax_default box_1 transition notrs">
              <div id="u2026_div" class=""></div>
              <div id="u2026_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2027" class="ax_default label transition notrs">
              <div id="u2027_div" class=""></div>
              <div id="u2027_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2028" class="ax_default label transition notrs">
              <div id="u2028_div" class=""></div>
              <div id="u2028_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2029" class="ax_default button transition notrs">
              <div id="u2029_div" class=""></div>
              <div id="u2029_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2030" class="ax_default primary_button transition notrs">
              <div id="u2030_div" class=""></div>
              <div id="u2030_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2031" class="ax_default label transition notrs">
              <div id="u2031_div" class=""></div>
              <div id="u2031_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2032" class="ax_default text_field transition notrs">
              <div id="u2032_div" class=""></div>
              <input id="u2032_input" type="text" value="" class="u2032_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2033" class="ax_default label transition notrs">
              <div id="u2033_div" class=""></div>
              <div id="u2033_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2034" class="ax_default box_1 transition notrs">
        <div id="u2034_div" class=""></div>
        <div id="u2034_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2035" class="ax_default label transition notrs">
        <div id="u2035_div" class=""></div>
        <div id="u2035_text" class="text ">
          <p><span>总额：10.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2036" class="ax_default label transition notrs">
        <div id="u2036_div" class=""></div>
        <div id="u2036_text" class="text ">
          <p><span>已提现：9.000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2037" class="ax_default label transition notrs">
        <div id="u2037_div" class=""></div>
        <div id="u2037_text" class="text ">
          <p><span>未提现：1.000</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u2038" class="ax_default line transition notrs">
        <svg data="images/我的钱包/u2038.svg" id="u2038_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -40 -163 )">
    <path d="M 0 0.5  L 399 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0.12941176470588237)" fill="none" transform="matrix(1 0 0 1 40 163 )" class="stroke" />
  </g>
        </svg>
        <div id="u2038_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2039" class="ax_default label transition notrs">
        <div id="u2039_div" class=""></div>
        <div id="u2039_text" class="text ">
          <p><span>提现记录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2040" class="ax_default box_1 transition notrs">
        <div id="u2040_div" class=""></div>
        <div id="u2040_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2041" class="ax_default label transition notrs">
        <div id="u2041_div" class=""></div>
        <div id="u2041_text" class="text ">
          <p><span>申请时间：2025-11-11 12:12:12</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2042" class="ax_default label transition notrs">
        <div id="u2042_div" class=""></div>
        <div id="u2042_text" class="text ">
          <p><span>提现金额：500元</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2043" class="ax_default label transition notrs">
        <div id="u2043_div" class=""></div>
        <div id="u2043_text" class="text ">
          <p><span>提现方式：微信</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2044" class="ax_default label transition notrs">
        <div id="u2044_div" class=""></div>
        <div id="u2044_text" class="text ">
          <p><span>提现中</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2045" class="ax_default box_1 transition notrs">
        <div id="u2045_div" class=""></div>
        <div id="u2045_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2046" class="ax_default label transition notrs">
        <div id="u2046_div" class=""></div>
        <div id="u2046_text" class="text ">
          <p><span>申请时间：2025-11-11 12:12:12</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2047" class="ax_default label transition notrs">
        <div id="u2047_div" class=""></div>
        <div id="u2047_text" class="text ">
          <p><span>提现金额：500元</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2048" class="ax_default label transition notrs">
        <div id="u2048_div" class=""></div>
        <div id="u2048_text" class="text ">
          <p><span>提现方式：微信</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2049" class="ax_default label transition notrs">
        <div id="u2049_div" class=""></div>
        <div id="u2049_text" class="text ">
          <p><span>已打款</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2050" class="ax_default label transition notrs">
        <div id="u2050_div" class=""></div>
        <div id="u2050_text" class="text ">
          <p><span>去提现&gt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2051" class="ax_default sticky_1 transition notrs">
        <div id="u2051_div" class=""></div>
        <div id="u2051_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">总额</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">=</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">已提现+未提现</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">已提现：已打款的金额累加</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">未提现：包括提现中、未提现申请的金额</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;"><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2052" class="ax_default label transition notrs">
        <div id="u2052_div" class=""></div>
        <div id="u2052_text" class="text ">
          <p><span>打款时间：2025-11-12 12:12:12</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2053" class="ax_default label transition notrs">
        <div id="u2053_div" class=""></div>
        <div id="u2053_text" class="text ">
          <p><span>实际到账：450元</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2054" class="ax_default label transition notrs">
        <div id="u2054_div" class=""></div>
        <div id="u2054_text" class="text ">
          <p><span>实际到账：450元</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
