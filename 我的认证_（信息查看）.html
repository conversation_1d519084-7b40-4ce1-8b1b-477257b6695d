﻿<!DOCTYPE html>
<html>
  <head>
    <title>我的认证 （信息查看）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/我的认证_（信息查看）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/我的认证_（信息查看）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u2232" class="ax_default box_1 transition notrs">
        <div id="u2232_div" class=""></div>
        <div id="u2232_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2233" class="ax_default label transition notrs">
        <div id="u2233_div" class=""></div>
        <div id="u2233_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2234" class="ax_default label transition notrs">
        <div id="u2234_div" class=""></div>
        <div id="u2234_text" class="text ">
          <p><span>我的信息</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2235" class="ax_default _图片 transition notrs">
        <img id="u2235_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u2235_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2236" class="ax_default" data-left="35" data-top="140" data-width="410" data-height="35" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2237" class="ax_default label transition notrs">
          <div id="u2237_div" class=""></div>
          <div id="u2237_text" class="text ">
            <p><span>头像</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u2238" class="ax_default line transition notrs">
          <svg data="images/我的认证（表单填写）/u2149.svg" id="u2238_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
          </svg>
          <div id="u2238_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2239" class="ax_default primary_button transition notrs">
        <div id="u2239_div" class=""></div>
        <div id="u2239_text" class="text ">
          <p><span>修改个人信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2240" class="ax_default label transition notrs">
        <div id="u2240_div" class=""></div>
        <div id="u2240_text" class="text ">
          <p><span>您的姓名</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u2241" class="ax_default line transition notrs">
        <svg data="images/我的认证（表单填写）/u2149.svg" id="u2241_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
        </svg>
        <div id="u2241_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2242" class="ax_default text_field transition notrs">
        <div id="u2242_div" class=""></div>
        <input id="u2242_input" type="text" value="张伟伟" class="u2242_input"/>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2243" class="ax_default _图片 transition notrs">
        <img id="u2243_img" class="img " src="images/我的认证（表单填写）/u2156.png"/>
        <div id="u2243_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2244" class="ax_default _图片 transition notrs">
        <img id="u2244_img" class="img " src="images/我的认证（表单填写）/u2157.png"/>
        <div id="u2244_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2245" class="ax_default" data-left="35" data-top="295" data-width="411" data-height="27" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2246" class="ax_default label transition notrs">
          <div id="u2246_div" class=""></div>
          <div id="u2246_text" class="text ">
            <p><span>微信号</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u2247" class="ax_default line transition notrs">
          <svg data="images/我的认证（表单填写）/u2149.svg" id="u2247_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
          </svg>
          <div id="u2247_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2248" class="ax_default label transition notrs">
          <div id="u2248_div" class=""></div>
          <div id="u2248_text" class="text ">
            <p><span>11111111111111111111111</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2249" class="ax_default" data-left="35" data-top="335" data-width="410" data-height="30" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u2250" class="ax_default" data-left="35" data-top="338" data-width="410" data-height="27" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2251" class="ax_default label transition notrs">
            <div id="u2251_div" class=""></div>
            <div id="u2251_text" class="text ">
              <p><span>最高学历学校</span></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u2252" class="ax_default line transition notrs">
            <svg data="images/我的认证（表单填写）/u2149.svg" id="u2252_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
            </svg>
            <div id="u2252_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u2253" class="ax_default text_field transition notrs">
          <div id="u2253_div" class=""></div>
          <input id="u2253_input" type="text" value="北京航空航天大学" class="u2253_input"/>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2254" class="ax_default" data-left="35" data-top="249" data-width="410" data-height="30" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u2255" class="ax_default" data-left="35" data-top="252" data-width="410" data-height="27" layer-opacity="1">

          <!-- Unnamed (矩形) -->
          <div id="u2256" class="ax_default label transition notrs">
            <div id="u2256_div" class=""></div>
            <div id="u2256_text" class="text ">
              <p><span>手机号</span></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u2257" class="ax_default line transition notrs">
            <svg data="images/我的认证（表单填写）/u2149.svg" id="u2257_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
            </svg>
            <div id="u2257_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u2258" class="ax_default text_field transition notrs">
          <div id="u2258_div" class=""></div>
          <input id="u2258_input" type="text" value="17812345678" class="u2258_input"/>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2259" class="ax_default" data-left="35" data-top="383" data-width="410" data-height="27" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u2260" class="ax_default" data-left="35" data-top="383" data-width="410" data-height="27" layer-opacity="1">

          <!-- Unnamed (组合) -->
          <div id="u2261" class="ax_default" data-left="35" data-top="383" data-width="410" data-height="27" layer-opacity="1">

            <!-- Unnamed (矩形) -->
            <div id="u2262" class="ax_default label transition notrs">
              <div id="u2262_div" class=""></div>
              <div id="u2262_text" class="text ">
                <p><span>目前在读（职）</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u2263" class="ax_default line transition notrs">
              <svg data="images/我的认证（表单填写）/u2149.svg" id="u2263_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
              </svg>
              <div id="u2263_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2264" class="ax_default shape transition notrs">
          <div id="u2264_div" class=""></div>
          <div id="u2264_text" class="text ">
            <p><span>博士</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2265" class="ax_default label transition notrs">
        <div id="u2265_div" class=""></div>
        <div id="u2265_text" class="text ">
          <p><span>专业或研究方向</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u2266" class="ax_default line transition notrs">
        <svg data="images/我的认证（表单填写）/u2149.svg" id="u2266_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
        </svg>
        <div id="u2266_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2267" class="ax_default text_field transition notrs">
        <div id="u2267_div" class=""></div>
        <input id="u2267_input" type="text" value="计算机科学与工程" class="u2267_input"/>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2268" class="ax_default" data-left="35" data-top="614" data-width="410" data-height="81" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2269" class="ax_default label transition notrs">
          <div id="u2269_div" class=""></div>
          <div id="u2269_text" class="text ">
            <p><span>科研经历</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2270" class="ax_default label transition notrs">
          <div id="u2270_div" class=""></div>
          <div id="u2270_text" class="text ">
            <p><span>（请保证经历的真实性，一旦发现虚假信息，责任自担）</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2271" class="ax_default label transition notrs">
          <div id="u2271_div" class=""></div>
          <div id="u2271_text" class="text ">
            <p><span>2022-2024年，独作发表过3篇会计方向的SC。</span></p><p><span>2022-2025年，以通讯作者发表2篇公司治理的SSCI。</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u2272" class="ax_default line transition notrs">
          <svg data="images/我的认证（表单填写）/u2149.svg" id="u2272_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
          </svg>
          <div id="u2272_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2273" class="ax_default label transition notrs">
        <div id="u2273_div" class=""></div>
        <div id="u2273_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2274" class="ax_default" data-left="35" data-top="709" data-width="414" data-height="147" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2275" class="ax_default label transition notrs">
          <div id="u2275_div" class=""></div>
          <div id="u2275_text" class="text ">
            <p><span>个人自述</span></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u2276" class="ax_default line transition notrs">
          <svg data="images/我的认证（表单填写）/u2149.svg" id="u2276_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -35 -174 )">
    <path d="M 0 0.5  L 410 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 35 174 )" class="stroke" />
  </g>
          </svg>
          <div id="u2276_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2277" class="ax_default label transition notrs">
          <div id="u2277_div" class=""></div>
          <div id="u2277_text" class="text ">
            <p><span>平时爱好科研，喜欢和专业的人士交朋友。喜欢钻研科研喜欢钻研科研</span></p><p><span>喜欢钻研科研</span></p><p><span>喜欢钻研科研</span></p><p><span>喜欢钻研科研</span></p><p><span>喜欢钻研科研。</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2278" class="ax_default label transition notrs">
        <div id="u2278_div" class=""></div>
        <div id="u2278_text" class="text ">
          <p><span>个人证件</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2279" class="ax_default label transition notrs">
        <div id="u2279_div" class=""></div>
        <div id="u2279_text" class="text ">
          <p><span>（个人证件仅为平台审核使用，不会对外展示）</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2280" class="ax_default sticky_4 transition notrs">
        <div id="u2280_div" class=""></div>
        <div id="u2280_text" class="text ">
          <p><span>后期需考虑个人信息更新的问题，第一版先不允许修改。</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2281" class="ax_default _图片 transition notrs">
        <img id="u2281_img" class="img " src="images/首页（学生端）/u21.png"/>
        <div id="u2281_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2282" class="ax_default _图片 transition notrs">
        <img id="u2282_img" class="img " src="images/我的认证_（信息查看）/u2282.png"/>
        <div id="u2282_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2283" class="ax_default _图片 transition notrs">
        <img id="u2283_img" class="img " src="images/我的认证_（信息查看）/u2282.png"/>
        <div id="u2283_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u2284" class="ax_default _图片 transition notrs">
        <img id="u2284_img" class="img " src="images/我的认证_（信息查看）/u2282.png"/>
        <div id="u2284_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2285" class="ax_default sticky_1 transition notrs">
        <div id="u2285_div" class=""></div>
        <div id="u2285_text" class="text ">
          <p><span>头像可以随时修改</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2286" class="ax_default sticky_4 transition notrs">
        <div id="u2286_div" class=""></div>
        <div id="u2286_text" class="text ">
          <p><span>审核通过之后，1个月只能修改1次</span></p><p><span>不能修改时按钮置灰，可修改时才可点击</span></p><p><span>审核中不允许修改，按钮置灰</span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2287" class="ax_default primary_button transition notrs">
        <svg data="images/我的认证_（信息查看）/u2287.svg" id="u2287_img" class="img generatedImage">

  <defs>
    <pattern id="u2287_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u2287_img_cl56">
      <path d="M 0 19.058823529411764  L 0 7.9411764705882355  C 0 3.4941176470588284  2.1087804878049217 0  4.7926829268292686 0  L 388.2073170731707 0  C 390.89121951219505 0  393 3.4941176470588284  393 7.9411764705882355  L 393 19.058823529411764  C 393 23.50588235294117  390.89121951219505 27  388.2073170731707 27  L 209.7278048780516 27  L 4.7926829268292686 27  C 2.1087804878049217 27  0 23.50588235294117  0 19.058823529411764  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -41 -72 )">
    <path d="M 0 19.058823529411764  L 0 7.9411764705882355  C 0 3.4941176470588284  2.1087804878049217 0  4.7926829268292686 0  L 388.2073170731707 0  C 390.89121951219505 0  393 3.4941176470588284  393 7.9411764705882355  L 393 19.058823529411764  C 393 23.50588235294117  390.89121951219505 27  388.2073170731707 27  L 209.7278048780516 27  L 4.7926829268292686 27  C 2.1087804878049217 27  0 23.50588235294117  0 19.058823529411764  Z " fill-rule="nonzero" fill="rgba(215, 215, 215, 1)" stroke="none" transform="matrix(1 0 0 1 41 72 )" class="fill" />
    <path d="M 0 19.058823529411764  L 0 7.9411764705882355  C 0 3.4941176470588284  2.1087804878049217 0  4.7926829268292686 0  L 388.2073170731707 0  C 390.89121951219505 0  393 3.4941176470588284  393 7.9411764705882355  L 393 19.058823529411764  C 393 23.50588235294117  390.89121951219505 27  388.2073170731707 27  L 209.7278048780516 27  L 4.7926829268292686 27  C 2.1087804878049217 27  0 23.50588235294117  0 19.058823529411764  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 41 72 )" class="stroke" mask="url(#u2287_img_cl56)" />
  </g>
        </svg>
        <div id="u2287_text" class="text ">
          <p><span>审核通过，认证成功。</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2288" class="ax_default primary_button transition notrs">
        <svg data="images/我的认证_（信息查看）/u2287.svg" id="u2288_img" class="img generatedImage">

  <defs>
    <pattern id="u2288_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u2288_img_cl56">
      <path d="M 0 19.058823529411764  L 0 7.9411764705882355  C 0 3.4941176470588284  2.1087804878049217 0  4.7926829268292686 0  L 388.2073170731707 0  C 390.89121951219505 0  393 3.4941176470588284  393 7.9411764705882355  L 393 19.058823529411764  C 393 23.50588235294117  390.89121951219505 27  388.2073170731707 27  L 209.7278048780516 27  L 4.7926829268292686 27  C 2.1087804878049217 27  0 23.50588235294117  0 19.058823529411764  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -41 -72 )">
    <path d="M 0 19.058823529411764  L 0 7.9411764705882355  C 0 3.4941176470588284  2.1087804878049217 0  4.7926829268292686 0  L 388.2073170731707 0  C 390.89121951219505 0  393 3.4941176470588284  393 7.9411764705882355  L 393 19.058823529411764  C 393 23.50588235294117  390.89121951219505 27  388.2073170731707 27  L 209.7278048780516 27  L 4.7926829268292686 27  C 2.1087804878049217 27  0 23.50588235294117  0 19.058823529411764  Z " fill-rule="nonzero" fill="rgba(215, 215, 215, 1)" stroke="none" transform="matrix(1 0 0 1 41 72 )" class="fill" />
    <path d="M 0 19.058823529411764  L 0 7.9411764705882355  C 0 3.4941176470588284  2.1087804878049217 0  4.7926829268292686 0  L 388.2073170731707 0  C 390.89121951219505 0  393 3.4941176470588284  393 7.9411764705882355  L 393 19.058823529411764  C 393 23.50588235294117  390.89121951219505 27  388.2073170731707 27  L 209.7278048780516 27  L 4.7926829268292686 27  C 2.1087804878049217 27  0 23.50588235294117  0 19.058823529411764  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 41 72 )" class="stroke" mask="url(#u2288_img_cl56)" />
  </g>
        </svg>
        <div id="u2288_text" class="text ">
          <p><span>审核失败，原因：1、手机号不对；2、学校名字不存在。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2289" class="ax_default sticky_1 transition notrs">
        <div id="u2289_div" class=""></div>
        <div id="u2289_text" class="text ">
          <p><span>前期先人工审核，一个身份证只能有一个有效的账号</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2290" class="ax_default" data-left="35" data-top="477" data-width="410" data-height="108" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u2291" class="ax_default label transition notrs">
          <div id="u2291_div" class=""></div>
          <div id="u2291_text" class="text ">
            <p><span>擅长辅导</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2292" class="ax_default box_1 transition notrs">
          <div id="u2292_div" class=""></div>
          <div id="u2292_text" class="text ">
            <p><span>国内论文</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2293" class="ax_default shape transition notrs">
          <div id="u2293_div" class=""></div>
          <div id="u2293_text" class="text ">
            <p><span>专业课</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2294" class="ax_default box_1 transition notrs">
          <div id="u2294_div" class=""></div>
          <div id="u2294_text" class="text ">
            <p><span>保研</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2295" class="ax_default box_1 transition notrs">
          <div id="u2295_div" class=""></div>
          <div id="u2295_text" class="text ">
            <p><span>考研</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2296" class="ax_default box_1 transition notrs">
          <div id="u2296_div" class=""></div>
          <div id="u2296_text" class="text ">
            <p><span>留学论文</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2297" class="ax_default shape transition notrs">
          <div id="u2297_div" class=""></div>
          <div id="u2297_text" class="text ">
            <p><span>留学申请</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2298" class="ax_default box_1 transition notrs">
          <div id="u2298_div" class=""></div>
          <div id="u2298_text" class="text ">
            <p><span>申博</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2299" class="ax_default box_1 transition notrs">
          <div id="u2299_div" class=""></div>
          <div id="u2299_text" class="text ">
            <p><span>竞赛</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2300" class="ax_default label transition notrs">
          <div id="u2300_div" class=""></div>
          <div id="u2300_text" class="text ">
            <p><span>（最多选4个）</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2301" class="ax_default label transition notrs">
        <div id="u2301_div" class=""></div>
        <div id="u2301_text" class="text ">
          <p><span>个人信息1个月只能修改1次</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2302" class="ax_default primary_button transition notrs">
        <svg data="images/我的认证_（信息查看）/u2302.svg" id="u2302_img" class="img generatedImage">

  <defs>
    <pattern id="u2302_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u2302_img_cl57">
      <path d="M 0 19.058823529411764  L 0 7.9411764705882355  C 0 3.4941176470588284  1.3146341463414906 0  2.9878048780487805 0  L 242.0121951219512 0  C 243.6853658536585 0  245 3.4941176470588284  245 7.9411764705882355  L 245 19.058823529411764  C 245 23.50588235294117  243.6853658536585 27  242.0121951219512 27  L 130.74634146341637 27  L 2.9878048780487805 27  C 1.3146341463414906 27  0 23.50588235294117  0 19.058823529411764  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -915 -72 )">
    <path d="M 0 19.058823529411764  L 0 7.9411764705882355  C 0 3.4941176470588284  1.3146341463414906 0  2.9878048780487805 0  L 242.0121951219512 0  C 243.6853658536585 0  245 3.4941176470588284  245 7.9411764705882355  L 245 19.058823529411764  C 245 23.50588235294117  243.6853658536585 27  242.0121951219512 27  L 130.74634146341637 27  L 2.9878048780487805 27  C 1.3146341463414906 27  0 23.50588235294117  0 19.058823529411764  Z " fill-rule="nonzero" fill="rgba(215, 215, 215, 1)" stroke="none" transform="matrix(1 0 0 1 915 72 )" class="fill" />
    <path d="M 0 19.058823529411764  L 0 7.9411764705882355  C 0 3.4941176470588284  1.3146341463414906 0  2.9878048780487805 0  L 242.0121951219512 0  C 243.6853658536585 0  245 3.4941176470588284  245 7.9411764705882355  L 245 19.058823529411764  C 245 23.50588235294117  243.6853658536585 27  242.0121951219512 27  L 130.74634146341637 27  L 2.9878048780487805 27  C 1.3146341463414906 27  0 23.50588235294117  0 19.058823529411764  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 915 72 )" class="stroke" mask="url(#u2302_img_cl57)" />
  </g>
        </svg>
        <div id="u2302_text" class="text ">
          <p><span>信息审核中</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2303" class="ax_default sticky_1 transition notrs">
        <div id="u2303_div" class=""></div>
        <div id="u2303_text" class="text ">
          <p><span>审核中时显示：信息审核中</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
