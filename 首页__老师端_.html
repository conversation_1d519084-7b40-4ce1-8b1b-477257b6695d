﻿<!DOCTYPE html>
<html>
  <head>
    <title>首页 (老师端)</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/首页__老师端_/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/首页__老师端_/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u1230" class="ax_default box_1 transition notrs">
        <div id="u1230_div" class=""></div>
        <div id="u1230_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u1231" class="ax_default icon transition notrs">
        <svg data="images/首页（学生端）/u1.svg" id="u1231_img" class="img generatedImage">

  <defs>
    <pattern id="u1231_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u1231_img_cl44">
      <path d="M 21.081473214285715 14.572823660714285  C 21.207217261904763 14.589006696428571  21.308779761904763 14.656436011904761  21.386160714285715 14.77511160714286  L 21.952008928571427 15.600446428571429  C 22.029389880952383 15.719122023809524  22.060825892857142 15.845889136904761  22.046316964285715 15.98074776785714  C 22.031808035714285 16.115606398809522  21.976190476190474 16.226190476190478  21.879464285714285 16.3125  C 21.463541666666668 16.679315476190478  20.851748511904763 17.210658482142858  20.044084821428573 17.906529017857142  C 19.236421130952383 18.60239955357143  18.527901785714285 19.21196056547619  17.918526785714285 19.73521205357143  C 17.309151785714285 20.258463541666664  16.985119047619047 20.53627232142857  16.946428571428573 20.568638392857142  C 16.569196428571427 20.913876488095234  16.279017857142858 21.17280505952381  16.075892857142858 21.345424107142858  C 15.872767857142856 21.5180431547619  15.580171130952383 21.73921130952381  15.198102678571429 22.00892857142857  C 14.816034226190476 22.278645833333336  14.446056547619047 22.47553943452381  14.088169642857144 22.599609375  C 13.730282738095239 22.72367931547619  13.372395833333336 22.785714285714285  13.014508928571429 22.785714285714285  L 13 22.785714285714285  L 12.985491071428571 22.785714285714285  C 12.62760416666667 22.785714285714285  12.269717261904763 22.72367931547619  11.911830357142858 22.599609375  C 11.553943452380954 22.47553943452381  11.186383928571429 22.278645833333336  10.809151785714286 22.00892857142857  C 10.431919642857142 21.73921130952381  10.136904761904763 21.515345982142858  9.924107142857142 21.337332589285715  C 9.711309523809524 21.15931919642857  9.421130952380954 20.903087797619047  9.053571428571429 20.568638392857142  C 9.005208333333334 20.52548363095238  8.690848214285714 20.25306919642857  8.110491071428571 19.751395089285715  C 7.530133928571428 19.249720982142858  6.836123511904762 18.650948660714285  6.028459821428571 17.955078125  C 5.220796130952381 17.259207589285715  4.628348214285714 16.749441964285715  4.251116071428571 16.42578125  C 4.144717261904762 16.339471726190478  4.084263392857143 16.228887648809522  4.069754464285714 16.094029017857142  C 4.055245535714286 15.959170386904761  4.0866815476190474 15.832403273809524  4.1640625 15.713727678571429  L 4.700892857142857 14.872209821428571  C 4.77827380952381 14.753534226190476  4.882254464285714 14.683407738095235  5.012834821428571 14.66183035714286  C 5.143415178571429 14.640252976190476  5.261904761904762 14.67801339285714  5.368303571428571 14.77511160714286  C 6.277529761904762 15.562686011904761  7.757440476190477 16.835751488095234  9.808035714285714 18.594308035714285  C 9.85639880952381 18.637462797619047  10.066778273809524 18.82626488095238  10.439174107142858 19.160714285714285  C 10.811569940476192 19.49516369047619  11.104166666666668 19.746000744047617  11.316964285714286 19.91322544642857  C 11.529761904761905 20.080450148809522  11.803013392857142 20.255766369047617  12.13671875 20.439174107142858  C 12.470424107142858 20.622581845238095  12.753348214285714 20.714285714285715  12.985491071428571 20.714285714285715  L 13 20.714285714285715  L 13.014508928571429 20.714285714285715  C 13.246651785714288 20.714285714285715  13.529575892857144 20.622581845238095  13.86328125 20.439174107142858  C 14.19698660714286 20.255766369047617  14.470238095238098 20.080450148809522  14.683035714285715 19.91322544642857  C 14.895833333333336 19.746000744047617  15.188430059523812 19.49516369047619  15.560825892857144 19.160714285714285  C 15.933221726190476 18.82626488095238  16.14360119047619 18.637462797619047  16.191964285714285 18.594308035714285  C 18.6875 16.45814732142857  20.201264880952383 15.152715773809524  20.733258928571427 14.67801339285714  C 20.83965773809524 14.591703869047617  20.955729166666668 14.556640625  21.081473214285715 14.572823660714285  Z M 24.005022321428573 26.774832589285715  C 24.096912202380956 26.672340029761905  24.142857142857142 26.550967261904763  24 26.410714285714285  L 24 11.392857142857144  C 23.272321428571427 10.497395833333332  22.503348214285715 9.747581845238095  21.8359375 9.143415178571429  C 20.955729166666668 8.345052083333332  19.074404761904763 6.70517113095238  16.191964285714285 4.22377232142857  C 16.162946428571427 4.202194940476188  15.95498511904762 4.013392857142856  15.568080357142856 3.6573660714285703  C 15.181175595238098 3.301339285714285  14.886160714285715 3.042410714285715  14.683035714285715 2.880580357142856  C 14.479910714285715 2.71875  14.20907738095238 2.543433779761903  13.870535714285715 2.3546316964285703  C 13.531994047619047 2.165829613095238  13.246651785714288 2.0714285714285703  13.014508928571429 2.0714285714285703  L 13 2.0714285714285703  L 12.985491071428571 2.0714285714285703  C 12.753348214285714 2.0714285714285703  12.468005952380954 2.165829613095238  12.129464285714286 2.3546316964285703  C 11.79092261904762 2.543433779761903  11.520089285714288 2.71875  11.316964285714286 2.880580357142856  C 11.113839285714286 3.042410714285715  10.818824404761905 3.301339285714285  10.431919642857142 3.6573660714285703  C 10.045014880952381 4.013392857142856  9.837053571428571 4.202194940476188  9.808035714285714 4.22377232142857  C 7.7284226190476195 6.014694940476188  6.202566964285714 7.33900669642857  5.23046875 8.196707589285715  C 4.258370535714286 9.054408482142856  3.632068452380953 9.61542038690476  3.3515625 9.879743303571429  C 3.071056547619048 10.144066220238095  2.674479166666667 10.545944940476188  2.161830357142857 11.085379464285715  C 2.0264136904761907 11.21484375  1.924851190476191 11.317336309523808  2 11.392857142857144  L 2 26.410714285714285  C 1.857142857142857 26.550967261904763  1.903087797619048 26.672340029761905  1.9949776785714286 26.774832589285715  C 2.0868675595238093 26.877325148809522  2.1956845238095237 26.92857142857143  2.3214285714285716 27  L 23.678571428571427 27  C 23.804315476190474 26.92857142857143  23.91313244047619 26.877325148809522  24.005022321428573 26.774832589285715  Z M 25.405133928571427 9.871651785714285  C 25.80171130952381 10.281622023809524  26 10.788690476190473  26 11.392857142857144  L 26 26.410714285714285  C 26 27.122767857142854  25.772693452380956 27.73232886904762  25.318080357142858 28.23939732142857  C 24.863467261904766 28.746465773809522  24.316964285714292 29  23.678571428571427 29  L 2.3214285714285716 29  C 1.6830357142857144 29  1.1365327380952381 28.746465773809522  0.6819196428571429 28.23939732142857  C 0.22730654761904762 27.73232886904762  0 27.122767857142854  0 26.410714285714285  L 0 11.392857142857144  C 0 10.788690476190473  0.19828869047619047 10.281622023809524  0.5948660714285714 9.871651785714285  C 1.7845982142857144 8.641741071428571  3.477306547619047 7.074683779761902  5.672991071428571 5.170479910714286  C 7.868675595238096 3.2662760416666643  8.995535714285714 2.287202380952379  9.053571428571429 2.2332589285714297  C 9.401785714285714 1.9095982142857149  9.687127976190478 1.653366815476188  9.909598214285714 1.4645647321428559  C 10.132068452380954 1.2757626488095235  10.429501488095237 1.0492001488095235  10.801897321428571 0.7848772321428558  C 11.174293154761905 0.520554315476188  11.541852678571429 0.32366071428571475  11.904575892857142 0.1941964285714295  C 12.26729910714286 0.06473214285714424  12.62760416666667 0  12.985491071428571 0  L 13 0  L 13.014508928571429 0  C 13.372395833333336 0  13.732700892857144 0.06473214285714424  14.095424107142856 0.1941964285714295  C 14.458147321428571 0.32366071428571475  14.825706845238095 0.520554315476188  15.198102678571429 0.7848772321428558  C 15.570498511904763 1.0492001488095235  15.867931547619047 1.2757626488095235  16.090401785714285 1.4645647321428559  C 16.312872023809526 1.653366815476188  16.598214285714285 1.9095982142857149  16.946428571428573 2.2332589285714297  C 17.36235119047619 2.621651785714285  18.116815476190474 3.279761904761903  19.209821428571427 4.207589285714286  C 20.302827380952383 5.135416666666664  21.395833333333336 6.090215773809524  22.488839285714285 7.071986607142856  C 23.58184523809524 8.053757440476188  24.553943452380956 8.986979166666664  25.405133928571427 9.871651785714285  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 174 3226 )">
    <path d="M 21.081473214285715 14.572823660714285  C 21.207217261904763 14.589006696428571  21.308779761904763 14.656436011904761  21.386160714285715 14.77511160714286  L 21.952008928571427 15.600446428571429  C 22.029389880952383 15.719122023809524  22.060825892857142 15.845889136904761  22.046316964285715 15.98074776785714  C 22.031808035714285 16.115606398809522  21.976190476190474 16.226190476190478  21.879464285714285 16.3125  C 21.463541666666668 16.679315476190478  20.851748511904763 17.210658482142858  20.044084821428573 17.906529017857142  C 19.236421130952383 18.60239955357143  18.527901785714285 19.21196056547619  17.918526785714285 19.73521205357143  C 17.309151785714285 20.258463541666664  16.985119047619047 20.53627232142857  16.946428571428573 20.568638392857142  C 16.569196428571427 20.913876488095234  16.279017857142858 21.17280505952381  16.075892857142858 21.345424107142858  C 15.872767857142856 21.5180431547619  15.580171130952383 21.73921130952381  15.198102678571429 22.00892857142857  C 14.816034226190476 22.278645833333336  14.446056547619047 22.47553943452381  14.088169642857144 22.599609375  C 13.730282738095239 22.72367931547619  13.372395833333336 22.785714285714285  13.014508928571429 22.785714285714285  L 13 22.785714285714285  L 12.985491071428571 22.785714285714285  C 12.62760416666667 22.785714285714285  12.269717261904763 22.72367931547619  11.911830357142858 22.599609375  C 11.553943452380954 22.47553943452381  11.186383928571429 22.278645833333336  10.809151785714286 22.00892857142857  C 10.431919642857142 21.73921130952381  10.136904761904763 21.515345982142858  9.924107142857142 21.337332589285715  C 9.711309523809524 21.15931919642857  9.421130952380954 20.903087797619047  9.053571428571429 20.568638392857142  C 9.005208333333334 20.52548363095238  8.690848214285714 20.25306919642857  8.110491071428571 19.751395089285715  C 7.530133928571428 19.249720982142858  6.836123511904762 18.650948660714285  6.028459821428571 17.955078125  C 5.220796130952381 17.259207589285715  4.628348214285714 16.749441964285715  4.251116071428571 16.42578125  C 4.144717261904762 16.339471726190478  4.084263392857143 16.228887648809522  4.069754464285714 16.094029017857142  C 4.055245535714286 15.959170386904761  4.0866815476190474 15.832403273809524  4.1640625 15.713727678571429  L 4.700892857142857 14.872209821428571  C 4.77827380952381 14.753534226190476  4.882254464285714 14.683407738095235  5.012834821428571 14.66183035714286  C 5.143415178571429 14.640252976190476  5.261904761904762 14.67801339285714  5.368303571428571 14.77511160714286  C 6.277529761904762 15.562686011904761  7.757440476190477 16.835751488095234  9.808035714285714 18.594308035714285  C 9.85639880952381 18.637462797619047  10.066778273809524 18.82626488095238  10.439174107142858 19.160714285714285  C 10.811569940476192 19.49516369047619  11.104166666666668 19.746000744047617  11.316964285714286 19.91322544642857  C 11.529761904761905 20.080450148809522  11.803013392857142 20.255766369047617  12.13671875 20.439174107142858  C 12.470424107142858 20.622581845238095  12.753348214285714 20.714285714285715  12.985491071428571 20.714285714285715  L 13 20.714285714285715  L 13.014508928571429 20.714285714285715  C 13.246651785714288 20.714285714285715  13.529575892857144 20.622581845238095  13.86328125 20.439174107142858  C 14.19698660714286 20.255766369047617  14.470238095238098 20.080450148809522  14.683035714285715 19.91322544642857  C 14.895833333333336 19.746000744047617  15.188430059523812 19.49516369047619  15.560825892857144 19.160714285714285  C 15.933221726190476 18.82626488095238  16.14360119047619 18.637462797619047  16.191964285714285 18.594308035714285  C 18.6875 16.45814732142857  20.201264880952383 15.152715773809524  20.733258928571427 14.67801339285714  C 20.83965773809524 14.591703869047617  20.955729166666668 14.556640625  21.081473214285715 14.572823660714285  Z M 24.005022321428573 26.774832589285715  C 24.096912202380956 26.672340029761905  24.142857142857142 26.550967261904763  24 26.410714285714285  L 24 11.392857142857144  C 23.272321428571427 10.497395833333332  22.503348214285715 9.747581845238095  21.8359375 9.143415178571429  C 20.955729166666668 8.345052083333332  19.074404761904763 6.70517113095238  16.191964285714285 4.22377232142857  C 16.162946428571427 4.202194940476188  15.95498511904762 4.013392857142856  15.568080357142856 3.6573660714285703  C 15.181175595238098 3.301339285714285  14.886160714285715 3.042410714285715  14.683035714285715 2.880580357142856  C 14.479910714285715 2.71875  14.20907738095238 2.543433779761903  13.870535714285715 2.3546316964285703  C 13.531994047619047 2.165829613095238  13.246651785714288 2.0714285714285703  13.014508928571429 2.0714285714285703  L 13 2.0714285714285703  L 12.985491071428571 2.0714285714285703  C 12.753348214285714 2.0714285714285703  12.468005952380954 2.165829613095238  12.129464285714286 2.3546316964285703  C 11.79092261904762 2.543433779761903  11.520089285714288 2.71875  11.316964285714286 2.880580357142856  C 11.113839285714286 3.042410714285715  10.818824404761905 3.301339285714285  10.431919642857142 3.6573660714285703  C 10.045014880952381 4.013392857142856  9.837053571428571 4.202194940476188  9.808035714285714 4.22377232142857  C 7.7284226190476195 6.014694940476188  6.202566964285714 7.33900669642857  5.23046875 8.196707589285715  C 4.258370535714286 9.054408482142856  3.632068452380953 9.61542038690476  3.3515625 9.879743303571429  C 3.071056547619048 10.144066220238095  2.674479166666667 10.545944940476188  2.161830357142857 11.085379464285715  C 2.0264136904761907 11.21484375  1.924851190476191 11.317336309523808  2 11.392857142857144  L 2 26.410714285714285  C 1.857142857142857 26.550967261904763  1.903087797619048 26.672340029761905  1.9949776785714286 26.774832589285715  C 2.0868675595238093 26.877325148809522  2.1956845238095237 26.92857142857143  2.3214285714285716 27  L 23.678571428571427 27  C 23.804315476190474 26.92857142857143  23.91313244047619 26.877325148809522  24.005022321428573 26.774832589285715  Z M 25.405133928571427 9.871651785714285  C 25.80171130952381 10.281622023809524  26 10.788690476190473  26 11.392857142857144  L 26 26.410714285714285  C 26 27.122767857142854  25.772693452380956 27.73232886904762  25.318080357142858 28.23939732142857  C 24.863467261904766 28.746465773809522  24.316964285714292 29  23.678571428571427 29  L 2.3214285714285716 29  C 1.6830357142857144 29  1.1365327380952381 28.746465773809522  0.6819196428571429 28.23939732142857  C 0.22730654761904762 27.73232886904762  0 27.122767857142854  0 26.410714285714285  L 0 11.392857142857144  C 0 10.788690476190473  0.19828869047619047 10.281622023809524  0.5948660714285714 9.871651785714285  C 1.7845982142857144 8.641741071428571  3.477306547619047 7.074683779761902  5.672991071428571 5.170479910714286  C 7.868675595238096 3.2662760416666643  8.995535714285714 2.287202380952379  9.053571428571429 2.2332589285714297  C 9.401785714285714 1.9095982142857149  9.687127976190478 1.653366815476188  9.909598214285714 1.4645647321428559  C 10.132068452380954 1.2757626488095235  10.429501488095237 1.0492001488095235  10.801897321428571 0.7848772321428558  C 11.174293154761905 0.520554315476188  11.541852678571429 0.32366071428571475  11.904575892857142 0.1941964285714295  C 12.26729910714286 0.06473214285714424  12.62760416666667 0  12.985491071428571 0  L 13 0  L 13.014508928571429 0  C 13.372395833333336 0  13.732700892857144 0.06473214285714424  14.095424107142856 0.1941964285714295  C 14.458147321428571 0.32366071428571475  14.825706845238095 0.520554315476188  15.198102678571429 0.7848772321428558  C 15.570498511904763 1.0492001488095235  15.867931547619047 1.2757626488095235  16.090401785714285 1.4645647321428559  C 16.312872023809526 1.653366815476188  16.598214285714285 1.9095982142857149  16.946428571428573 2.2332589285714297  C 17.36235119047619 2.621651785714285  18.116815476190474 3.279761904761903  19.209821428571427 4.207589285714286  C 20.302827380952383 5.135416666666664  21.395833333333336 6.090215773809524  22.488839285714285 7.071986607142856  C 23.58184523809524 8.053757440476188  24.553943452380956 8.986979166666664  25.405133928571427 9.871651785714285  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 1)" stroke="none" transform="matrix(1 0 0 1 -174 -3226 )" class="fill" />
    <path d="M 21.081473214285715 14.572823660714285  C 21.207217261904763 14.589006696428571  21.308779761904763 14.656436011904761  21.386160714285715 14.77511160714286  L 21.952008928571427 15.600446428571429  C 22.029389880952383 15.719122023809524  22.060825892857142 15.845889136904761  22.046316964285715 15.98074776785714  C 22.031808035714285 16.115606398809522  21.976190476190474 16.226190476190478  21.879464285714285 16.3125  C 21.463541666666668 16.679315476190478  20.851748511904763 17.210658482142858  20.044084821428573 17.906529017857142  C 19.236421130952383 18.60239955357143  18.527901785714285 19.21196056547619  17.918526785714285 19.73521205357143  C 17.309151785714285 20.258463541666664  16.985119047619047 20.53627232142857  16.946428571428573 20.568638392857142  C 16.569196428571427 20.913876488095234  16.279017857142858 21.17280505952381  16.075892857142858 21.345424107142858  C 15.872767857142856 21.5180431547619  15.580171130952383 21.73921130952381  15.198102678571429 22.00892857142857  C 14.816034226190476 22.278645833333336  14.446056547619047 22.47553943452381  14.088169642857144 22.599609375  C 13.730282738095239 22.72367931547619  13.372395833333336 22.785714285714285  13.014508928571429 22.785714285714285  L 13 22.785714285714285  L 12.985491071428571 22.785714285714285  C 12.62760416666667 22.785714285714285  12.269717261904763 22.72367931547619  11.911830357142858 22.599609375  C 11.553943452380954 22.47553943452381  11.186383928571429 22.278645833333336  10.809151785714286 22.00892857142857  C 10.431919642857142 21.73921130952381  10.136904761904763 21.515345982142858  9.924107142857142 21.337332589285715  C 9.711309523809524 21.15931919642857  9.421130952380954 20.903087797619047  9.053571428571429 20.568638392857142  C 9.005208333333334 20.52548363095238  8.690848214285714 20.25306919642857  8.110491071428571 19.751395089285715  C 7.530133928571428 19.249720982142858  6.836123511904762 18.650948660714285  6.028459821428571 17.955078125  C 5.220796130952381 17.259207589285715  4.628348214285714 16.749441964285715  4.251116071428571 16.42578125  C 4.144717261904762 16.339471726190478  4.084263392857143 16.228887648809522  4.069754464285714 16.094029017857142  C 4.055245535714286 15.959170386904761  4.0866815476190474 15.832403273809524  4.1640625 15.713727678571429  L 4.700892857142857 14.872209821428571  C 4.77827380952381 14.753534226190476  4.882254464285714 14.683407738095235  5.012834821428571 14.66183035714286  C 5.143415178571429 14.640252976190476  5.261904761904762 14.67801339285714  5.368303571428571 14.77511160714286  C 6.277529761904762 15.562686011904761  7.757440476190477 16.835751488095234  9.808035714285714 18.594308035714285  C 9.85639880952381 18.637462797619047  10.066778273809524 18.82626488095238  10.439174107142858 19.160714285714285  C 10.811569940476192 19.49516369047619  11.104166666666668 19.746000744047617  11.316964285714286 19.91322544642857  C 11.529761904761905 20.080450148809522  11.803013392857142 20.255766369047617  12.13671875 20.439174107142858  C 12.470424107142858 20.622581845238095  12.753348214285714 20.714285714285715  12.985491071428571 20.714285714285715  L 13 20.714285714285715  L 13.014508928571429 20.714285714285715  C 13.246651785714288 20.714285714285715  13.529575892857144 20.622581845238095  13.86328125 20.439174107142858  C 14.19698660714286 20.255766369047617  14.470238095238098 20.080450148809522  14.683035714285715 19.91322544642857  C 14.895833333333336 19.746000744047617  15.188430059523812 19.49516369047619  15.560825892857144 19.160714285714285  C 15.933221726190476 18.82626488095238  16.14360119047619 18.637462797619047  16.191964285714285 18.594308035714285  C 18.6875 16.45814732142857  20.201264880952383 15.152715773809524  20.733258928571427 14.67801339285714  C 20.83965773809524 14.591703869047617  20.955729166666668 14.556640625  21.081473214285715 14.572823660714285  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 -174 -3226 )" class="stroke" mask="url(#u1231_img_cl44)" />
    <path d="M 24.005022321428573 26.774832589285715  C 24.096912202380956 26.672340029761905  24.142857142857142 26.550967261904763  24 26.410714285714285  L 24 11.392857142857144  C 23.272321428571427 10.497395833333332  22.503348214285715 9.747581845238095  21.8359375 9.143415178571429  C 20.955729166666668 8.345052083333332  19.074404761904763 6.70517113095238  16.191964285714285 4.22377232142857  C 16.162946428571427 4.202194940476188  15.95498511904762 4.013392857142856  15.568080357142856 3.6573660714285703  C 15.181175595238098 3.301339285714285  14.886160714285715 3.042410714285715  14.683035714285715 2.880580357142856  C 14.479910714285715 2.71875  14.20907738095238 2.543433779761903  13.870535714285715 2.3546316964285703  C 13.531994047619047 2.165829613095238  13.246651785714288 2.0714285714285703  13.014508928571429 2.0714285714285703  L 13 2.0714285714285703  L 12.985491071428571 2.0714285714285703  C 12.753348214285714 2.0714285714285703  12.468005952380954 2.165829613095238  12.129464285714286 2.3546316964285703  C 11.79092261904762 2.543433779761903  11.520089285714288 2.71875  11.316964285714286 2.880580357142856  C 11.113839285714286 3.042410714285715  10.818824404761905 3.301339285714285  10.431919642857142 3.6573660714285703  C 10.045014880952381 4.013392857142856  9.837053571428571 4.202194940476188  9.808035714285714 4.22377232142857  C 7.7284226190476195 6.014694940476188  6.202566964285714 7.33900669642857  5.23046875 8.196707589285715  C 4.258370535714286 9.054408482142856  3.632068452380953 9.61542038690476  3.3515625 9.879743303571429  C 3.071056547619048 10.144066220238095  2.674479166666667 10.545944940476188  2.161830357142857 11.085379464285715  C 2.0264136904761907 11.21484375  1.924851190476191 11.317336309523808  2 11.392857142857144  L 2 26.410714285714285  C 1.857142857142857 26.550967261904763  1.903087797619048 26.672340029761905  1.9949776785714286 26.774832589285715  C 2.0868675595238093 26.877325148809522  2.1956845238095237 26.92857142857143  2.3214285714285716 27  L 23.678571428571427 27  C 23.804315476190474 26.92857142857143  23.91313244047619 26.877325148809522  24.005022321428573 26.774832589285715  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 -174 -3226 )" class="stroke" mask="url(#u1231_img_cl44)" />
    <path d="M 25.405133928571427 9.871651785714285  C 25.80171130952381 10.281622023809524  26 10.788690476190473  26 11.392857142857144  L 26 26.410714285714285  C 26 27.122767857142854  25.772693452380956 27.73232886904762  25.318080357142858 28.23939732142857  C 24.863467261904766 28.746465773809522  24.316964285714292 29  23.678571428571427 29  L 2.3214285714285716 29  C 1.6830357142857144 29  1.1365327380952381 28.746465773809522  0.6819196428571429 28.23939732142857  C 0.22730654761904762 27.73232886904762  0 27.122767857142854  0 26.410714285714285  L 0 11.392857142857144  C 0 10.788690476190473  0.19828869047619047 10.281622023809524  0.5948660714285714 9.871651785714285  C 1.7845982142857144 8.641741071428571  3.477306547619047 7.074683779761902  5.672991071428571 5.170479910714286  C 7.868675595238096 3.2662760416666643  8.995535714285714 2.287202380952379  9.053571428571429 2.2332589285714297  C 9.401785714285714 1.9095982142857149  9.687127976190478 1.653366815476188  9.909598214285714 1.4645647321428559  C 10.132068452380954 1.2757626488095235  10.429501488095237 1.0492001488095235  10.801897321428571 0.7848772321428558  C 11.174293154761905 0.520554315476188  11.541852678571429 0.32366071428571475  11.904575892857142 0.1941964285714295  C 12.26729910714286 0.06473214285714424  12.62760416666667 0  12.985491071428571 0  L 13 0  L 13.014508928571429 0  C 13.372395833333336 0  13.732700892857144 0.06473214285714424  14.095424107142856 0.1941964285714295  C 14.458147321428571 0.32366071428571475  14.825706845238095 0.520554315476188  15.198102678571429 0.7848772321428558  C 15.570498511904763 1.0492001488095235  15.867931547619047 1.2757626488095235  16.090401785714285 1.4645647321428559  C 16.312872023809526 1.653366815476188  16.598214285714285 1.9095982142857149  16.946428571428573 2.2332589285714297  C 17.36235119047619 2.621651785714285  18.116815476190474 3.279761904761903  19.209821428571427 4.207589285714286  C 20.302827380952383 5.135416666666664  21.395833333333336 6.090215773809524  22.488839285714285 7.071986607142856  C 23.58184523809524 8.053757440476188  24.553943452380956 8.986979166666664  25.405133928571427 9.871651785714285  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 -174 -3226 )" class="stroke" mask="url(#u1231_img_cl44)" />
  </g>
        </svg>
        <div id="u1231_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- user (组合) -->
      <div id="u1232" class="ax_default" data-label="user" data-left="64" data-top="-3226" data-width="38" data-height="34" layer-opacity="1">

        <!-- Rectangle 4117 (形状) -->
        <div id="u1233" class="ax_default _形状 transition notrs" data-label="Rectangle 4117">
          <svg data="images/首页（学生端）/rectangle_4117_u3.svg" id="u1233_img" class="img generatedImage">

  <defs>
    <pattern id="u1233_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u1233_img_cl45">
      <path d="M 0 33.99998813176525  L 1.3264497682875357E-05 0  L 38 0  L 38 34  L 0 33.99998813176525  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -64 3226 )">
    <path d="M 0 33.99998813176525  L 1.3264497682875357E-05 0  L 38 0  L 38 34  L 0 33.99998813176525  Z " fill-rule="nonzero" fill="rgba(196, 196, 196, 0)" stroke="none" transform="matrix(1 0 0 1 64 -3226 )" class="fill" />
    <path d="M 0 33.99998813176525  L 1.3264497682875357E-05 0  L 38 0  L 38 34  L 0 33.99998813176525  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 64 -3226 )" class="stroke" mask="url(#u1233_img_cl45)" />
  </g>
          </svg>
          <div id="u1233_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Union (形状) -->
        <div id="u1234" class="ax_default _形状 transition notrs" data-label="Union">
          <svg data="images/首页（学生端）/union_u4.svg" id="u1234_img" class="img generatedImage">

  <defs>
    <pattern id="u1234_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u1234_img_cl46">
      <path d="M 24.75000117857143 8.571429728571372  C 24.75000117857143 12.713569778571445  21.056357849999998 16.071429728571374  16.500001272857144 16.071429728571374  C 11.943655632857144 16.071429728571374  8.250001272857144 12.713569778571445  8.250001272857144 8.571429728571372  C 8.250001272857144 4.429294521428631  11.943655632857144 1.071429728571372  16.500001272857144 1.071429728571372  C 21.056357849999998 1.071429728571372  24.75000117857143 4.429294521428631  24.75000117857143 8.571429728571372  Z M 30.555976864285714 21.113096785714287  C 31.342904528571427 21.454778571428548  31.82142975 22.188557785714238  31.82142975 22.981355571428583  L 31.82142975 27.85714392857143  C 31.82142975 28.44888685714291  31.293775542857144 28.928572499999998  30.642858321428573 29  L 2.3571441299999996 29  C 1.7062403442857144 28.928572499999998  1.1785727014285716 28.44888685714291  1.1785727014285716 27.85714392857143  L 1.1785727014285716 22.981355571428583  C 1.1785727014285716 22.188557785714238  1.6571103921428572 21.454778571428548  2.444038810714286 21.113096785714287  C 6.716453370000001 19.258027242857167  11.471061784285713 18.214286871428513  16.500001272857144 18.214286871428513  C 21.528952735714284 18.214286871428513  26.28356162142857 19.258027242857167  30.555976864285714 21.113096785714287  Z M 10.60714413 8.571429728571372  C 10.60714413 11.530101085714284  13.245468955714287 13.928572585714228  16.500001272857144 13.928572585714228  C 19.754542287857145 13.928572585714228  22.392858321428573 11.530102092857085  22.392858321428573 8.571429728571372  C 22.392858321428573 5.612762185714278  19.754542287857145 3.2142868714285147  16.500001272857144 3.2142868714285147  C 13.245468955714287 3.2142868714285147  10.60714413 5.612762185714278  10.60714413 8.571429728571372  Z M 29.464286892857146 26.785715357142852  L 29.464286892857146 23.013932571428608  C 25.517627614285715 21.31228135714281  21.135557543571426 20.35714392857143  16.500001272857144 20.35714392857143  C 11.864457070714286 20.35714392857143  7.482388202142858 21.31228135714281  3.535715558571428 23.013932571428608  L 3.535715558571428 26.785715357142852  L 29.464286892857146 26.785715357142852  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -66 3224 )">
    <path d="M 24.75000117857143 8.571429728571372  C 24.75000117857143 12.713569778571445  21.056357849999998 16.071429728571374  16.500001272857144 16.071429728571374  C 11.943655632857144 16.071429728571374  8.250001272857144 12.713569778571445  8.250001272857144 8.571429728571372  C 8.250001272857144 4.429294521428631  11.943655632857144 1.071429728571372  16.500001272857144 1.071429728571372  C 21.056357849999998 1.071429728571372  24.75000117857143 4.429294521428631  24.75000117857143 8.571429728571372  Z M 30.555976864285714 21.113096785714287  C 31.342904528571427 21.454778571428548  31.82142975 22.188557785714238  31.82142975 22.981355571428583  L 31.82142975 27.85714392857143  C 31.82142975 28.44888685714291  31.293775542857144 28.928572499999998  30.642858321428573 29  L 2.3571441299999996 29  C 1.7062403442857144 28.928572499999998  1.1785727014285716 28.44888685714291  1.1785727014285716 27.85714392857143  L 1.1785727014285716 22.981355571428583  C 1.1785727014285716 22.188557785714238  1.6571103921428572 21.454778571428548  2.444038810714286 21.113096785714287  C 6.716453370000001 19.258027242857167  11.471061784285713 18.214286871428513  16.500001272857144 18.214286871428513  C 21.528952735714284 18.214286871428513  26.28356162142857 19.258027242857167  30.555976864285714 21.113096785714287  Z M 10.60714413 8.571429728571372  C 10.60714413 11.530101085714284  13.245468955714287 13.928572585714228  16.500001272857144 13.928572585714228  C 19.754542287857145 13.928572585714228  22.392858321428573 11.530102092857085  22.392858321428573 8.571429728571372  C 22.392858321428573 5.612762185714278  19.754542287857145 3.2142868714285147  16.500001272857144 3.2142868714285147  C 13.245468955714287 3.2142868714285147  10.60714413 5.612762185714278  10.60714413 8.571429728571372  Z M 29.464286892857146 26.785715357142852  L 29.464286892857146 23.013932571428608  C 25.517627614285715 21.31228135714281  21.135557543571426 20.35714392857143  16.500001272857144 20.35714392857143  C 11.864457070714286 20.35714392857143  7.482388202142858 21.31228135714281  3.535715558571428 23.013932571428608  L 3.535715558571428 26.785715357142852  L 29.464286892857146 26.785715357142852  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 0.8980392156862745)" stroke="none" transform="matrix(1 0 0 1 66 -3224 )" class="fill" />
    <path d="M 24.75000117857143 8.571429728571372  C 24.75000117857143 12.713569778571445  21.056357849999998 16.071429728571374  16.500001272857144 16.071429728571374  C 11.943655632857144 16.071429728571374  8.250001272857144 12.713569778571445  8.250001272857144 8.571429728571372  C 8.250001272857144 4.429294521428631  11.943655632857144 1.071429728571372  16.500001272857144 1.071429728571372  C 21.056357849999998 1.071429728571372  24.75000117857143 4.429294521428631  24.75000117857143 8.571429728571372  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 66 -3224 )" class="stroke" mask="url(#u1234_img_cl46)" />
    <path d="M 30.555976864285714 21.113096785714287  C 31.342904528571427 21.454778571428548  31.82142975 22.188557785714238  31.82142975 22.981355571428583  L 31.82142975 27.85714392857143  C 31.82142975 28.44888685714291  31.293775542857144 28.928572499999998  30.642858321428573 29  L 2.3571441299999996 29  C 1.7062403442857144 28.928572499999998  1.1785727014285716 28.44888685714291  1.1785727014285716 27.85714392857143  L 1.1785727014285716 22.981355571428583  C 1.1785727014285716 22.188557785714238  1.6571103921428572 21.454778571428548  2.444038810714286 21.113096785714287  C 6.716453370000001 19.258027242857167  11.471061784285713 18.214286871428513  16.500001272857144 18.214286871428513  C 21.528952735714284 18.214286871428513  26.28356162142857 19.258027242857167  30.555976864285714 21.113096785714287  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 66 -3224 )" class="stroke" mask="url(#u1234_img_cl46)" />
    <path d="M 10.60714413 8.571429728571372  C 10.60714413 11.530101085714284  13.245468955714287 13.928572585714228  16.500001272857144 13.928572585714228  C 19.754542287857145 13.928572585714228  22.392858321428573 11.530102092857085  22.392858321428573 8.571429728571372  C 22.392858321428573 5.612762185714278  19.754542287857145 3.2142868714285147  16.500001272857144 3.2142868714285147  C 13.245468955714287 3.2142868714285147  10.60714413 5.612762185714278  10.60714413 8.571429728571372  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 66 -3224 )" class="stroke" mask="url(#u1234_img_cl46)" />
    <path d="M 29.464286892857146 26.785715357142852  L 29.464286892857146 23.013932571428608  C 25.517627614285715 21.31228135714281  21.135557543571426 20.35714392857143  16.500001272857144 20.35714392857143  C 11.864457070714286 20.35714392857143  7.482388202142858 21.31228135714281  3.535715558571428 23.013932571428608  L 3.535715558571428 26.785715357142852  L 29.464286892857146 26.785715357142852  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 66 -3224 )" class="stroke" mask="url(#u1234_img_cl46)" />
  </g>
          </svg>
          <div id="u1234_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1235" class="ax_default label transition notrs">
        <div id="u1235_div" class=""></div>
        <div id="u1235_text" class="text ">
          <p><span>首页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1236" class="ax_default label transition notrs">
        <div id="u1236_div" class=""></div>
        <div id="u1236_text" class="text ">
          <p><span>我的</span></p>
        </div>
      </div>

      <!-- Unnamed (椭圆) -->
      <div id="u1237" class="ax_default ellipse transition notrs">
        <svg data="images/首页（学生端）/u7.svg" id="u1237_img" class="img generatedImage">

  <defs>
    <pattern id="u1237_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u1237_img_cl47">
      <path d="M 0 22  C 0 9.68  9.68 0  22 0  C 34.32 0  44 9.68  44 22  C 44 34.32  34.32 44  22 44  C 9.68 44  0 34.32  0 22  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 53 3226 )">
    <path d="M 0 22  C 0 9.68  9.68 0  22 0  C 34.32 0  44 9.68  44 22  C 44 34.32  34.32 44  22 44  C 9.68 44  0 34.32  0 22  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 -53 -3226 )" class="fill" />
    <path d="M 0 22  C 0 9.68  9.68 0  22 0  C 34.32 0  44 9.68  44 22  C 44 34.32  34.32 44  22 44  C 9.68 44  0 34.32  0 22  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 -53 -3226 )" class="stroke" mask="url(#u1237_img_cl47)" />
  </g>
        </svg>
        <div id="u1237_text" class="text ">
          <p><span>+</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1238" class="ax_default box_1 transition notrs">
        <div id="u1238_div" class=""></div>
        <div id="u1238_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1239" class="ax_default _图片 transition notrs">
        <img id="u1239_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u1239_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1240" class="ax_default label transition notrs">
        <div id="u1240_div" class=""></div>
        <div id="u1240_text" class="text ">
          <p><span>辅导君</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1241" class="ax_default box_2 transition notrs">
        <svg data="images/首页（学生端）/u12.svg" id="u1241_img" class="img generatedImage">

  <defs>
    <pattern id="u1241_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
  </defs>
  <g transform="matrix(1 0 0 1 -19 -231 )">
    <path d="M 0 46  L 0 0  L 1 0  L 1 46  L 0 46  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 0)" stroke="none" transform="matrix(1 0 0 1 19 231 )" class="fill" />
    <path d="M 1 45.5  L 0 45.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(233, 233, 233, 1)" fill="none" transform="matrix(1 0 0 1 19 231 )" class="stroke" />
  </g>
        </svg>
        <div id="u1241_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1242" class="ax_default box_2 transition notrs">
        <div id="u1242_div" class=""></div>
        <div id="u1242_text" class="text ">
          <p><span>论文辅导</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1243" class="ax_default box_2 transition notrs">
        <div id="u1243_div" class=""></div>
        <div id="u1243_text" class="text ">
          <p><span>推荐</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1244" class="ax_default box_2 transition notrs">
        <div id="u1244_div" class=""></div>
        <div id="u1244_text" class="text ">
          <p><span>作业辅导</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1245" class="ax_default box_2 transition notrs">
        <div id="u1245_div" class=""></div>
        <div id="u1245_text" class="text ">
          <p><span>保研</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1246" class="ax_default box_2 transition notrs">
        <div id="u1246_div" class=""></div>
        <div id="u1246_text" class="text ">
          <p><span>考研</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1247" class="ax_default _图片 transition notrs">
        <img id="u1247_img" class="img " src="images/首页（学生端）/u19.svg"/>
        <div id="u1247_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u1248" class="ax_default _图片 transition notrs">
        <img id="u1248_img" class="img " src="images/首页（学生端）/u21.png"/>
        <div id="u1248_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1249" class="ax_default label transition notrs">
        <div id="u1249_div" class=""></div>
        <div id="u1249_text" class="text ">
          <p><span>张伟 | 本科| 心理学</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1250" class="ax_default label transition notrs">
        <div id="u1250_div" class=""></div>
        <div id="u1250_text" class="text ">
          <p><span>2025-3-12 11:12</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1251" class="ax_default label transition notrs">
        <div id="u1251_div" class=""></div>
        <div id="u1251_text" class="text ">
          <p><span>需要本科论文辅导需要本科论文辅导需要本科论文辅导需要本科</span></p><p><span>论文辅导需要本科论文辅导需要本科论文辅导需要本科论文辅导导</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1252" class="ax_default label transition notrs">
        <div id="u1252_div" class=""></div>
        <div id="u1252_text" class="text ">
          <p><span>博士</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1253" class="ax_default label transition notrs">
        <div id="u1253_div" class=""></div>
        <div id="u1253_text" class="text ">
          <p><span>老师要求：</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1254" class="ax_default label transition notrs">
        <div id="u1254_div" class=""></div>
        <div id="u1254_text" class="text ">
          <p><span>预算：10000-20000</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u1255" class="ax_default line transition notrs">
        <svg data="images/首页（学生端）/u30.svg" id="u1255_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -24 -454 )">
    <path d="M 0 0.5  L 424 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(215, 215, 215, 1)" fill="none" transform="matrix(1 0 0 1 24 454 )" class="stroke" />
  </g>
        </svg>
        <div id="u1255_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1256" class="ax_default primary_button transition notrs">
        <div id="u1256_div" class=""></div>
        <div id="u1256_text" class="text ">
          <p><span>投递</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1257" class="ax_default" data-left="49" data-top="760" data-width="36" data-height="56" layer-opacity="1">

        <!-- Unnamed (形状) -->
        <div id="u1258" class="ax_default icon transition notrs">
          <svg data="images/首页（学生端）/u44.svg" id="u1258_img" class="img generatedImage">

  <defs>
    <pattern id="u1258_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u1258_img_cl49">
      <path d="M 29.11276332094176 16.568199532346064  C 29.126807104502273 16.598337230449467  29.133828996282528 16.643543777604574  29 16.70381917381138  L 29 27.553390491036634  C 29.133828996282528 27.945180566380877  29.000413052457667 28.284229670044166  28.73358116480793 28.5705378020265  C 28.4667492771582 28.856845934008835  28.15076414704668 29  27.785625774473356 29  L 19.696406443618336 29  L 19.696406443618336 20.3203429462198  L 14.30359355638166 20.3203429462198  L 14.30359355638166 29  L 6.214374225526642 29  C 5.849235852953326 29  5.533250722841801 28.856845934008835  5.266418835192069 28.5705378020265  C 4.999586947542339 28.284229670044166  4.866171003717472 27.945180566380877  5 27.553390491036634  L 5 16.70381917381138  C 4.866171003717472 16.688750324759678  4.8696819496076005 16.666147051182126  4.876703841387856 16.63600935307872  C 4.883725733168113 16.605871654975317  4.88723667905824 16.583268381397765  4.88723667905824 16.568199532346064  L 17 5.854247856586126  L 29.11276332094176 16.568199532346064  Z M 33.97893432465923 14.613016367887765  C 33.992978108219745 14.816445830085737  33.94382486575795 14.993504806443232  33.83147459727385 15.144193296960252  L 32.52540272614622 16.816835541699145  C 32.413052457662126 16.95245518316446  32.265592730276744 17.035333852948817  32.08302354399009 17.065471551052223  L 32.01982651796778 17.065471551052223  C 31.837257331681126 17.065471551052223  31.689797604295748 17.012730579371265  31.577447335811648 16.90724863600935  L 17 3.8651597817614967  L 2.422552664188352 16.90724863600935  C 2.254027261462206 17.027799428422963  2.0855018587360594 17.08054040010392  1.9169764560099132 17.065471551052223  C 1.7344072697232549 17.035333852948817  1.5869475423378772 16.95245518316446  1.4745972738537794 16.816835541699145  L 0.16852540272614622 15.144193296960252  C 0.056175134242048765 14.993504806443232  0.007021891780256142 14.816445830085737  0.021065675340768277 14.613016367887765  C 0.03510945890128057 14.40958690568979  0.11235026848409753 14.247596778383995  0.2527881040892193 14.127045985970382  L 15.39900867410161 0.5876851130163687  C 15.848409748038002 0.1958950376721197  16.382073523337464 0  17 0  C 17.61792647666254 0  18.151590251962002 0.1958950376721197  18.600991325898388 0.5876851130163687  L 23.741016109045848 5.198752922837099  L 23.741016109045848 0.79111457521434  C 23.741016109045848 0.5801506884905139  23.804213135068153 0.40685892439594584  23.930607187112763 0.2712392829306327  C 24.057001239157373 0.13561964146531635  24.218504750103264 0.06780982073265818  24.415117719950434 0.06780982073265818  L 28.459727385377946 0.06780982073265818  C 28.656340355225115 0.06780982073265818  28.817843866171003 0.13561964146531635  28.944237918215613 0.2712392829306327  C 29.070631970260223 0.40685892439594584  29.133828996282528 0.5801506884905139  29.133828996282528 0.79111457521434  L 29.133828996282528 10.013250194855807  L 33.74721189591078 14.127045985970382  C 33.88764973151591 14.247596778383995  33.964890541098725 14.40958690568979  33.97893432465923 14.613016367887765  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -49 -760 )">
    <path d="M 29.11276332094176 16.568199532346064  C 29.126807104502273 16.598337230449467  29.133828996282528 16.643543777604574  29 16.70381917381138  L 29 27.553390491036634  C 29.133828996282528 27.945180566380877  29.000413052457667 28.284229670044166  28.73358116480793 28.5705378020265  C 28.4667492771582 28.856845934008835  28.15076414704668 29  27.785625774473356 29  L 19.696406443618336 29  L 19.696406443618336 20.3203429462198  L 14.30359355638166 20.3203429462198  L 14.30359355638166 29  L 6.214374225526642 29  C 5.849235852953326 29  5.533250722841801 28.856845934008835  5.266418835192069 28.5705378020265  C 4.999586947542339 28.284229670044166  4.866171003717472 27.945180566380877  5 27.553390491036634  L 5 16.70381917381138  C 4.866171003717472 16.688750324759678  4.8696819496076005 16.666147051182126  4.876703841387856 16.63600935307872  C 4.883725733168113 16.605871654975317  4.88723667905824 16.583268381397765  4.88723667905824 16.568199532346064  L 17 5.854247856586126  L 29.11276332094176 16.568199532346064  Z M 33.97893432465923 14.613016367887765  C 33.992978108219745 14.816445830085737  33.94382486575795 14.993504806443232  33.83147459727385 15.144193296960252  L 32.52540272614622 16.816835541699145  C 32.413052457662126 16.95245518316446  32.265592730276744 17.035333852948817  32.08302354399009 17.065471551052223  L 32.01982651796778 17.065471551052223  C 31.837257331681126 17.065471551052223  31.689797604295748 17.012730579371265  31.577447335811648 16.90724863600935  L 17 3.8651597817614967  L 2.422552664188352 16.90724863600935  C 2.254027261462206 17.027799428422963  2.0855018587360594 17.08054040010392  1.9169764560099132 17.065471551052223  C 1.7344072697232549 17.035333852948817  1.5869475423378772 16.95245518316446  1.4745972738537794 16.816835541699145  L 0.16852540272614622 15.144193296960252  C 0.056175134242048765 14.993504806443232  0.007021891780256142 14.816445830085737  0.021065675340768277 14.613016367887765  C 0.03510945890128057 14.40958690568979  0.11235026848409753 14.247596778383995  0.2527881040892193 14.127045985970382  L 15.39900867410161 0.5876851130163687  C 15.848409748038002 0.1958950376721197  16.382073523337464 0  17 0  C 17.61792647666254 0  18.151590251962002 0.1958950376721197  18.600991325898388 0.5876851130163687  L 23.741016109045848 5.198752922837099  L 23.741016109045848 0.79111457521434  C 23.741016109045848 0.5801506884905139  23.804213135068153 0.40685892439594584  23.930607187112763 0.2712392829306327  C 24.057001239157373 0.13561964146531635  24.218504750103264 0.06780982073265818  24.415117719950434 0.06780982073265818  L 28.459727385377946 0.06780982073265818  C 28.656340355225115 0.06780982073265818  28.817843866171003 0.13561964146531635  28.944237918215613 0.2712392829306327  C 29.070631970260223 0.40685892439594584  29.133828996282528 0.5801506884905139  29.133828996282528 0.79111457521434  L 29.133828996282528 10.013250194855807  L 33.74721189591078 14.127045985970382  C 33.88764973151591 14.247596778383995  33.964890541098725 14.40958690568979  33.97893432465923 14.613016367887765  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 1)" stroke="none" transform="matrix(1 0 0 1 49 760 )" class="fill" />
    <path d="M 29.11276332094176 16.568199532346064  C 29.126807104502273 16.598337230449467  29.133828996282528 16.643543777604574  29 16.70381917381138  L 29 27.553390491036634  C 29.133828996282528 27.945180566380877  29.000413052457667 28.284229670044166  28.73358116480793 28.5705378020265  C 28.4667492771582 28.856845934008835  28.15076414704668 29  27.785625774473356 29  L 19.696406443618336 29  L 19.696406443618336 20.3203429462198  L 14.30359355638166 20.3203429462198  L 14.30359355638166 29  L 6.214374225526642 29  C 5.849235852953326 29  5.533250722841801 28.856845934008835  5.266418835192069 28.5705378020265  C 4.999586947542339 28.284229670044166  4.866171003717472 27.945180566380877  5 27.553390491036634  L 5 16.70381917381138  C 4.866171003717472 16.688750324759678  4.8696819496076005 16.666147051182126  4.876703841387856 16.63600935307872  C 4.883725733168113 16.605871654975317  4.88723667905824 16.583268381397765  4.88723667905824 16.568199532346064  L 17 5.854247856586126  L 29.11276332094176 16.568199532346064  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 49 760 )" class="stroke" mask="url(#u1258_img_cl49)" />
    <path d="M 33.97893432465923 14.613016367887765  C 33.992978108219745 14.816445830085737  33.94382486575795 14.993504806443232  33.83147459727385 15.144193296960252  L 32.52540272614622 16.816835541699145  C 32.413052457662126 16.95245518316446  32.265592730276744 17.035333852948817  32.08302354399009 17.065471551052223  L 32.01982651796778 17.065471551052223  C 31.837257331681126 17.065471551052223  31.689797604295748 17.012730579371265  31.577447335811648 16.90724863600935  L 17 3.8651597817614967  L 2.422552664188352 16.90724863600935  C 2.254027261462206 17.027799428422963  2.0855018587360594 17.08054040010392  1.9169764560099132 17.065471551052223  C 1.7344072697232549 17.035333852948817  1.5869475423378772 16.95245518316446  1.4745972738537794 16.816835541699145  L 0.16852540272614622 15.144193296960252  C 0.056175134242048765 14.993504806443232  0.007021891780256142 14.816445830085737  0.021065675340768277 14.613016367887765  C 0.03510945890128057 14.40958690568979  0.11235026848409753 14.247596778383995  0.2527881040892193 14.127045985970382  L 15.39900867410161 0.5876851130163687  C 15.848409748038002 0.1958950376721197  16.382073523337464 0  17 0  C 17.61792647666254 0  18.151590251962002 0.1958950376721197  18.600991325898388 0.5876851130163687  L 23.741016109045848 5.198752922837099  L 23.741016109045848 0.79111457521434  C 23.741016109045848 0.5801506884905139  23.804213135068153 0.40685892439594584  23.930607187112763 0.2712392829306327  C 24.057001239157373 0.13561964146531635  24.218504750103264 0.06780982073265818  24.415117719950434 0.06780982073265818  L 28.459727385377946 0.06780982073265818  C 28.656340355225115 0.06780982073265818  28.817843866171003 0.13561964146531635  28.944237918215613 0.2712392829306327  C 29.070631970260223 0.40685892439594584  29.133828996282528 0.5801506884905139  29.133828996282528 0.79111457521434  L 29.133828996282528 10.013250194855807  L 33.74721189591078 14.127045985970382  C 33.88764973151591 14.247596778383995  33.964890541098725 14.40958690568979  33.97893432465923 14.613016367887765  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 49 760 )" class="stroke" mask="url(#u1258_img_cl49)" />
  </g>
          </svg>
          <div id="u1258_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1259" class="ax_default label transition notrs">
          <div id="u1259_div" class=""></div>
          <div id="u1259_text" class="text ">
            <p><span>首页</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1260" class="ax_default" data-left="370" data-top="760" data-width="33" data-height="58" layer-opacity="1">

        <!-- Unnamed (形状) -->
        <div id="u1261" class="ax_default icon transition notrs">
          <svg data="images/首页（学生端）/u47.svg" id="u1261_img" class="img generatedImage">

  <defs>
    <pattern id="u1261_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u1261_img_cl50">
      <path d="M 28.807421875000003 24.581380208333332  C 28.935807291666666 25.695529513888886  29 26.879774305555554  29 28.134114583333336  C 29 29.74262152777778  28.527994791666668 31.122395833333336  27.583984375 32.2734375  C 26.639973958333336 33.424479166666664  25.50338541666667 34  24.17421875 34  L 4.82578125 34  C 3.4966145833333333 34  2.360026041666667 33.424479166666664  1.4160156250000002 32.2734375  C 0.4720052083333335 31.122395833333336  0 29.74262152777778  0 28.134114583333336  C 0 26.879774305555554  0.06419270833333349 25.695529513888886  0.19257812500000018 24.581380208333332  C 0.32096354166666685 23.46723090277777  0.5588541666666668 22.345703125  0.9062500000000002 21.216796875  C 1.2536458333333338 20.087890625  1.6954427083333337 19.12131076388889  2.231640625 18.317057291666668  C 2.7678385416666673 17.512803819444443  3.477734375 16.856119791666664  4.361328125 16.347005208333336  C 5.244921874999999 15.837890625  6.260677083333334 15.583333333333334  7.4085937500000005 15.583333333333334  C 9.387239583333335 17.47222222222222  11.75104166666667 18.416666666666668  14.5 18.416666666666668  C 17.248958333333338 18.416666666666668  19.61276041666667 17.47222222222222  21.59140625 15.583333333333334  C 22.73932291666667 15.583333333333334  23.755078124999997 15.837890625  24.638671875 16.347005208333336  C 25.522265625000003 16.856119791666664  26.232161458333337 17.512803819444443  26.768359375000003 18.317057291666668  C 27.30455729166667 19.12131076388889  27.746354166666666 20.087890625  28.09375 21.216796875  C 28.441145833333337 22.345703125  28.679036458333336 23.46723090277777  28.807421875000003 24.581380208333332  Z M 20.651171875000003 2.490234375  C 22.350390625000003 4.150390625  23.200000000000003 6.153645833333335  23.200000000000003 8.5  C 23.200000000000003 10.846354166666666  22.350390625000003 12.849609375  20.651171875000003 14.509765625  C 18.951953125000003 16.169921875  16.901562499999997 17  14.5 17  C 12.0984375 17  10.048046875 16.169921875  8.348828125 14.509765625  C 6.649609375000001 12.849609375  5.800000000000001 10.846354166666666  5.800000000000001 8.5  C 5.800000000000001 6.153645833333335  6.649609375000001 4.150390625  8.348828125 2.490234375  C 10.048046875 0.830078125  12.0984375 0  14.5 0  C 16.901562499999997 0  18.951953125000003 0.830078125  20.651171875000003 2.490234375  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -370 -760 )">
    <path d="M 28.807421875000003 24.581380208333332  C 28.935807291666666 25.695529513888886  29 26.879774305555554  29 28.134114583333336  C 29 29.74262152777778  28.527994791666668 31.122395833333336  27.583984375 32.2734375  C 26.639973958333336 33.424479166666664  25.50338541666667 34  24.17421875 34  L 4.82578125 34  C 3.4966145833333333 34  2.360026041666667 33.424479166666664  1.4160156250000002 32.2734375  C 0.4720052083333335 31.122395833333336  0 29.74262152777778  0 28.134114583333336  C 0 26.879774305555554  0.06419270833333349 25.695529513888886  0.19257812500000018 24.581380208333332  C 0.32096354166666685 23.46723090277777  0.5588541666666668 22.345703125  0.9062500000000002 21.216796875  C 1.2536458333333338 20.087890625  1.6954427083333337 19.12131076388889  2.231640625 18.317057291666668  C 2.7678385416666673 17.512803819444443  3.477734375 16.856119791666664  4.361328125 16.347005208333336  C 5.244921874999999 15.837890625  6.260677083333334 15.583333333333334  7.4085937500000005 15.583333333333334  C 9.387239583333335 17.47222222222222  11.75104166666667 18.416666666666668  14.5 18.416666666666668  C 17.248958333333338 18.416666666666668  19.61276041666667 17.47222222222222  21.59140625 15.583333333333334  C 22.73932291666667 15.583333333333334  23.755078124999997 15.837890625  24.638671875 16.347005208333336  C 25.522265625000003 16.856119791666664  26.232161458333337 17.512803819444443  26.768359375000003 18.317057291666668  C 27.30455729166667 19.12131076388889  27.746354166666666 20.087890625  28.09375 21.216796875  C 28.441145833333337 22.345703125  28.679036458333336 23.46723090277777  28.807421875000003 24.581380208333332  Z M 20.651171875000003 2.490234375  C 22.350390625000003 4.150390625  23.200000000000003 6.153645833333335  23.200000000000003 8.5  C 23.200000000000003 10.846354166666666  22.350390625000003 12.849609375  20.651171875000003 14.509765625  C 18.951953125000003 16.169921875  16.901562499999997 17  14.5 17  C 12.0984375 17  10.048046875 16.169921875  8.348828125 14.509765625  C 6.649609375000001 12.849609375  5.800000000000001 10.846354166666666  5.800000000000001 8.5  C 5.800000000000001 6.153645833333335  6.649609375000001 4.150390625  8.348828125 2.490234375  C 10.048046875 0.830078125  12.0984375 0  14.5 0  C 16.901562499999997 0  18.951953125000003 0.830078125  20.651171875000003 2.490234375  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 1)" stroke="none" transform="matrix(1 0 0 1 370 760 )" class="fill" />
    <path d="M 28.807421875000003 24.581380208333332  C 28.935807291666666 25.695529513888886  29 26.879774305555554  29 28.134114583333336  C 29 29.74262152777778  28.527994791666668 31.122395833333336  27.583984375 32.2734375  C 26.639973958333336 33.424479166666664  25.50338541666667 34  24.17421875 34  L 4.82578125 34  C 3.4966145833333333 34  2.360026041666667 33.424479166666664  1.4160156250000002 32.2734375  C 0.4720052083333335 31.122395833333336  0 29.74262152777778  0 28.134114583333336  C 0 26.879774305555554  0.06419270833333349 25.695529513888886  0.19257812500000018 24.581380208333332  C 0.32096354166666685 23.46723090277777  0.5588541666666668 22.345703125  0.9062500000000002 21.216796875  C 1.2536458333333338 20.087890625  1.6954427083333337 19.12131076388889  2.231640625 18.317057291666668  C 2.7678385416666673 17.512803819444443  3.477734375 16.856119791666664  4.361328125 16.347005208333336  C 5.244921874999999 15.837890625  6.260677083333334 15.583333333333334  7.4085937500000005 15.583333333333334  C 9.387239583333335 17.47222222222222  11.75104166666667 18.416666666666668  14.5 18.416666666666668  C 17.248958333333338 18.416666666666668  19.61276041666667 17.47222222222222  21.59140625 15.583333333333334  C 22.73932291666667 15.583333333333334  23.755078124999997 15.837890625  24.638671875 16.347005208333336  C 25.522265625000003 16.856119791666664  26.232161458333337 17.512803819444443  26.768359375000003 18.317057291666668  C 27.30455729166667 19.12131076388889  27.746354166666666 20.087890625  28.09375 21.216796875  C 28.441145833333337 22.345703125  28.679036458333336 23.46723090277777  28.807421875000003 24.581380208333332  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 370 760 )" class="stroke" mask="url(#u1261_img_cl50)" />
    <path d="M 20.651171875000003 2.490234375  C 22.350390625000003 4.150390625  23.200000000000003 6.153645833333335  23.200000000000003 8.5  C 23.200000000000003 10.846354166666666  22.350390625000003 12.849609375  20.651171875000003 14.509765625  C 18.951953125000003 16.169921875  16.901562499999997 17  14.5 17  C 12.0984375 17  10.048046875 16.169921875  8.348828125 14.509765625  C 6.649609375000001 12.849609375  5.800000000000001 10.846354166666666  5.800000000000001 8.5  C 5.800000000000001 6.153645833333335  6.649609375000001 4.150390625  8.348828125 2.490234375  C 10.048046875 0.830078125  12.0984375 0  14.5 0  C 16.901562499999997 0  18.951953125000003 0.830078125  20.651171875000003 2.490234375  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 370 760 )" class="stroke" mask="url(#u1261_img_cl50)" />
  </g>
          </svg>
          <div id="u1261_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1262" class="ax_default label transition notrs">
          <div id="u1262_div" class=""></div>
          <div id="u1262_text" class="text ">
            <p><span>我的</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1263" class="ax_default" data-left="188" data-top="760" data-width="36" data-height="56" layer-opacity="1">

        <!-- Unnamed (形状) -->
        <div id="u1264" class="ax_default icon transition notrs">
          <svg data="images/首页（学生端）/u44.svg" id="u1264_img" class="img generatedImage">

  <defs>
    <pattern id="u1264_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u1264_img_cl49">
      <path d="M 29.11276332094176 16.568199532346064  C 29.126807104502273 16.598337230449467  29.133828996282528 16.643543777604574  29 16.70381917381138  L 29 27.553390491036634  C 29.133828996282528 27.945180566380877  29.000413052457667 28.284229670044166  28.73358116480793 28.5705378020265  C 28.4667492771582 28.856845934008835  28.15076414704668 29  27.785625774473356 29  L 19.696406443618336 29  L 19.696406443618336 20.3203429462198  L 14.30359355638166 20.3203429462198  L 14.30359355638166 29  L 6.214374225526642 29  C 5.849235852953326 29  5.533250722841801 28.856845934008835  5.266418835192069 28.5705378020265  C 4.999586947542339 28.284229670044166  4.866171003717472 27.945180566380877  5 27.553390491036634  L 5 16.70381917381138  C 4.866171003717472 16.688750324759678  4.8696819496076005 16.666147051182126  4.876703841387856 16.63600935307872  C 4.883725733168113 16.605871654975317  4.88723667905824 16.583268381397765  4.88723667905824 16.568199532346064  L 17 5.854247856586126  L 29.11276332094176 16.568199532346064  Z M 33.97893432465923 14.613016367887765  C 33.992978108219745 14.816445830085737  33.94382486575795 14.993504806443232  33.83147459727385 15.144193296960252  L 32.52540272614622 16.816835541699145  C 32.413052457662126 16.95245518316446  32.265592730276744 17.035333852948817  32.08302354399009 17.065471551052223  L 32.01982651796778 17.065471551052223  C 31.837257331681126 17.065471551052223  31.689797604295748 17.012730579371265  31.577447335811648 16.90724863600935  L 17 3.8651597817614967  L 2.422552664188352 16.90724863600935  C 2.254027261462206 17.027799428422963  2.0855018587360594 17.08054040010392  1.9169764560099132 17.065471551052223  C 1.7344072697232549 17.035333852948817  1.5869475423378772 16.95245518316446  1.4745972738537794 16.816835541699145  L 0.16852540272614622 15.144193296960252  C 0.056175134242048765 14.993504806443232  0.007021891780256142 14.816445830085737  0.021065675340768277 14.613016367887765  C 0.03510945890128057 14.40958690568979  0.11235026848409753 14.247596778383995  0.2527881040892193 14.127045985970382  L 15.39900867410161 0.5876851130163687  C 15.848409748038002 0.1958950376721197  16.382073523337464 0  17 0  C 17.61792647666254 0  18.151590251962002 0.1958950376721197  18.600991325898388 0.5876851130163687  L 23.741016109045848 5.198752922837099  L 23.741016109045848 0.79111457521434  C 23.741016109045848 0.5801506884905139  23.804213135068153 0.40685892439594584  23.930607187112763 0.2712392829306327  C 24.057001239157373 0.13561964146531635  24.218504750103264 0.06780982073265818  24.415117719950434 0.06780982073265818  L 28.459727385377946 0.06780982073265818  C 28.656340355225115 0.06780982073265818  28.817843866171003 0.13561964146531635  28.944237918215613 0.2712392829306327  C 29.070631970260223 0.40685892439594584  29.133828996282528 0.5801506884905139  29.133828996282528 0.79111457521434  L 29.133828996282528 10.013250194855807  L 33.74721189591078 14.127045985970382  C 33.88764973151591 14.247596778383995  33.964890541098725 14.40958690568979  33.97893432465923 14.613016367887765  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -49 -760 )">
    <path d="M 29.11276332094176 16.568199532346064  C 29.126807104502273 16.598337230449467  29.133828996282528 16.643543777604574  29 16.70381917381138  L 29 27.553390491036634  C 29.133828996282528 27.945180566380877  29.000413052457667 28.284229670044166  28.73358116480793 28.5705378020265  C 28.4667492771582 28.856845934008835  28.15076414704668 29  27.785625774473356 29  L 19.696406443618336 29  L 19.696406443618336 20.3203429462198  L 14.30359355638166 20.3203429462198  L 14.30359355638166 29  L 6.214374225526642 29  C 5.849235852953326 29  5.533250722841801 28.856845934008835  5.266418835192069 28.5705378020265  C 4.999586947542339 28.284229670044166  4.866171003717472 27.945180566380877  5 27.553390491036634  L 5 16.70381917381138  C 4.866171003717472 16.688750324759678  4.8696819496076005 16.666147051182126  4.876703841387856 16.63600935307872  C 4.883725733168113 16.605871654975317  4.88723667905824 16.583268381397765  4.88723667905824 16.568199532346064  L 17 5.854247856586126  L 29.11276332094176 16.568199532346064  Z M 33.97893432465923 14.613016367887765  C 33.992978108219745 14.816445830085737  33.94382486575795 14.993504806443232  33.83147459727385 15.144193296960252  L 32.52540272614622 16.816835541699145  C 32.413052457662126 16.95245518316446  32.265592730276744 17.035333852948817  32.08302354399009 17.065471551052223  L 32.01982651796778 17.065471551052223  C 31.837257331681126 17.065471551052223  31.689797604295748 17.012730579371265  31.577447335811648 16.90724863600935  L 17 3.8651597817614967  L 2.422552664188352 16.90724863600935  C 2.254027261462206 17.027799428422963  2.0855018587360594 17.08054040010392  1.9169764560099132 17.065471551052223  C 1.7344072697232549 17.035333852948817  1.5869475423378772 16.95245518316446  1.4745972738537794 16.816835541699145  L 0.16852540272614622 15.144193296960252  C 0.056175134242048765 14.993504806443232  0.007021891780256142 14.816445830085737  0.021065675340768277 14.613016367887765  C 0.03510945890128057 14.40958690568979  0.11235026848409753 14.247596778383995  0.2527881040892193 14.127045985970382  L 15.39900867410161 0.5876851130163687  C 15.848409748038002 0.1958950376721197  16.382073523337464 0  17 0  C 17.61792647666254 0  18.151590251962002 0.1958950376721197  18.600991325898388 0.5876851130163687  L 23.741016109045848 5.198752922837099  L 23.741016109045848 0.79111457521434  C 23.741016109045848 0.5801506884905139  23.804213135068153 0.40685892439594584  23.930607187112763 0.2712392829306327  C 24.057001239157373 0.13561964146531635  24.218504750103264 0.06780982073265818  24.415117719950434 0.06780982073265818  L 28.459727385377946 0.06780982073265818  C 28.656340355225115 0.06780982073265818  28.817843866171003 0.13561964146531635  28.944237918215613 0.2712392829306327  C 29.070631970260223 0.40685892439594584  29.133828996282528 0.5801506884905139  29.133828996282528 0.79111457521434  L 29.133828996282528 10.013250194855807  L 33.74721189591078 14.127045985970382  C 33.88764973151591 14.247596778383995  33.964890541098725 14.40958690568979  33.97893432465923 14.613016367887765  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 1)" stroke="none" transform="matrix(1 0 0 1 49 760 )" class="fill" />
    <path d="M 29.11276332094176 16.568199532346064  C 29.126807104502273 16.598337230449467  29.133828996282528 16.643543777604574  29 16.70381917381138  L 29 27.553390491036634  C 29.133828996282528 27.945180566380877  29.000413052457667 28.284229670044166  28.73358116480793 28.5705378020265  C 28.4667492771582 28.856845934008835  28.15076414704668 29  27.785625774473356 29  L 19.696406443618336 29  L 19.696406443618336 20.3203429462198  L 14.30359355638166 20.3203429462198  L 14.30359355638166 29  L 6.214374225526642 29  C 5.849235852953326 29  5.533250722841801 28.856845934008835  5.266418835192069 28.5705378020265  C 4.999586947542339 28.284229670044166  4.866171003717472 27.945180566380877  5 27.553390491036634  L 5 16.70381917381138  C 4.866171003717472 16.688750324759678  4.8696819496076005 16.666147051182126  4.876703841387856 16.63600935307872  C 4.883725733168113 16.605871654975317  4.88723667905824 16.583268381397765  4.88723667905824 16.568199532346064  L 17 5.854247856586126  L 29.11276332094176 16.568199532346064  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 49 760 )" class="stroke" mask="url(#u1264_img_cl49)" />
    <path d="M 33.97893432465923 14.613016367887765  C 33.992978108219745 14.816445830085737  33.94382486575795 14.993504806443232  33.83147459727385 15.144193296960252  L 32.52540272614622 16.816835541699145  C 32.413052457662126 16.95245518316446  32.265592730276744 17.035333852948817  32.08302354399009 17.065471551052223  L 32.01982651796778 17.065471551052223  C 31.837257331681126 17.065471551052223  31.689797604295748 17.012730579371265  31.577447335811648 16.90724863600935  L 17 3.8651597817614967  L 2.422552664188352 16.90724863600935  C 2.254027261462206 17.027799428422963  2.0855018587360594 17.08054040010392  1.9169764560099132 17.065471551052223  C 1.7344072697232549 17.035333852948817  1.5869475423378772 16.95245518316446  1.4745972738537794 16.816835541699145  L 0.16852540272614622 15.144193296960252  C 0.056175134242048765 14.993504806443232  0.007021891780256142 14.816445830085737  0.021065675340768277 14.613016367887765  C 0.03510945890128057 14.40958690568979  0.11235026848409753 14.247596778383995  0.2527881040892193 14.127045985970382  L 15.39900867410161 0.5876851130163687  C 15.848409748038002 0.1958950376721197  16.382073523337464 0  17 0  C 17.61792647666254 0  18.151590251962002 0.1958950376721197  18.600991325898388 0.5876851130163687  L 23.741016109045848 5.198752922837099  L 23.741016109045848 0.79111457521434  C 23.741016109045848 0.5801506884905139  23.804213135068153 0.40685892439594584  23.930607187112763 0.2712392829306327  C 24.057001239157373 0.13561964146531635  24.218504750103264 0.06780982073265818  24.415117719950434 0.06780982073265818  L 28.459727385377946 0.06780982073265818  C 28.656340355225115 0.06780982073265818  28.817843866171003 0.13561964146531635  28.944237918215613 0.2712392829306327  C 29.070631970260223 0.40685892439594584  29.133828996282528 0.5801506884905139  29.133828996282528 0.79111457521434  L 29.133828996282528 10.013250194855807  L 33.74721189591078 14.127045985970382  C 33.88764973151591 14.247596778383995  33.964890541098725 14.40958690568979  33.97893432465923 14.613016367887765  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 49 760 )" class="stroke" mask="url(#u1264_img_cl49)" />
  </g>
          </svg>
          <div id="u1264_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1265" class="ax_default label transition notrs">
          <div id="u1265_div" class=""></div>
          <div id="u1265_text" class="text ">
            <p><span>消息</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1266" class="ax_default box_2 transition notrs">
        <div id="u1266_div" class=""></div>
        <div id="u1266_text" class="text ">
          <p><span>其他</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1267" class="ax_default" data-left="24.99941039141416" data-top="493" data-width="424.0011792171716" data-height="208" layer-opacity="1">

        <!-- Unnamed (组合) -->
        <div id="u1268" class="ax_default" data-left="26" data-top="493" data-width="421" data-height="155" layer-opacity="1">

          <!-- Unnamed (图片) -->
          <div id="u1269" class="ax_default _图片 transition notrs">
            <img id="u1269_img" class="img " src="images/首页（学生端）/u21.png"/>
            <div id="u1269_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1270" class="ax_default label transition notrs">
            <div id="u1270_div" class=""></div>
            <div id="u1270_text" class="text ">
              <p><span>张伟 | 本科| 心理学</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1271" class="ax_default label transition notrs">
            <div id="u1271_div" class=""></div>
            <div id="u1271_text" class="text ">
              <p><span>2025-3-12 11:12</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1272" class="ax_default label transition notrs">
            <div id="u1272_div" class=""></div>
            <div id="u1272_text" class="text ">
              <p><span>需要本科论文辅导需要本科论文辅导需要本科论文辅导需要本科</span></p><p><span>论文辅导需要本科论文辅导需要本科论文辅导需要本科论文辅导导</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1273" class="ax_default label transition notrs">
            <div id="u1273_div" class=""></div>
            <div id="u1273_text" class="text ">
              <p><span>博士</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1274" class="ax_default label transition notrs">
            <div id="u1274_div" class=""></div>
            <div id="u1274_text" class="text ">
              <p><span>老师要求：</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u1275" class="ax_default label transition notrs">
            <div id="u1275_div" class=""></div>
            <div id="u1275_text" class="text ">
              <p><span>预算：10000-20000</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u1276" class="ax_default line transition notrs">
          <svg data="images/首页（学生端）/u30.svg" id="u1276_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -24 -454 )">
    <path d="M 0 0.5  L 424 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(215, 215, 215, 1)" fill="none" transform="matrix(1 0 0 1 24 454 )" class="stroke" />
  </g>
          </svg>
          <div id="u1276_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1277" class="ax_default primary_button transition notrs">
          <div id="u1277_div" class=""></div>
          <div id="u1277_text" class="text ">
            <p><span>已成交</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1278" class="ax_default sticky_1 transition notrs">
        <div id="u1278_div" class=""></div>
        <div id="u1278_text" class="text ">
          <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"><br></span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">可以学生推广、老师招募推广、广告推广、期刊合作等</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1279" class="ax_default label transition notrs">
        <div id="u1279_div" class=""></div>
        <div id="u1279_text" class="text ">
          <p><span>论文辅导</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1280" class="ax_default label transition notrs">
        <div id="u1280_div" class=""></div>
        <div id="u1280_text" class="text ">
          <p><span>作业辅导</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1281" class="ax_default label transition notrs">
        <div id="u1281_div" class=""></div>
        <div id="u1281_text" class="text ">
          <p><span>已有20人投递</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1282" class="ax_default label transition notrs">
        <div id="u1282_div" class=""></div>
        <div id="u1282_text" class="text ">
          <p><span>已有10人投递</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1283" class="ax_default sticky_1 transition notrs">
        <div id="u1283_div" class=""></div>
        <div id="u1283_text" class="text ">
          <p><span>推荐规则：</span></p><p><span>1、辅导项目匹配</span></p><p><span>2、专业</span></p><p><span>3、老师要求</span></p><p><span>排序：按照发布时间倒序</span></p><p><span><br></span></p><p><span>中间显示需求描述，多余50字显示…</span></p><p><span><br></span></p><p><span>订单状态：非删除的订单</span></p><p><span><br></span></p><p><span><br></span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (热区) -->
      <div id="u1284" class="ax_default">
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1285" class="ax_default sticky_4 transition notrs">
        <div id="u1285_div" class=""></div>
        <div id="u1285_text" class="text ">
          <p><span>消息主要发站内消息</span></p><p><span>比如：被拒绝了，订单取消了、被复制微信了、支付了等</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1286" class="ax_default sticky_1 transition notrs">
        <div id="u1286_div" class=""></div>
        <div id="u1286_text" class="text ">
          <p><span>关于投递按钮</span></p><p><span>1、如果已经投递，按钮显示“已投递”，按钮置灰不可点</span></p><p><span>如果订单是交付中或已完成，显示“已投递”，按钮置灰不可点</span></p><p><span>2、投递时需要校验是否认证，如果无认证，给弹框提示，引导进行实名认证</span></p><p><span>3、个人限制每人每天投递10个，投递的时候做校验，如果超过10个人，toast提示：个人限制每天投递10个，请明天再投递。</span></p>
        </div>
      </div>

      <!-- 引导实名认证 (动态面板) -->
      <div id="u1287" class="ax_default ax_default_hidden" data-label="引导实名认证" style="display:none; visibility: hidden">
        <div id="u1287_state0" class="panel_state" data-label="State 1" style="">
          <div id="u1287_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u1288" class="ax_default box_1 transition notrs">
              <div id="u1288_div" class=""></div>
              <div id="u1288_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1289" class="ax_default label transition notrs">
              <div id="u1289_div" class=""></div>
              <div id="u1289_text" class="text ">
                <p><span>还未实名认证，无法投递。</span></p><p><span>请先实名认证后再投递。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1290" class="ax_default primary_button transition notrs">
              <div id="u1290_div" class=""></div>
              <div id="u1290_text" class="text ">
                <p><span>去认证</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u1291" class="ax_default label transition notrs">
              <div id="u1291_div" class=""></div>
              <div id="u1291_text" class="text ">
                <p><span>×</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
