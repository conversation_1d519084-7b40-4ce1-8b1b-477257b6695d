﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单列表</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单列表/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单列表/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u239" class="ax_default box_1 transition notrs">
        <div id="u239_div" class=""></div>
        <div id="u239_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u240" class="ax_default box_1 transition notrs">
        <div id="u240_div" class=""></div>
        <div id="u240_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u241" class="ax_default label transition notrs">
        <div id="u241_div" class=""></div>
        <div id="u241_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">&nbsp; 订单号：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">FD13455555333&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u242" class="ax_default" data-left="329" data-top="223" data-width="106" data-height="34" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u243" class="ax_default primary_button transition notrs">
          <div id="u243_div" class=""></div>
          <div id="u243_text" class="text ">
            <p><span>报名：30</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u244" class="ax_default label transition notrs">
        <div id="u244_div" class=""></div>
        <div id="u244_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u245" class="ax_default label transition notrs">
        <div id="u245_div" class=""></div>
        <div id="u245_text" class="text ">
          <p><span>我的订单</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u246" class="ax_default _图片 transition notrs">
        <img id="u246_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u246_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u247" class="ax_default label transition notrs">
        <div id="u247_div" class=""></div>
        <div id="u247_text" class="text ">
          <p><span>待接单</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u248" class="ax_default label transition notrs">
        <div id="u248_div" class=""></div>
        <div id="u248_text" class="text ">
          <p><span>交付中</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u249" class="ax_default label transition notrs">
        <div id="u249_div" class=""></div>
        <div id="u249_text" class="text ">
          <p><span>待评价</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u250" class="ax_default label transition notrs">
        <div id="u250_div" class=""></div>
        <div id="u250_text" class="text ">
          <p><span>已评价</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u251" class="ax_default line transition notrs">
        <svg data="images/订单列表/u251.svg" id="u251_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -43.5 -110.5 )">
    <path d="M 0 1.5  L 54 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 45 112 )" class="stroke" />
  </g>
        </svg>
        <div id="u251_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u252" class="ax_default sticky_1 transition notrs">
        <div id="u252_div" class=""></div>
        <div id="u252_text" class="text ">
          <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;"><br></span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">订单状态：待接单、交付中、待评价、已评价</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">待接单：发布后即为待接单状态</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">交付中：确认老师之后即为交付中</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">待评价：交付完成之后即为待评价，交付中取消也为待评价</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">已评价：评价之后几位已评价</span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;color:#D9001B;"><br></span></p><p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;color:#D9001B;"><br></span></p>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u253" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u253_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u253_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u254" class="ax_default box_1 transition notrs">
              <div id="u254_div" class=""></div>
              <div id="u254_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u255" class="ax_default label transition notrs">
              <div id="u255_div" class=""></div>
              <div id="u255_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u256" class="ax_default label transition notrs">
              <div id="u256_div" class=""></div>
              <div id="u256_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u257" class="ax_default button transition notrs">
              <div id="u257_div" class=""></div>
              <div id="u257_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u258" class="ax_default primary_button transition notrs">
              <div id="u258_div" class=""></div>
              <div id="u258_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u253_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u253_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u259" class="ax_default box_1 transition notrs">
              <div id="u259_div" class=""></div>
              <div id="u259_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u260" class="ax_default label transition notrs">
              <div id="u260_div" class=""></div>
              <div id="u260_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u261" class="ax_default label transition notrs">
              <div id="u261_div" class=""></div>
              <div id="u261_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u262" class="ax_default button transition notrs">
              <div id="u262_div" class=""></div>
              <div id="u262_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u263" class="ax_default primary_button transition notrs">
              <div id="u263_div" class=""></div>
              <div id="u263_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u264" class="ax_default label transition notrs">
              <div id="u264_div" class=""></div>
              <div id="u264_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u265" class="ax_default text_field transition notrs">
              <div id="u265_div" class=""></div>
              <input id="u265_input" type="text" value="" class="u265_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u266" class="ax_default label transition notrs">
              <div id="u266_div" class=""></div>
              <div id="u266_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u267" class="ax_default" data-left="28" data-top="175" data-width="107" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u268" class="ax_default label transition notrs">
          <div id="u268_div" class=""></div>
          <div id="u268_text" class="text ">
            <p><span>辅导项目：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u269" class="ax_default shape transition notrs">
          <div id="u269_div" class=""></div>
          <div id="u269_text" class="text ">
            <p><span>作业</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u270" class="ax_default" data-left="301" data-top="175" data-width="145" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u271" class="ax_default label transition notrs">
          <div id="u271_div" class=""></div>
          <div id="u271_text" class="text ">
            <p><span>预算区间：1000-2000元</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (椭圆) -->
      <div id="u272" class="ax_default ellipse transition notrs">
        <svg data="images/订单列表/u272.svg" id="u272_img" class="img generatedImage">

  <defs>
    <pattern id="u272_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u272_img_cl52">
      <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -28 -226 )">
    <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 28 226 )" class="fill" />
    <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 28 226 )" class="stroke" mask="url(#u272_img_cl52)" />
  </g>
        </svg>
        <div id="u272_text" class="text ">
          <p><span>头像</span></p>
        </div>
      </div>

      <!-- Unnamed (椭圆) -->
      <div id="u273" class="ax_default ellipse transition notrs">
        <svg data="images/订单列表/u272.svg" id="u273_img" class="img generatedImage">

  <defs>
    <pattern id="u273_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u273_img_cl52">
      <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -28 -226 )">
    <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 28 226 )" class="fill" />
    <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 28 226 )" class="stroke" mask="url(#u273_img_cl52)" />
  </g>
        </svg>
        <div id="u273_text" class="text ">
          <p><span>头像</span></p>
        </div>
      </div>

      <!-- Unnamed (椭圆) -->
      <div id="u274" class="ax_default ellipse transition notrs">
        <svg data="images/订单列表/u272.svg" id="u274_img" class="img generatedImage">

  <defs>
    <pattern id="u274_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u274_img_cl52">
      <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -28 -226 )">
    <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 28 226 )" class="fill" />
    <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 28 226 )" class="stroke" mask="url(#u274_img_cl52)" />
  </g>
        </svg>
        <div id="u274_text" class="text ">
          <p><span>头像</span></p>
        </div>
      </div>

      <!-- Unnamed (椭圆) -->
      <div id="u275" class="ax_default ellipse transition notrs">
        <svg data="images/订单列表/u272.svg" id="u275_img" class="img generatedImage">

  <defs>
    <pattern id="u275_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u275_img_cl52">
      <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -28 -226 )">
    <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 28 226 )" class="fill" />
    <path d="M 0 16  C 0 7.039999999999999  7.259999999999999 0  16.5 0  C 25.740000000000002 0  33 7.039999999999999  33 16  C 33 24.96  25.740000000000002 32  16.5 32  C 7.259999999999999 32  0 24.96  0 16  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 28 226 )" class="stroke" mask="url(#u275_img_cl52)" />
  </g>
        </svg>
        <div id="u275_text" class="text ">
          <p><span>头像</span></p>
        </div>
      </div>

      <!-- Unnamed (表格) -->
      <div id="u276" class="ax_default">

        <!-- Unnamed (单元格) -->
        <div id="u277" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u277.svg" id="u277_img" class="img generatedImage" viewbox="0 0 100 55">

  <path d="M 1 1  L 100 1  L 100 55  L 1 55  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" class="fill" />
  <path d="M 0.5 1  L 0.5 55  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" class="stroke" />
  <path d="M 0 0.5  L 100 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" class="stroke" />
          </svg>
          <div id="u277_text" class="text ">
            <p><span>待接单</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u278" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u278.svg" id="u278_img" class="img generatedImage" viewbox="100 0 325 55">

  <path d="M 1 1  L 324 1  L 324 55  L 1 55  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 100 0 )" class="fill" />
  <path d="M 0.5 1  L 0.5 55  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 0 )" class="stroke" />
  <path d="M 0 0.5  L 325 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 0 )" class="stroke" />
  <path d="M 324.5 1  L 324.5 55  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 0 )" class="stroke" />
          </svg>
          <div id="u278_text" class="text ">
            <p><span>按创建时间倒序，可分页，每页10个，前期可先不做分页，后面再做</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u279" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u279.svg" id="u279_img" class="img generatedImage" viewbox="0 55 100 30">

  <path d="M 1 1  L 100 1  L 100 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 55 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 55 )" class="stroke" />
  <path d="M 0 0.5  L 100 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 55 )" class="stroke" />
          </svg>
          <div id="u279_text" class="text ">
            <p><span>订单号</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u280" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u280.svg" id="u280_img" class="img generatedImage" viewbox="100 55 325 30">

  <path d="M 1 1  L 324 1  L 324 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 100 55 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 55 )" class="stroke" />
  <path d="M 0 0.5  L 325 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 55 )" class="stroke" />
  <path d="M 324.5 1  L 324.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 55 )" class="stroke" />
          </svg>
          <div id="u280_text" class="text ">
            <p><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">FD+</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">年月日</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">+</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">四位随机数，不累加</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u281" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u281.svg" id="u281_img" class="img generatedImage" viewbox="0 85 100 30">

  <path d="M 1 1  L 100 1  L 100 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 85 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 85 )" class="stroke" />
  <path d="M 0 0.5  L 100 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 85 )" class="stroke" />
          </svg>
          <div id="u281_text" class="text ">
            <p><span>发布时间</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u282" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u282.svg" id="u282_img" class="img generatedImage" viewbox="100 85 325 30">

  <path d="M 1 1  L 324 1  L 324 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 100 85 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 85 )" class="stroke" />
  <path d="M 0 0.5  L 325 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 85 )" class="stroke" />
  <path d="M 324.5 1  L 324.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 85 )" class="stroke" />
          </svg>
          <div id="u282_text" class="text ">
            <p><span>精确到时分秒</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u283" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u283.svg" id="u283_img" class="img generatedImage" viewbox="0 115 100 30">

  <path d="M 1 1  L 100 1  L 100 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 115 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 115 )" class="stroke" />
  <path d="M 0 0.5  L 100 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 115 )" class="stroke" />
          </svg>
          <div id="u283_text" class="text ">
            <p><span>辅导项目</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u284" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u284.svg" id="u284_img" class="img generatedImage" viewbox="100 115 325 30">

  <path d="M 1 1  L 324 1  L 324 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 100 115 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 115 )" class="stroke" />
  <path d="M 0 0.5  L 325 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 115 )" class="stroke" />
  <path d="M 324.5 1  L 324.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 115 )" class="stroke" />
          </svg>
          <div id="u284_text" class="text ">
            <p><span>显示填写的内容</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u285" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u285.svg" id="u285_img" class="img generatedImage" viewbox="0 145 100 30">

  <path d="M 1 1  L 100 1  L 100 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 145 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 145 )" class="stroke" />
  <path d="M 0 0.5  L 100 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 145 )" class="stroke" />
          </svg>
          <div id="u285_text" class="text ">
            <p><span>预算区间</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u286" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u286.svg" id="u286_img" class="img generatedImage" viewbox="100 145 325 30">

  <path d="M 1 1  L 324 1  L 324 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 100 145 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 145 )" class="stroke" />
  <path d="M 0 0.5  L 325 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 145 )" class="stroke" />
  <path d="M 324.5 1  L 324.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 145 )" class="stroke" />
          </svg>
          <div id="u286_text" class="text ">
            <p><span>显示填写的内容</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u287" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u287.svg" id="u287_img" class="img generatedImage" viewbox="0 175 100 30">

  <path d="M 1 1  L 100 1  L 100 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 175 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 175 )" class="stroke" />
  <path d="M 0 0.5  L 100 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 175 )" class="stroke" />
          </svg>
          <div id="u287_text" class="text ">
            <p><span>报名头像</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u288" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u288.svg" id="u288_img" class="img generatedImage" viewbox="100 175 325 30">

  <path d="M 1 1  L 324 1  L 324 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 100 175 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 175 )" class="stroke" />
  <path d="M 0 0.5  L 325 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 175 )" class="stroke" />
  <path d="M 324.5 1  L 324.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 175 )" class="stroke" />
          </svg>
          <div id="u288_text" class="text ">
            <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">显示</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">5</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">个就行</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u289" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u289.svg" id="u289_img" class="img generatedImage" viewbox="0 205 100 30">

  <path d="M 1 1  L 100 1  L 100 29  L 1 29  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 205 )" class="fill" />
  <path d="M 0.5 1  L 0.5 29  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 205 )" class="stroke" />
  <path d="M 0 0.5  L 100 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 205 )" class="stroke" />
  <path d="M 0 29.5  L 100 29.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 205 )" class="stroke" />
          </svg>
          <div id="u289_text" class="text ">
            <p><span>报名人数</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u290" class="ax_default table_cell transition notrs">
          <svg data="images/订单列表/u290.svg" id="u290_img" class="img generatedImage" viewbox="100 205 325 30">

  <path d="M 1 1  L 324 1  L 324 29  L 1 29  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 100 205 )" class="fill" />
  <path d="M 0.5 1  L 0.5 29  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 205 )" class="stroke" />
  <path d="M 0 0.5  L 325 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 205 )" class="stroke" />
  <path d="M 324.5 1  L 324.5 29  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 205 )" class="stroke" />
  <path d="M 0 29.5  L 325 29.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 100 205 )" class="stroke" />
          </svg>
          <div id="u290_text" class="text ">
            <p><span>已报名人数</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (热区) -->
      <div id="u291" class="ax_default">
      </div>

      <!-- Unnamed (热区) -->
      <div id="u292" class="ax_default">
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
