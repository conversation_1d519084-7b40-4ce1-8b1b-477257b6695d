﻿<!DOCTYPE html>
<html>
  <head>
    <title>问题记录</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/问题记录/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/问题记录/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (表格) -->
      <div id="u2312" class="ax_default">

        <!-- Unnamed (单元格) -->
        <div id="u2313" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2313.svg" id="u2313_img" class="img generatedImage" viewbox="0 0 130 30">

  <path d="M 1 1  L 130 1  L 130 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" class="stroke" />
  <path d="M 0 0.5  L 130 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" class="stroke" />
          </svg>
          <div id="u2313_text" class="text ">
            <p><span>问题</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2314" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2314.svg" id="u2314_img" class="img generatedImage" viewbox="130 0 232 30">

  <path d="M 1 1  L 232 1  L 232 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 130 0 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 0 )" class="stroke" />
  <path d="M 0 0.5  L 232 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 0 )" class="stroke" />
          </svg>
          <div id="u2314_text" class="text ">
            <p><span>方案</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2315" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2315.svg" id="u2315_img" class="img generatedImage" viewbox="362 0 208 30">

  <path d="M 1 1  L 207 1  L 207 30  L 1 30  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 362 0 )" class="fill" />
  <path d="M 0.5 1  L 0.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 0 )" class="stroke" />
  <path d="M 0 0.5  L 208 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 0 )" class="stroke" />
  <path d="M 207.5 1  L 207.5 30  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 0 )" class="stroke" />
          </svg>
          <div id="u2315_text" class="text ">
            <p><span>备注</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2316" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2316.svg" id="u2316_img" class="img generatedImage" viewbox="0 30 130 70">

  <path d="M 1 1  L 130 1  L 130 70  L 1 70  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 30 )" class="fill" />
  <path d="M 0.5 1  L 0.5 70  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 30 )" class="stroke" />
  <path d="M 0 0.5  L 130 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 30 )" class="stroke" />
          </svg>
          <div id="u2316_text" class="text ">
            <p><span>学生不知道价格？</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2317" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2317.svg" id="u2317_img" class="img generatedImage" viewbox="130 30 232 70">

  <path d="M 1 1  L 232 1  L 232 70  L 1 70  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 130 30 )" class="fill" />
  <path d="M 0.5 1  L 0.5 70  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 30 )" class="stroke" />
  <path d="M 0 0.5  L 232 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 30 )" class="stroke" />
          </svg>
          <div id="u2317_text" class="text ">
            <p><span>老师自己报价、学生报价</span></p><p><span>平台给出价格参考</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2318" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2318.svg" id="u2318_img" class="img generatedImage" viewbox="362 30 208 70">

  <path d="M 1 1  L 207 1  L 207 70  L 1 70  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 362 30 )" class="fill" />
  <path d="M 0.5 1  L 0.5 70  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 30 )" class="stroke" />
  <path d="M 0 0.5  L 208 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 30 )" class="stroke" />
  <path d="M 207.5 1  L 207.5 70  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 30 )" class="stroke" />
          </svg>
          <div id="u2318_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2319" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2319.svg" id="u2319_img" class="img generatedImage" viewbox="0 100 130 71">

  <path d="M 1 1  L 130 1  L 130 71  L 1 71  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 100 )" class="fill" />
  <path d="M 0.5 1  L 0.5 71  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 100 )" class="stroke" />
  <path d="M 0 0.5  L 130 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 100 )" class="stroke" />
          </svg>
          <div id="u2319_text" class="text ">
            <p><span>是否可以切换角色？</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2320" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2320.svg" id="u2320_img" class="img generatedImage" viewbox="130 100 232 71">

  <path d="M 1 1  L 232 1  L 232 71  L 1 71  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 130 100 )" class="fill" />
  <path d="M 0.5 1  L 0.5 71  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 100 )" class="stroke" />
  <path d="M 0 0.5  L 232 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 100 )" class="stroke" />
          </svg>
          <div id="u2320_text" class="text ">
            <p><span>默认都是学生角色，可以认证为老师，认证后暂时不能切换为学生。</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2321" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2321.svg" id="u2321_img" class="img generatedImage" viewbox="362 100 208 71">

  <path d="M 1 1  L 207 1  L 207 71  L 1 71  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 362 100 )" class="fill" />
  <path d="M 0.5 1  L 0.5 71  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 100 )" class="stroke" />
  <path d="M 0 0.5  L 208 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 100 )" class="stroke" />
  <path d="M 207.5 1  L 207.5 71  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 100 )" class="stroke" />
          </svg>
          <div id="u2321_text" class="text ">
            <p><span>后期可以考虑老师切换为学生</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2322" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2322.svg" id="u2322_img" class="img generatedImage" viewbox="0 171 130 59">

  <path d="M 1 1  L 130 1  L 130 59  L 1 59  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 171 )" class="fill" />
  <path d="M 0.5 1  L 0.5 59  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 171 )" class="stroke" />
  <path d="M 0 0.5  L 130 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 171 )" class="stroke" />
          </svg>
          <div id="u2322_text" class="text ">
            <p><span>账号管理</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2323" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2323.svg" id="u2323_img" class="img generatedImage" viewbox="130 171 232 59">

  <path d="M 1 1  L 232 1  L 232 59  L 1 59  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 130 171 )" class="fill" />
  <path d="M 0.5 1  L 0.5 59  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 171 )" class="stroke" />
  <path d="M 0 0.5  L 232 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 171 )" class="stroke" />
          </svg>
          <div id="u2323_text" class="text ">
            <p><span>老师个人以身份证为唯一标识</span></p><p><span>机构以营业执照为唯一标识</span></p><p><span>学生以个人微信ID为唯一标识</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2324" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2324.svg" id="u2324_img" class="img generatedImage" viewbox="362 171 208 59">

  <path d="M 1 1  L 207 1  L 207 59  L 1 59  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 362 171 )" class="fill" />
  <path d="M 0.5 1  L 0.5 59  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 171 )" class="stroke" />
  <path d="M 0 0.5  L 208 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 171 )" class="stroke" />
  <path d="M 207.5 1  L 207.5 59  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 171 )" class="stroke" />
          </svg>
          <div id="u2324_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2325" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2325.svg" id="u2325_img" class="img generatedImage" viewbox="0 230 130 59">

  <path d="M 1 1  L 130 1  L 130 59  L 1 59  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 230 )" class="fill" />
  <path d="M 0.5 1  L 0.5 59  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 230 )" class="stroke" />
  <path d="M 0 0.5  L 130 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 230 )" class="stroke" />
          </svg>
          <div id="u2325_text" class="text ">
            <p><span>关于角色</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2326" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2326.svg" id="u2326_img" class="img generatedImage" viewbox="130 230 232 59">

  <path d="M 1 1  L 232 1  L 232 59  L 1 59  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 130 230 )" class="fill" />
  <path d="M 0.5 1  L 0.5 59  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 230 )" class="stroke" />
  <path d="M 0 0.5  L 232 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 230 )" class="stroke" />
          </svg>
          <div id="u2326_text" class="text ">
            <p><span>学生端：只有一个角色</span></p><p><span>老师端有3个：个人、机构、平台</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2327" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2327.svg" id="u2327_img" class="img generatedImage" viewbox="362 230 208 59">

  <path d="M 1 1  L 207 1  L 207 59  L 1 59  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 362 230 )" class="fill" />
  <path d="M 0.5 1  L 0.5 59  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 230 )" class="stroke" />
  <path d="M 0 0.5  L 208 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 230 )" class="stroke" />
  <path d="M 207.5 1  L 207.5 59  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 230 )" class="stroke" />
          </svg>
          <div id="u2327_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2328" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2328.svg" id="u2328_img" class="img generatedImage" viewbox="0 289 130 59">

  <path d="M 1 1  L 130 1  L 130 58  L 1 58  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 0 289 )" class="fill" />
  <path d="M 0.5 1  L 0.5 58  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 289 )" class="stroke" />
  <path d="M 0 0.5  L 130 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 289 )" class="stroke" />
  <path d="M 0 58.5  L 130 58.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 0 289 )" class="stroke" />
          </svg>
          <div id="u2328_text" class="text ">
            <p><span>区分老师可以辅导国外 论文</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2329" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2329.svg" id="u2329_img" class="img generatedImage" viewbox="130 289 232 59">

  <path d="M 1 1  L 232 1  L 232 58  L 1 58  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 130 289 )" class="fill" />
  <path d="M 0.5 1  L 0.5 58  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 289 )" class="stroke" />
  <path d="M 0 0.5  L 232 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 289 )" class="stroke" />
  <path d="M 0 58.5  L 232 58.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 130 289 )" class="stroke" />
          </svg>
          <div id="u2329_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2330" class="ax_default table_cell transition notrs">
          <svg data="images/问题记录/u2330.svg" id="u2330_img" class="img generatedImage" viewbox="362 289 208 59">

  <path d="M 1 1  L 207 1  L 207 58  L 1 58  L 1 1  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 362 289 )" class="fill" />
  <path d="M 0.5 1  L 0.5 58  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 289 )" class="stroke" />
  <path d="M 0 0.5  L 208 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 289 )" class="stroke" />
  <path d="M 207.5 1  L 207.5 58  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 289 )" class="stroke" />
  <path d="M 0 58.5  L 208 58.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 362 289 )" class="stroke" />
          </svg>
          <div id="u2330_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
