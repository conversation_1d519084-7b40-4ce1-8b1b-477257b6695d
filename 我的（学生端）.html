﻿<!DOCTYPE html>
<html>
  <head>
    <title>我的（学生端）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/我的（学生端）/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/我的（学生端）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u214" class="ax_default box_1 transition notrs">
        <div id="u214_div" class=""></div>
        <div id="u214_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u215" class="ax_default icon transition notrs">
        <svg data="images/首页（学生端）/u1.svg" id="u215_img" class="img generatedImage">

  <defs>
    <pattern id="u215_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u215_img_cl44">
      <path d="M 21.081473214285715 14.572823660714285  C 21.207217261904763 14.589006696428571  21.308779761904763 14.656436011904761  21.386160714285715 14.77511160714286  L 21.952008928571427 15.600446428571429  C 22.029389880952383 15.719122023809524  22.060825892857142 15.845889136904761  22.046316964285715 15.98074776785714  C 22.031808035714285 16.115606398809522  21.976190476190474 16.226190476190478  21.879464285714285 16.3125  C 21.463541666666668 16.679315476190478  20.851748511904763 17.210658482142858  20.044084821428573 17.906529017857142  C 19.236421130952383 18.60239955357143  18.527901785714285 19.21196056547619  17.918526785714285 19.73521205357143  C 17.309151785714285 20.258463541666664  16.985119047619047 20.53627232142857  16.946428571428573 20.568638392857142  C 16.569196428571427 20.913876488095234  16.279017857142858 21.17280505952381  16.075892857142858 21.345424107142858  C 15.872767857142856 21.5180431547619  15.580171130952383 21.73921130952381  15.198102678571429 22.00892857142857  C 14.816034226190476 22.278645833333336  14.446056547619047 22.47553943452381  14.088169642857144 22.599609375  C 13.730282738095239 22.72367931547619  13.372395833333336 22.785714285714285  13.014508928571429 22.785714285714285  L 13 22.785714285714285  L 12.985491071428571 22.785714285714285  C 12.62760416666667 22.785714285714285  12.269717261904763 22.72367931547619  11.911830357142858 22.599609375  C 11.553943452380954 22.47553943452381  11.186383928571429 22.278645833333336  10.809151785714286 22.00892857142857  C 10.431919642857142 21.73921130952381  10.136904761904763 21.515345982142858  9.924107142857142 21.337332589285715  C 9.711309523809524 21.15931919642857  9.421130952380954 20.903087797619047  9.053571428571429 20.568638392857142  C 9.005208333333334 20.52548363095238  8.690848214285714 20.25306919642857  8.110491071428571 19.751395089285715  C 7.530133928571428 19.249720982142858  6.836123511904762 18.650948660714285  6.028459821428571 17.955078125  C 5.220796130952381 17.259207589285715  4.628348214285714 16.749441964285715  4.251116071428571 16.42578125  C 4.144717261904762 16.339471726190478  4.084263392857143 16.228887648809522  4.069754464285714 16.094029017857142  C 4.055245535714286 15.959170386904761  4.0866815476190474 15.832403273809524  4.1640625 15.713727678571429  L 4.700892857142857 14.872209821428571  C 4.77827380952381 14.753534226190476  4.882254464285714 14.683407738095235  5.012834821428571 14.66183035714286  C 5.143415178571429 14.640252976190476  5.261904761904762 14.67801339285714  5.368303571428571 14.77511160714286  C 6.277529761904762 15.562686011904761  7.757440476190477 16.835751488095234  9.808035714285714 18.594308035714285  C 9.85639880952381 18.637462797619047  10.066778273809524 18.82626488095238  10.439174107142858 19.160714285714285  C 10.811569940476192 19.49516369047619  11.104166666666668 19.746000744047617  11.316964285714286 19.91322544642857  C 11.529761904761905 20.080450148809522  11.803013392857142 20.255766369047617  12.13671875 20.439174107142858  C 12.470424107142858 20.622581845238095  12.753348214285714 20.714285714285715  12.985491071428571 20.714285714285715  L 13 20.714285714285715  L 13.014508928571429 20.714285714285715  C 13.246651785714288 20.714285714285715  13.529575892857144 20.622581845238095  13.86328125 20.439174107142858  C 14.19698660714286 20.255766369047617  14.470238095238098 20.080450148809522  14.683035714285715 19.91322544642857  C 14.895833333333336 19.746000744047617  15.188430059523812 19.49516369047619  15.560825892857144 19.160714285714285  C 15.933221726190476 18.82626488095238  16.14360119047619 18.637462797619047  16.191964285714285 18.594308035714285  C 18.6875 16.45814732142857  20.201264880952383 15.152715773809524  20.733258928571427 14.67801339285714  C 20.83965773809524 14.591703869047617  20.955729166666668 14.556640625  21.081473214285715 14.572823660714285  Z M 24.005022321428573 26.774832589285715  C 24.096912202380956 26.672340029761905  24.142857142857142 26.550967261904763  24 26.410714285714285  L 24 11.392857142857144  C 23.272321428571427 10.497395833333332  22.503348214285715 9.747581845238095  21.8359375 9.143415178571429  C 20.955729166666668 8.345052083333332  19.074404761904763 6.70517113095238  16.191964285714285 4.22377232142857  C 16.162946428571427 4.202194940476188  15.95498511904762 4.013392857142856  15.568080357142856 3.6573660714285703  C 15.181175595238098 3.301339285714285  14.886160714285715 3.042410714285715  14.683035714285715 2.880580357142856  C 14.479910714285715 2.71875  14.20907738095238 2.543433779761903  13.870535714285715 2.3546316964285703  C 13.531994047619047 2.165829613095238  13.246651785714288 2.0714285714285703  13.014508928571429 2.0714285714285703  L 13 2.0714285714285703  L 12.985491071428571 2.0714285714285703  C 12.753348214285714 2.0714285714285703  12.468005952380954 2.165829613095238  12.129464285714286 2.3546316964285703  C 11.79092261904762 2.543433779761903  11.520089285714288 2.71875  11.316964285714286 2.880580357142856  C 11.113839285714286 3.042410714285715  10.818824404761905 3.301339285714285  10.431919642857142 3.6573660714285703  C 10.045014880952381 4.013392857142856  9.837053571428571 4.202194940476188  9.808035714285714 4.22377232142857  C 7.7284226190476195 6.014694940476188  6.202566964285714 7.33900669642857  5.23046875 8.196707589285715  C 4.258370535714286 9.054408482142856  3.632068452380953 9.61542038690476  3.3515625 9.879743303571429  C 3.071056547619048 10.144066220238095  2.674479166666667 10.545944940476188  2.161830357142857 11.085379464285715  C 2.0264136904761907 11.21484375  1.924851190476191 11.317336309523808  2 11.392857142857144  L 2 26.410714285714285  C 1.857142857142857 26.550967261904763  1.903087797619048 26.672340029761905  1.9949776785714286 26.774832589285715  C 2.0868675595238093 26.877325148809522  2.1956845238095237 26.92857142857143  2.3214285714285716 27  L 23.678571428571427 27  C 23.804315476190474 26.92857142857143  23.91313244047619 26.877325148809522  24.005022321428573 26.774832589285715  Z M 25.405133928571427 9.871651785714285  C 25.80171130952381 10.281622023809524  26 10.788690476190473  26 11.392857142857144  L 26 26.410714285714285  C 26 27.122767857142854  25.772693452380956 27.73232886904762  25.318080357142858 28.23939732142857  C 24.863467261904766 28.746465773809522  24.316964285714292 29  23.678571428571427 29  L 2.3214285714285716 29  C 1.6830357142857144 29  1.1365327380952381 28.746465773809522  0.6819196428571429 28.23939732142857  C 0.22730654761904762 27.73232886904762  0 27.122767857142854  0 26.410714285714285  L 0 11.392857142857144  C 0 10.788690476190473  0.19828869047619047 10.281622023809524  0.5948660714285714 9.871651785714285  C 1.7845982142857144 8.641741071428571  3.477306547619047 7.074683779761902  5.672991071428571 5.170479910714286  C 7.868675595238096 3.2662760416666643  8.995535714285714 2.287202380952379  9.053571428571429 2.2332589285714297  C 9.401785714285714 1.9095982142857149  9.687127976190478 1.653366815476188  9.909598214285714 1.4645647321428559  C 10.132068452380954 1.2757626488095235  10.429501488095237 1.0492001488095235  10.801897321428571 0.7848772321428558  C 11.174293154761905 0.520554315476188  11.541852678571429 0.32366071428571475  11.904575892857142 0.1941964285714295  C 12.26729910714286 0.06473214285714424  12.62760416666667 0  12.985491071428571 0  L 13 0  L 13.014508928571429 0  C 13.372395833333336 0  13.732700892857144 0.06473214285714424  14.095424107142856 0.1941964285714295  C 14.458147321428571 0.32366071428571475  14.825706845238095 0.520554315476188  15.198102678571429 0.7848772321428558  C 15.570498511904763 1.0492001488095235  15.867931547619047 1.2757626488095235  16.090401785714285 1.4645647321428559  C 16.312872023809526 1.653366815476188  16.598214285714285 1.9095982142857149  16.946428571428573 2.2332589285714297  C 17.36235119047619 2.621651785714285  18.116815476190474 3.279761904761903  19.209821428571427 4.207589285714286  C 20.302827380952383 5.135416666666664  21.395833333333336 6.090215773809524  22.488839285714285 7.071986607142856  C 23.58184523809524 8.053757440476188  24.553943452380956 8.986979166666664  25.405133928571427 9.871651785714285  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 174 3226 )">
    <path d="M 21.081473214285715 14.572823660714285  C 21.207217261904763 14.589006696428571  21.308779761904763 14.656436011904761  21.386160714285715 14.77511160714286  L 21.952008928571427 15.600446428571429  C 22.029389880952383 15.719122023809524  22.060825892857142 15.845889136904761  22.046316964285715 15.98074776785714  C 22.031808035714285 16.115606398809522  21.976190476190474 16.226190476190478  21.879464285714285 16.3125  C 21.463541666666668 16.679315476190478  20.851748511904763 17.210658482142858  20.044084821428573 17.906529017857142  C 19.236421130952383 18.60239955357143  18.527901785714285 19.21196056547619  17.918526785714285 19.73521205357143  C 17.309151785714285 20.258463541666664  16.985119047619047 20.53627232142857  16.946428571428573 20.568638392857142  C 16.569196428571427 20.913876488095234  16.279017857142858 21.17280505952381  16.075892857142858 21.345424107142858  C 15.872767857142856 21.5180431547619  15.580171130952383 21.73921130952381  15.198102678571429 22.00892857142857  C 14.816034226190476 22.278645833333336  14.446056547619047 22.47553943452381  14.088169642857144 22.599609375  C 13.730282738095239 22.72367931547619  13.372395833333336 22.785714285714285  13.014508928571429 22.785714285714285  L 13 22.785714285714285  L 12.985491071428571 22.785714285714285  C 12.62760416666667 22.785714285714285  12.269717261904763 22.72367931547619  11.911830357142858 22.599609375  C 11.553943452380954 22.47553943452381  11.186383928571429 22.278645833333336  10.809151785714286 22.00892857142857  C 10.431919642857142 21.73921130952381  10.136904761904763 21.515345982142858  9.924107142857142 21.337332589285715  C 9.711309523809524 21.15931919642857  9.421130952380954 20.903087797619047  9.053571428571429 20.568638392857142  C 9.005208333333334 20.52548363095238  8.690848214285714 20.25306919642857  8.110491071428571 19.751395089285715  C 7.530133928571428 19.249720982142858  6.836123511904762 18.650948660714285  6.028459821428571 17.955078125  C 5.220796130952381 17.259207589285715  4.628348214285714 16.749441964285715  4.251116071428571 16.42578125  C 4.144717261904762 16.339471726190478  4.084263392857143 16.228887648809522  4.069754464285714 16.094029017857142  C 4.055245535714286 15.959170386904761  4.0866815476190474 15.832403273809524  4.1640625 15.713727678571429  L 4.700892857142857 14.872209821428571  C 4.77827380952381 14.753534226190476  4.882254464285714 14.683407738095235  5.012834821428571 14.66183035714286  C 5.143415178571429 14.640252976190476  5.261904761904762 14.67801339285714  5.368303571428571 14.77511160714286  C 6.277529761904762 15.562686011904761  7.757440476190477 16.835751488095234  9.808035714285714 18.594308035714285  C 9.85639880952381 18.637462797619047  10.066778273809524 18.82626488095238  10.439174107142858 19.160714285714285  C 10.811569940476192 19.49516369047619  11.104166666666668 19.746000744047617  11.316964285714286 19.91322544642857  C 11.529761904761905 20.080450148809522  11.803013392857142 20.255766369047617  12.13671875 20.439174107142858  C 12.470424107142858 20.622581845238095  12.753348214285714 20.714285714285715  12.985491071428571 20.714285714285715  L 13 20.714285714285715  L 13.014508928571429 20.714285714285715  C 13.246651785714288 20.714285714285715  13.529575892857144 20.622581845238095  13.86328125 20.439174107142858  C 14.19698660714286 20.255766369047617  14.470238095238098 20.080450148809522  14.683035714285715 19.91322544642857  C 14.895833333333336 19.746000744047617  15.188430059523812 19.49516369047619  15.560825892857144 19.160714285714285  C 15.933221726190476 18.82626488095238  16.14360119047619 18.637462797619047  16.191964285714285 18.594308035714285  C 18.6875 16.45814732142857  20.201264880952383 15.152715773809524  20.733258928571427 14.67801339285714  C 20.83965773809524 14.591703869047617  20.955729166666668 14.556640625  21.081473214285715 14.572823660714285  Z M 24.005022321428573 26.774832589285715  C 24.096912202380956 26.672340029761905  24.142857142857142 26.550967261904763  24 26.410714285714285  L 24 11.392857142857144  C 23.272321428571427 10.497395833333332  22.503348214285715 9.747581845238095  21.8359375 9.143415178571429  C 20.955729166666668 8.345052083333332  19.074404761904763 6.70517113095238  16.191964285714285 4.22377232142857  C 16.162946428571427 4.202194940476188  15.95498511904762 4.013392857142856  15.568080357142856 3.6573660714285703  C 15.181175595238098 3.301339285714285  14.886160714285715 3.042410714285715  14.683035714285715 2.880580357142856  C 14.479910714285715 2.71875  14.20907738095238 2.543433779761903  13.870535714285715 2.3546316964285703  C 13.531994047619047 2.165829613095238  13.246651785714288 2.0714285714285703  13.014508928571429 2.0714285714285703  L 13 2.0714285714285703  L 12.985491071428571 2.0714285714285703  C 12.753348214285714 2.0714285714285703  12.468005952380954 2.165829613095238  12.129464285714286 2.3546316964285703  C 11.79092261904762 2.543433779761903  11.520089285714288 2.71875  11.316964285714286 2.880580357142856  C 11.113839285714286 3.042410714285715  10.818824404761905 3.301339285714285  10.431919642857142 3.6573660714285703  C 10.045014880952381 4.013392857142856  9.837053571428571 4.202194940476188  9.808035714285714 4.22377232142857  C 7.7284226190476195 6.014694940476188  6.202566964285714 7.33900669642857  5.23046875 8.196707589285715  C 4.258370535714286 9.054408482142856  3.632068452380953 9.61542038690476  3.3515625 9.879743303571429  C 3.071056547619048 10.144066220238095  2.674479166666667 10.545944940476188  2.161830357142857 11.085379464285715  C 2.0264136904761907 11.21484375  1.924851190476191 11.317336309523808  2 11.392857142857144  L 2 26.410714285714285  C 1.857142857142857 26.550967261904763  1.903087797619048 26.672340029761905  1.9949776785714286 26.774832589285715  C 2.0868675595238093 26.877325148809522  2.1956845238095237 26.92857142857143  2.3214285714285716 27  L 23.678571428571427 27  C 23.804315476190474 26.92857142857143  23.91313244047619 26.877325148809522  24.005022321428573 26.774832589285715  Z M 25.405133928571427 9.871651785714285  C 25.80171130952381 10.281622023809524  26 10.788690476190473  26 11.392857142857144  L 26 26.410714285714285  C 26 27.122767857142854  25.772693452380956 27.73232886904762  25.318080357142858 28.23939732142857  C 24.863467261904766 28.746465773809522  24.316964285714292 29  23.678571428571427 29  L 2.3214285714285716 29  C 1.6830357142857144 29  1.1365327380952381 28.746465773809522  0.6819196428571429 28.23939732142857  C 0.22730654761904762 27.73232886904762  0 27.122767857142854  0 26.410714285714285  L 0 11.392857142857144  C 0 10.788690476190473  0.19828869047619047 10.281622023809524  0.5948660714285714 9.871651785714285  C 1.7845982142857144 8.641741071428571  3.477306547619047 7.074683779761902  5.672991071428571 5.170479910714286  C 7.868675595238096 3.2662760416666643  8.995535714285714 2.287202380952379  9.053571428571429 2.2332589285714297  C 9.401785714285714 1.9095982142857149  9.687127976190478 1.653366815476188  9.909598214285714 1.4645647321428559  C 10.132068452380954 1.2757626488095235  10.429501488095237 1.0492001488095235  10.801897321428571 0.7848772321428558  C 11.174293154761905 0.520554315476188  11.541852678571429 0.32366071428571475  11.904575892857142 0.1941964285714295  C 12.26729910714286 0.06473214285714424  12.62760416666667 0  12.985491071428571 0  L 13 0  L 13.014508928571429 0  C 13.372395833333336 0  13.732700892857144 0.06473214285714424  14.095424107142856 0.1941964285714295  C 14.458147321428571 0.32366071428571475  14.825706845238095 0.520554315476188  15.198102678571429 0.7848772321428558  C 15.570498511904763 1.0492001488095235  15.867931547619047 1.2757626488095235  16.090401785714285 1.4645647321428559  C 16.312872023809526 1.653366815476188  16.598214285714285 1.9095982142857149  16.946428571428573 2.2332589285714297  C 17.36235119047619 2.621651785714285  18.116815476190474 3.279761904761903  19.209821428571427 4.207589285714286  C 20.302827380952383 5.135416666666664  21.395833333333336 6.090215773809524  22.488839285714285 7.071986607142856  C 23.58184523809524 8.053757440476188  24.553943452380956 8.986979166666664  25.405133928571427 9.871651785714285  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 1)" stroke="none" transform="matrix(1 0 0 1 -174 -3226 )" class="fill" />
    <path d="M 21.081473214285715 14.572823660714285  C 21.207217261904763 14.589006696428571  21.308779761904763 14.656436011904761  21.386160714285715 14.77511160714286  L 21.952008928571427 15.600446428571429  C 22.029389880952383 15.719122023809524  22.060825892857142 15.845889136904761  22.046316964285715 15.98074776785714  C 22.031808035714285 16.115606398809522  21.976190476190474 16.226190476190478  21.879464285714285 16.3125  C 21.463541666666668 16.679315476190478  20.851748511904763 17.210658482142858  20.044084821428573 17.906529017857142  C 19.236421130952383 18.60239955357143  18.527901785714285 19.21196056547619  17.918526785714285 19.73521205357143  C 17.309151785714285 20.258463541666664  16.985119047619047 20.53627232142857  16.946428571428573 20.568638392857142  C 16.569196428571427 20.913876488095234  16.279017857142858 21.17280505952381  16.075892857142858 21.345424107142858  C 15.872767857142856 21.5180431547619  15.580171130952383 21.73921130952381  15.198102678571429 22.00892857142857  C 14.816034226190476 22.278645833333336  14.446056547619047 22.47553943452381  14.088169642857144 22.599609375  C 13.730282738095239 22.72367931547619  13.372395833333336 22.785714285714285  13.014508928571429 22.785714285714285  L 13 22.785714285714285  L 12.985491071428571 22.785714285714285  C 12.62760416666667 22.785714285714285  12.269717261904763 22.72367931547619  11.911830357142858 22.599609375  C 11.553943452380954 22.47553943452381  11.186383928571429 22.278645833333336  10.809151785714286 22.00892857142857  C 10.431919642857142 21.73921130952381  10.136904761904763 21.515345982142858  9.924107142857142 21.337332589285715  C 9.711309523809524 21.15931919642857  9.421130952380954 20.903087797619047  9.053571428571429 20.568638392857142  C 9.005208333333334 20.52548363095238  8.690848214285714 20.25306919642857  8.110491071428571 19.751395089285715  C 7.530133928571428 19.249720982142858  6.836123511904762 18.650948660714285  6.028459821428571 17.955078125  C 5.220796130952381 17.259207589285715  4.628348214285714 16.749441964285715  4.251116071428571 16.42578125  C 4.144717261904762 16.339471726190478  4.084263392857143 16.228887648809522  4.069754464285714 16.094029017857142  C 4.055245535714286 15.959170386904761  4.0866815476190474 15.832403273809524  4.1640625 15.713727678571429  L 4.700892857142857 14.872209821428571  C 4.77827380952381 14.753534226190476  4.882254464285714 14.683407738095235  5.012834821428571 14.66183035714286  C 5.143415178571429 14.640252976190476  5.261904761904762 14.67801339285714  5.368303571428571 14.77511160714286  C 6.277529761904762 15.562686011904761  7.757440476190477 16.835751488095234  9.808035714285714 18.594308035714285  C 9.85639880952381 18.637462797619047  10.066778273809524 18.82626488095238  10.439174107142858 19.160714285714285  C 10.811569940476192 19.49516369047619  11.104166666666668 19.746000744047617  11.316964285714286 19.91322544642857  C 11.529761904761905 20.080450148809522  11.803013392857142 20.255766369047617  12.13671875 20.439174107142858  C 12.470424107142858 20.622581845238095  12.753348214285714 20.714285714285715  12.985491071428571 20.714285714285715  L 13 20.714285714285715  L 13.014508928571429 20.714285714285715  C 13.246651785714288 20.714285714285715  13.529575892857144 20.622581845238095  13.86328125 20.439174107142858  C 14.19698660714286 20.255766369047617  14.470238095238098 20.080450148809522  14.683035714285715 19.91322544642857  C 14.895833333333336 19.746000744047617  15.188430059523812 19.49516369047619  15.560825892857144 19.160714285714285  C 15.933221726190476 18.82626488095238  16.14360119047619 18.637462797619047  16.191964285714285 18.594308035714285  C 18.6875 16.45814732142857  20.201264880952383 15.152715773809524  20.733258928571427 14.67801339285714  C 20.83965773809524 14.591703869047617  20.955729166666668 14.556640625  21.081473214285715 14.572823660714285  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 -174 -3226 )" class="stroke" mask="url(#u215_img_cl44)" />
    <path d="M 24.005022321428573 26.774832589285715  C 24.096912202380956 26.672340029761905  24.142857142857142 26.550967261904763  24 26.410714285714285  L 24 11.392857142857144  C 23.272321428571427 10.497395833333332  22.503348214285715 9.747581845238095  21.8359375 9.143415178571429  C 20.955729166666668 8.345052083333332  19.074404761904763 6.70517113095238  16.191964285714285 4.22377232142857  C 16.162946428571427 4.202194940476188  15.95498511904762 4.013392857142856  15.568080357142856 3.6573660714285703  C 15.181175595238098 3.301339285714285  14.886160714285715 3.042410714285715  14.683035714285715 2.880580357142856  C 14.479910714285715 2.71875  14.20907738095238 2.543433779761903  13.870535714285715 2.3546316964285703  C 13.531994047619047 2.165829613095238  13.246651785714288 2.0714285714285703  13.014508928571429 2.0714285714285703  L 13 2.0714285714285703  L 12.985491071428571 2.0714285714285703  C 12.753348214285714 2.0714285714285703  12.468005952380954 2.165829613095238  12.129464285714286 2.3546316964285703  C 11.79092261904762 2.543433779761903  11.520089285714288 2.71875  11.316964285714286 2.880580357142856  C 11.113839285714286 3.042410714285715  10.818824404761905 3.301339285714285  10.431919642857142 3.6573660714285703  C 10.045014880952381 4.013392857142856  9.837053571428571 4.202194940476188  9.808035714285714 4.22377232142857  C 7.7284226190476195 6.014694940476188  6.202566964285714 7.33900669642857  5.23046875 8.196707589285715  C 4.258370535714286 9.054408482142856  3.632068452380953 9.61542038690476  3.3515625 9.879743303571429  C 3.071056547619048 10.144066220238095  2.674479166666667 10.545944940476188  2.161830357142857 11.085379464285715  C 2.0264136904761907 11.21484375  1.924851190476191 11.317336309523808  2 11.392857142857144  L 2 26.410714285714285  C 1.857142857142857 26.550967261904763  1.903087797619048 26.672340029761905  1.9949776785714286 26.774832589285715  C 2.0868675595238093 26.877325148809522  2.1956845238095237 26.92857142857143  2.3214285714285716 27  L 23.678571428571427 27  C 23.804315476190474 26.92857142857143  23.91313244047619 26.877325148809522  24.005022321428573 26.774832589285715  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 -174 -3226 )" class="stroke" mask="url(#u215_img_cl44)" />
    <path d="M 25.405133928571427 9.871651785714285  C 25.80171130952381 10.281622023809524  26 10.788690476190473  26 11.392857142857144  L 26 26.410714285714285  C 26 27.122767857142854  25.772693452380956 27.73232886904762  25.318080357142858 28.23939732142857  C 24.863467261904766 28.746465773809522  24.316964285714292 29  23.678571428571427 29  L 2.3214285714285716 29  C 1.6830357142857144 29  1.1365327380952381 28.746465773809522  0.6819196428571429 28.23939732142857  C 0.22730654761904762 27.73232886904762  0 27.122767857142854  0 26.410714285714285  L 0 11.392857142857144  C 0 10.788690476190473  0.19828869047619047 10.281622023809524  0.5948660714285714 9.871651785714285  C 1.7845982142857144 8.641741071428571  3.477306547619047 7.074683779761902  5.672991071428571 5.170479910714286  C 7.868675595238096 3.2662760416666643  8.995535714285714 2.287202380952379  9.053571428571429 2.2332589285714297  C 9.401785714285714 1.9095982142857149  9.687127976190478 1.653366815476188  9.909598214285714 1.4645647321428559  C 10.132068452380954 1.2757626488095235  10.429501488095237 1.0492001488095235  10.801897321428571 0.7848772321428558  C 11.174293154761905 0.520554315476188  11.541852678571429 0.32366071428571475  11.904575892857142 0.1941964285714295  C 12.26729910714286 0.06473214285714424  12.62760416666667 0  12.985491071428571 0  L 13 0  L 13.014508928571429 0  C 13.372395833333336 0  13.732700892857144 0.06473214285714424  14.095424107142856 0.1941964285714295  C 14.458147321428571 0.32366071428571475  14.825706845238095 0.520554315476188  15.198102678571429 0.7848772321428558  C 15.570498511904763 1.0492001488095235  15.867931547619047 1.2757626488095235  16.090401785714285 1.4645647321428559  C 16.312872023809526 1.653366815476188  16.598214285714285 1.9095982142857149  16.946428571428573 2.2332589285714297  C 17.36235119047619 2.621651785714285  18.116815476190474 3.279761904761903  19.209821428571427 4.207589285714286  C 20.302827380952383 5.135416666666664  21.395833333333336 6.090215773809524  22.488839285714285 7.071986607142856  C 23.58184523809524 8.053757440476188  24.553943452380956 8.986979166666664  25.405133928571427 9.871651785714285  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 -174 -3226 )" class="stroke" mask="url(#u215_img_cl44)" />
  </g>
        </svg>
        <div id="u215_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- user (组合) -->
      <div id="u216" class="ax_default" data-label="user" data-left="64" data-top="-3226" data-width="38" data-height="34" layer-opacity="1">

        <!-- Rectangle 4117 (形状) -->
        <div id="u217" class="ax_default _形状 transition notrs" data-label="Rectangle 4117">
          <svg data="images/首页（学生端）/rectangle_4117_u3.svg" id="u217_img" class="img generatedImage">

  <defs>
    <pattern id="u217_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u217_img_cl45">
      <path d="M 0 33.99998813176525  L 1.3264497682875357E-05 0  L 38 0  L 38 34  L 0 33.99998813176525  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -64 3226 )">
    <path d="M 0 33.99998813176525  L 1.3264497682875357E-05 0  L 38 0  L 38 34  L 0 33.99998813176525  Z " fill-rule="nonzero" fill="rgba(196, 196, 196, 0)" stroke="none" transform="matrix(1 0 0 1 64 -3226 )" class="fill" />
    <path d="M 0 33.99998813176525  L 1.3264497682875357E-05 0  L 38 0  L 38 34  L 0 33.99998813176525  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 64 -3226 )" class="stroke" mask="url(#u217_img_cl45)" />
  </g>
          </svg>
          <div id="u217_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Union (形状) -->
        <div id="u218" class="ax_default _形状 transition notrs" data-label="Union">
          <svg data="images/首页（学生端）/union_u4.svg" id="u218_img" class="img generatedImage">

  <defs>
    <pattern id="u218_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u218_img_cl46">
      <path d="M 24.75000117857143 8.571429728571372  C 24.75000117857143 12.713569778571445  21.056357849999998 16.071429728571374  16.500001272857144 16.071429728571374  C 11.943655632857144 16.071429728571374  8.250001272857144 12.713569778571445  8.250001272857144 8.571429728571372  C 8.250001272857144 4.429294521428631  11.943655632857144 1.071429728571372  16.500001272857144 1.071429728571372  C 21.056357849999998 1.071429728571372  24.75000117857143 4.429294521428631  24.75000117857143 8.571429728571372  Z M 30.555976864285714 21.113096785714287  C 31.342904528571427 21.454778571428548  31.82142975 22.188557785714238  31.82142975 22.981355571428583  L 31.82142975 27.85714392857143  C 31.82142975 28.44888685714291  31.293775542857144 28.928572499999998  30.642858321428573 29  L 2.3571441299999996 29  C 1.7062403442857144 28.928572499999998  1.1785727014285716 28.44888685714291  1.1785727014285716 27.85714392857143  L 1.1785727014285716 22.981355571428583  C 1.1785727014285716 22.188557785714238  1.6571103921428572 21.454778571428548  2.444038810714286 21.113096785714287  C 6.716453370000001 19.258027242857167  11.471061784285713 18.214286871428513  16.500001272857144 18.214286871428513  C 21.528952735714284 18.214286871428513  26.28356162142857 19.258027242857167  30.555976864285714 21.113096785714287  Z M 10.60714413 8.571429728571372  C 10.60714413 11.530101085714284  13.245468955714287 13.928572585714228  16.500001272857144 13.928572585714228  C 19.754542287857145 13.928572585714228  22.392858321428573 11.530102092857085  22.392858321428573 8.571429728571372  C 22.392858321428573 5.612762185714278  19.754542287857145 3.2142868714285147  16.500001272857144 3.2142868714285147  C 13.245468955714287 3.2142868714285147  10.60714413 5.612762185714278  10.60714413 8.571429728571372  Z M 29.464286892857146 26.785715357142852  L 29.464286892857146 23.013932571428608  C 25.517627614285715 21.31228135714281  21.135557543571426 20.35714392857143  16.500001272857144 20.35714392857143  C 11.864457070714286 20.35714392857143  7.482388202142858 21.31228135714281  3.535715558571428 23.013932571428608  L 3.535715558571428 26.785715357142852  L 29.464286892857146 26.785715357142852  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -66 3224 )">
    <path d="M 24.75000117857143 8.571429728571372  C 24.75000117857143 12.713569778571445  21.056357849999998 16.071429728571374  16.500001272857144 16.071429728571374  C 11.943655632857144 16.071429728571374  8.250001272857144 12.713569778571445  8.250001272857144 8.571429728571372  C 8.250001272857144 4.429294521428631  11.943655632857144 1.071429728571372  16.500001272857144 1.071429728571372  C 21.056357849999998 1.071429728571372  24.75000117857143 4.429294521428631  24.75000117857143 8.571429728571372  Z M 30.555976864285714 21.113096785714287  C 31.342904528571427 21.454778571428548  31.82142975 22.188557785714238  31.82142975 22.981355571428583  L 31.82142975 27.85714392857143  C 31.82142975 28.44888685714291  31.293775542857144 28.928572499999998  30.642858321428573 29  L 2.3571441299999996 29  C 1.7062403442857144 28.928572499999998  1.1785727014285716 28.44888685714291  1.1785727014285716 27.85714392857143  L 1.1785727014285716 22.981355571428583  C 1.1785727014285716 22.188557785714238  1.6571103921428572 21.454778571428548  2.444038810714286 21.113096785714287  C 6.716453370000001 19.258027242857167  11.471061784285713 18.214286871428513  16.500001272857144 18.214286871428513  C 21.528952735714284 18.214286871428513  26.28356162142857 19.258027242857167  30.555976864285714 21.113096785714287  Z M 10.60714413 8.571429728571372  C 10.60714413 11.530101085714284  13.245468955714287 13.928572585714228  16.500001272857144 13.928572585714228  C 19.754542287857145 13.928572585714228  22.392858321428573 11.530102092857085  22.392858321428573 8.571429728571372  C 22.392858321428573 5.612762185714278  19.754542287857145 3.2142868714285147  16.500001272857144 3.2142868714285147  C 13.245468955714287 3.2142868714285147  10.60714413 5.612762185714278  10.60714413 8.571429728571372  Z M 29.464286892857146 26.785715357142852  L 29.464286892857146 23.013932571428608  C 25.517627614285715 21.31228135714281  21.135557543571426 20.35714392857143  16.500001272857144 20.35714392857143  C 11.864457070714286 20.35714392857143  7.482388202142858 21.31228135714281  3.535715558571428 23.013932571428608  L 3.535715558571428 26.785715357142852  L 29.464286892857146 26.785715357142852  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 0.8980392156862745)" stroke="none" transform="matrix(1 0 0 1 66 -3224 )" class="fill" />
    <path d="M 24.75000117857143 8.571429728571372  C 24.75000117857143 12.713569778571445  21.056357849999998 16.071429728571374  16.500001272857144 16.071429728571374  C 11.943655632857144 16.071429728571374  8.250001272857144 12.713569778571445  8.250001272857144 8.571429728571372  C 8.250001272857144 4.429294521428631  11.943655632857144 1.071429728571372  16.500001272857144 1.071429728571372  C 21.056357849999998 1.071429728571372  24.75000117857143 4.429294521428631  24.75000117857143 8.571429728571372  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 66 -3224 )" class="stroke" mask="url(#u218_img_cl46)" />
    <path d="M 30.555976864285714 21.113096785714287  C 31.342904528571427 21.454778571428548  31.82142975 22.188557785714238  31.82142975 22.981355571428583  L 31.82142975 27.85714392857143  C 31.82142975 28.44888685714291  31.293775542857144 28.928572499999998  30.642858321428573 29  L 2.3571441299999996 29  C 1.7062403442857144 28.928572499999998  1.1785727014285716 28.44888685714291  1.1785727014285716 27.85714392857143  L 1.1785727014285716 22.981355571428583  C 1.1785727014285716 22.188557785714238  1.6571103921428572 21.454778571428548  2.444038810714286 21.113096785714287  C 6.716453370000001 19.258027242857167  11.471061784285713 18.214286871428513  16.500001272857144 18.214286871428513  C 21.528952735714284 18.214286871428513  26.28356162142857 19.258027242857167  30.555976864285714 21.113096785714287  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 66 -3224 )" class="stroke" mask="url(#u218_img_cl46)" />
    <path d="M 10.60714413 8.571429728571372  C 10.60714413 11.530101085714284  13.245468955714287 13.928572585714228  16.500001272857144 13.928572585714228  C 19.754542287857145 13.928572585714228  22.392858321428573 11.530102092857085  22.392858321428573 8.571429728571372  C 22.392858321428573 5.612762185714278  19.754542287857145 3.2142868714285147  16.500001272857144 3.2142868714285147  C 13.245468955714287 3.2142868714285147  10.60714413 5.612762185714278  10.60714413 8.571429728571372  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 66 -3224 )" class="stroke" mask="url(#u218_img_cl46)" />
    <path d="M 29.464286892857146 26.785715357142852  L 29.464286892857146 23.013932571428608  C 25.517627614285715 21.31228135714281  21.135557543571426 20.35714392857143  16.500001272857144 20.35714392857143  C 11.864457070714286 20.35714392857143  7.482388202142858 21.31228135714281  3.535715558571428 23.013932571428608  L 3.535715558571428 26.785715357142852  L 29.464286892857146 26.785715357142852  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 66 -3224 )" class="stroke" mask="url(#u218_img_cl46)" />
  </g>
          </svg>
          <div id="u218_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u219" class="ax_default label transition notrs">
        <div id="u219_div" class=""></div>
        <div id="u219_text" class="text ">
          <p><span>首页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u220" class="ax_default label transition notrs">
        <div id="u220_div" class=""></div>
        <div id="u220_text" class="text ">
          <p><span>我的</span></p>
        </div>
      </div>

      <!-- Unnamed (椭圆) -->
      <div id="u221" class="ax_default ellipse transition notrs">
        <svg data="images/首页（学生端）/u7.svg" id="u221_img" class="img generatedImage">

  <defs>
    <pattern id="u221_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u221_img_cl47">
      <path d="M 0 22  C 0 9.68  9.68 0  22 0  C 34.32 0  44 9.68  44 22  C 44 34.32  34.32 44  22 44  C 9.68 44  0 34.32  0 22  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 53 3226 )">
    <path d="M 0 22  C 0 9.68  9.68 0  22 0  C 34.32 0  44 9.68  44 22  C 44 34.32  34.32 44  22 44  C 9.68 44  0 34.32  0 22  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 -53 -3226 )" class="fill" />
    <path d="M 0 22  C 0 9.68  9.68 0  22 0  C 34.32 0  44 9.68  44 22  C 44 34.32  34.32 44  22 44  C 9.68 44  0 34.32  0 22  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 -53 -3226 )" class="stroke" mask="url(#u221_img_cl47)" />
  </g>
        </svg>
        <div id="u221_text" class="text ">
          <p><span>+</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u222" class="ax_default box_1 transition notrs">
        <div id="u222_div" class=""></div>
        <div id="u222_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u223" class="ax_default label transition notrs">
        <div id="u223_div" class=""></div>
        <div id="u223_text" class="text ">
          <p><span>&lt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u224" class="ax_default label transition notrs">
        <div id="u224_div" class=""></div>
        <div id="u224_text" class="text ">
          <p><span>我的</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u225" class="ax_default _图片 transition notrs">
        <img id="u225_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u225_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u226" class="ax_default" data-left="205" data-top="136" data-width="94" data-height="124" layer-opacity="1">

        <!-- Unnamed (椭圆) -->
        <div id="u227" class="ax_default ellipse transition notrs">
          <svg data="images/我的（学生端）/u227.svg" id="u227_img" class="img generatedImage">

  <defs>
    <pattern id="u227_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u227_img_cl51">
      <path d="M 0 47  C 0 20.679999999999996  20.679999999999996 0  47 0  C 73.32000000000001 0  94 20.679999999999996  94 47  C 94 73.32000000000001  73.32000000000001 94  47 94  C 20.679999999999996 94  0 73.32000000000001  0 47  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -205 -136 )">
    <path d="M 0 47  C 0 20.679999999999996  20.679999999999996 0  47 0  C 73.32000000000001 0  94 20.679999999999996  94 47  C 94 73.32000000000001  73.32000000000001 94  47 94  C 20.679999999999996 94  0 73.32000000000001  0 47  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 205 136 )" class="fill" />
    <path d="M 0 47  C 0 20.679999999999996  20.679999999999996 0  47 0  C 73.32000000000001 0  94 20.679999999999996  94 47  C 94 73.32000000000001  73.32000000000001 94  47 94  C 20.679999999999996 94  0 73.32000000000001  0 47  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 205 136 )" class="stroke" mask="url(#u227_img_cl51)" />
  </g>
          </svg>
          <div id="u227_text" class="text ">
            <p><span>头像</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u228" class="ax_default label transition notrs">
          <div id="u228_div" class=""></div>
          <div id="u228_text" class="text ">
            <p><span>FIGHTING</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u229" class="ax_default" data-left="112" data-top="301" data-width="354" data-height="18" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u230" class="ax_default label transition notrs">
          <div id="u230_div" class=""></div>
          <div id="u230_text" class="text ">
            <p><span>我的订单</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u231" class="ax_default label transition notrs">
          <div id="u231_div" class=""></div>
          <div id="u231_text" class="text ">
            <p><span>&gt;</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u232" class="ax_default line transition notrs">
        <svg data="images/我的（学生端）/u232.svg" id="u232_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -112 -333 )">
    <path d="M 0 0.5  L 361 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 112 333 )" class="stroke" />
  </g>
        </svg>
        <div id="u232_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u233" class="ax_default" data-left="113" data-top="348" data-width="353" data-height="18" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u234" class="ax_default label transition notrs">
          <div id="u234_div" class=""></div>
          <div id="u234_text" class="text ">
            <p><span>在线客服</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u235" class="ax_default label transition notrs">
          <div id="u235_div" class=""></div>
          <div id="u235_text" class="text ">
            <p><span>&gt;</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u236" class="ax_default label transition notrs">
        <div id="u236_div" class=""></div>
        <div id="u236_text" class="text ">
          <p><span>学生角色</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u237" class="ax_default label transition notrs">
        <div id="u237_div" class=""></div>
        <div id="u237_text" class="text ">
          <p><span>认证辅导老师&gt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u238" class="ax_default sticky_4 transition notrs">
        <div id="u238_div" class=""></div>
        <div id="u238_text" class="text ">
          <p><span>后面支持学生昵称和头像可以修改</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
