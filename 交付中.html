﻿<!DOCTYPE html>
<html>
  <head>
    <title>交付中</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/交付中/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/交付中/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u544" class="ax_default box_1 transition notrs">
        <div id="u544_div" class=""></div>
        <div id="u544_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u545" class="ax_default box_1 transition notrs">
        <div id="u545_div" class=""></div>
        <div id="u545_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u546" class="ax_default label transition notrs">
        <div id="u546_div" class=""></div>
        <div id="u546_text" class="text ">
          <p><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">&nbsp; 订单号：</span><span style="font-family:&quot;ArialMT&quot;, &quot;Arial&quot;, sans-serif;">FD13455555333&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2025-3-12 12</span><span style="font-family:&quot;PingFangSC-Regular&quot;, &quot;苹方-简&quot;, sans-serif;">:11:12</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u547" class="ax_default label transition notrs">
        <div id="u547_div" class=""></div>
        <div id="u547_text" class="text ">
          <p><span>&lt; </span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u548" class="ax_default label transition notrs">
        <div id="u548_div" class=""></div>
        <div id="u548_text" class="text ">
          <p><span>我的订单</span></p>
        </div>
      </div>

      <!-- Unnamed (图片) -->
      <div id="u549" class="ax_default _图片 transition notrs">
        <img id="u549_img" class="img " src="images/首页（学生端）/u9.png"/>
        <div id="u549_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u550" class="ax_default sticky_1 transition notrs">
        <div id="u550_div" class=""></div>
        <div id="u550_text" class="text ">
          <p><span><br></span></p><p><span>状态流程</span></p><p><span><br></span></p><p><span>1、竞价中--&gt;已取消</span></p><p><span>2、竞价中--&gt;运输中（已确认竞价）--&gt;待确认--&gt;待评价--&gt;已评价</span></p><p><span><br></span></p><p><span>点击订单跳转到订单详情</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u551" class="ax_default sticky_1 transition notrs">
        <div id="u551_div" class=""></div>
        <div id="u551_text" class="text ">
          <p><span>如果该笔订单已完成报价，则不显示修改报价按钮</span></p><p><span style="color:#D9001B;">如果取消报价，状态改为：取消报价</span></p><p><span style="color:#D9001B;">如果竞价失败，状态改为：竞价失败</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u552" class="ax_default sticky_1 transition notrs">
        <div id="u552_div" class=""></div>
        <div id="u552_text" class="text ">
          <p><span>订单竞价成功后，状态为：运输中</span></p><p><span><br></span></p><p><span>用户点击确认完成后，toast提示：订单确认完成，系统已提示客户及时确认订单</span></p><p><span>同时消息通知客户及时关闭订单</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u553" class="ax_default sticky_1 transition notrs">
        <div id="u553_div" class=""></div>
        <div id="u553_text" class="text ">
          <p><span>商家确认完状态为：用户确认中</span></p><p><span><br></span></p><p><span>商家确认后，每天上午10:00发通知消息</span></p><p><span>如果用户超过7天未进行确认，订单状态自动变更为：待评价</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u554" class="ax_default sticky_1 transition notrs">
        <div id="u554_div" class=""></div>
        <div id="u554_text" class="text ">
          <p><span>用户确认完后状态为：待评价</span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u555" class="ax_default sticky_1 transition notrs">
        <div id="u555_div" class=""></div>
        <div id="u555_text" class="text ">
          <p><span>用户完成评价，状态是：已评价</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u556" class="ax_default sticky_1 transition notrs">
        <div id="u556_div" class=""></div>
        <div id="u556_text" class="text ">
          <p><span>用户完成评价，状态是：已评价</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u557" class="ax_default sticky_1 transition notrs">
        <div id="u557_div" class=""></div>
        <div id="u557_text" class="text ">
          <p><span>用户取消订单后，状态为：订单已取消</span></p>
        </div>
      </div>

      <!-- 提交回复 (动态面板) -->
      <div id="u558" class="ax_default ax_default_hidden" data-label="提交回复" style="display:none; visibility: hidden">
        <div id="u558_state0" class="panel_state" data-label="State1" style="">
          <div id="u558_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u559" class="ax_default box_1 transition notrs">
              <div id="u559_div" class=""></div>
              <div id="u559_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (组合) -->
            <div id="u560" class="ax_default" data-left="41" data-top="14" data-width="89" data-height="39" layer-opacity="1">

              <!-- Unnamed (矩形) -->
              <div id="u561" class="ax_default label transition notrs">
                <div id="u561_div" class=""></div>
                <div id="u561_text" class="text ">
                  <p style="font-size:18px;"><span style="font-size:28px;">&lt; </span><span style="color:#999999;"> </span><span style="color:#CCCCCC;">&nbsp; </span></p>
                </div>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u562" class="ax_default label transition notrs">
              <div id="u562_div" class=""></div>
              <div id="u562_text" class="text ">
                <p><span>评价回复</span></p>
              </div>
            </div>

            <!-- Unnamed (图片) -->
            <div id="u563" class="ax_default _图片 transition notrs">
              <img id="u563_img" class="img " src="images/首页（学生端）/u9.png"/>
              <div id="u563_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u564" class="ax_default label transition notrs">
              <div id="u564_div" class=""></div>
              <div id="u564_text" class="text ">
                <p><span>评价内容：</span><span style="color:#000000;">服务超级好，价格优惠服务超级好，价格优惠</span></p><p><span style="color:#000000;">服务超级好，价格优惠服务超级好，价格优惠服务超级好，</span></p><p><span style="color:#000000;">价格优惠服务超级好，价格优惠。</span></p><p><span>&nbsp;</span></p>
              </div>
            </div>

            <!-- Unnamed (组合) -->
            <div id="u565" class="ax_default" data-left="26" data-top="79" data-width="168" data-height="26" layer-opacity="1">

              <!-- Unnamed (图片) -->
              <div id="u566" class="ax_default _图片 transition notrs">
                <img id="u566_img" class="img " src="images/交付中/u566.png"/>
                <div id="u566_text" class="text " style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>

              <!-- Unnamed (矩形) -->
              <div id="u567" class="ax_default label transition notrs">
                <div id="u567_div" class=""></div>
                <div id="u567_text" class="text ">
                  <p><span>4.5分</span></p>
                </div>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u568" class="ax_default label transition notrs">
              <div id="u568_div" class=""></div>
              <div id="u568_text" class="text ">
                <p><span>我的回复</span></p>
              </div>
            </div>

            <!-- Unnamed (文本域) -->
            <div id="u569" class="ax_default text_area transition notrs">
              <div id="u569_div" class=""></div>
              <textarea id="u569_input" class="u569_input">请谨慎填写我的回复（100字）</textarea>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u570" class="ax_default primary_button transition notrs">
              <div id="u570_div" class=""></div>
              <div id="u570_text" class="text ">
                <p><span>提交回复</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u571" class="ax_default label transition notrs">
              <div id="u571_div" class=""></div>
              <div id="u571_text" class="text ">
                <p><span>提交回复后不可修改，请谨慎填写回复内容</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 报价操作 (动态面板) -->
      <div id="u572" class="ax_default ax_default_hidden" data-label="报价操作" style="display:none; visibility: hidden">
        <div id="u572_state0" class="panel_state" data-label="取消报价" style="">
          <div id="u572_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u573" class="ax_default box_1 transition notrs">
              <div id="u573_div" class=""></div>
              <div id="u573_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u574" class="ax_default label transition notrs">
              <div id="u574_div" class=""></div>
              <div id="u574_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u575" class="ax_default label transition notrs">
              <div id="u575_div" class=""></div>
              <div id="u575_text" class="text ">
                <p><span>取消报价后，报价信息将不会在前台显示。</span></p><p><span>订单信息也会在我的订单中删除。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u576" class="ax_default button transition notrs">
              <div id="u576_div" class=""></div>
              <div id="u576_text" class="text ">
                <p><span>确认取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u577" class="ax_default primary_button transition notrs">
              <div id="u577_div" class=""></div>
              <div id="u577_text" class="text ">
                <p><span>考虑一下</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u572_state1" class="panel_state" data-label="修改报价" style="visibility: hidden;">
          <div id="u572_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u578" class="ax_default box_1 transition notrs">
              <div id="u578_div" class=""></div>
              <div id="u578_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u579" class="ax_default label transition notrs">
              <div id="u579_div" class=""></div>
              <div id="u579_text" class="text ">
                <p><span>提示</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u580" class="ax_default label transition notrs">
              <div id="u580_div" class=""></div>
              <div id="u580_text" class="text ">
                <p><span>本订单的报价为：</span><span style="color:#D9001B;">500元</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u581" class="ax_default button transition notrs">
              <div id="u581_div" class=""></div>
              <div id="u581_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u582" class="ax_default primary_button transition notrs">
              <div id="u582_div" class=""></div>
              <div id="u582_text" class="text ">
                <p><span>确认修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u583" class="ax_default label transition notrs">
              <div id="u583_div" class=""></div>
              <div id="u583_text" class="text ">
                <p><span>修改后的报价为：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u584" class="ax_default text_field transition notrs">
              <div id="u584_div" class=""></div>
              <input id="u584_input" type="text" value="" class="u584_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u585" class="ax_default label transition notrs">
              <div id="u585_div" class=""></div>
              <div id="u585_text" class="text ">
                <p><span>报价越低，成交概率越大哦</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u586" class="ax_default sticky_1 transition notrs">
        <div id="u586_div" class=""></div>
        <div id="u586_text" class="text ">
          <p><span>点击回复跳转到回复页面进行回复</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u587" class="ax_default" data-left="28" data-top="175" data-width="107" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u588" class="ax_default label transition notrs">
          <div id="u588_div" class=""></div>
          <div id="u588_text" class="text ">
            <p><span>辅导项目：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u589" class="ax_default shape transition notrs">
          <div id="u589_div" class=""></div>
          <div id="u589_text" class="text ">
            <p><span>作业</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u590" class="ax_default" data-left="343" data-top="175" data-width="97" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u591" class="ax_default label transition notrs">
          <div id="u591_div" class=""></div>
          <div id="u591_text" class="text ">
            <p><span>总金额：2000元</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u592" class="ax_default" data-left="28" data-top="212" data-width="85" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u593" class="ax_default label transition notrs">
          <div id="u593_div" class=""></div>
          <div id="u593_text" class="text ">
            <p><span>交付阶段：1/5</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u594" class="ax_default" data-left="343" data-top="212" data-width="92" data-height="20" layer-opacity="1">

        <!-- Unnamed (矩形) -->
        <div id="u595" class="ax_default label transition notrs">
          <div id="u595_div" class=""></div>
          <div id="u595_text" class="text ">
            <p><span>辅导老师：张伟</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u596" class="ax_default label transition notrs">
        <div id="u596_div" class=""></div>
        <div id="u596_text" class="text ">
          <p><span>待接单</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u597" class="ax_default label transition notrs">
        <div id="u597_div" class=""></div>
        <div id="u597_text" class="text ">
          <p><span>交付中</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u598" class="ax_default label transition notrs">
        <div id="u598_div" class=""></div>
        <div id="u598_text" class="text ">
          <p><span>待评价</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u599" class="ax_default label transition notrs">
        <div id="u599_div" class=""></div>
        <div id="u599_text" class="text ">
          <p><span>已评价</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u600" class="ax_default line transition notrs">
        <svg data="images/订单列表/u251.svg" id="u600_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -43.5 -110.5 )">
    <path d="M 0 1.5  L 54 1.5  " stroke-width="3" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 45 112 )" class="stroke" />
  </g>
        </svg>
        <div id="u600_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (热区) -->
      <div id="u601" class="ax_default">
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
